/*
* Copyright (C) 2021 Duowan Inc. All rights reserved.
*
* Licensed under the Apache License, Version 2.0 (the "License");
* you may not use this file except in compliance with the License.
* You may obtain a copy of the License at
*
*    http://www.apache.org/licenses/LICENSE-2.0
*
* Unless required by applicable law or agreed to in writing,
* software distributed under the License is distributed on an "AS IS" BASIS,
* WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
* See the License for the specific language governing permissions and
* limitations under the License.
*/


#ifndef __X_L1_L2_EXPAND_H
#define __X_L1_L2_EXPAND_H


// expand macro depend on parameters number
#define X_PACK_COUNT(LEVEL, ACTION,  _99,_98,_97,_96,_95,_94,_93,_92,_91,_90,_89,_88,_87,_86,_85,_84,_83,_82,_81,_80,_79,_78,_77,_76,_75,_74,_73,_72,_71,_70,_69,_68,_67,_66,_65,_64,_63,_62,_61,_60,_59,_58,_57,_56,_55,_54,_53,_52,_51,_50,_49,_48,_47,_46,_45,_44,_43,_42,_41,_40,_39,_38,_37,_36,_35,_34,_33,_32,_31,_30,_29,_28,_27,_26,_25,_24,_23,_22,_21,_20,_19,_18,_17,_16,_15,_14,_13,_12,_11,_10,_9,_8,_7,_6,_5,_4,_3,_2,_1,N,...) LEVEL##N
#define X_PACK_EXPAND(...) __VA_ARGS__


#if !defined(_MSC_VER) || defined(__clang__)

#define X_PACK_N(LEVEL, ACTION, ...)  X_PACK_COUNT(LEVEL, ACTION, __VA_ARGS__, _99,_98,_97,_96,_95,_94,_93,_92,_91,_90,_89,_88,_87,_86,_85,_84,_83,_82,_81,_80,_79,_78,_77,_76,_75,_74,_73,_72,_71,_70,_69,_68,_67,_66,_65,_64,_63,_62,_61,_60,_59,_58,_57,_56,_55,_54,_53,_52,_51,_50,_49,_48,_47,_46,_45,_44,_43,_42,_41,_40,_39,_38,_37,_36,_35,_34,_33,_32,_31,_30,_29,_28,_27,_26,_25,_24,_23,_22,_21,_20,_19,_18,_17,_16,_15,_14,_13,_12,_11,_10,_9,_8,_7,_6,_5,_4,_3,_2,_1) (ACTION, __VA_ARGS__)

#define X_PACK_N2(LEVEL, ACTION, ARG, ...) X_PACK_COUNT(LEVEL, ACTION, __VA_ARGS__, _99,_98,_97,_96,_95,_94,_93,_92,_91,_90,_89,_88,_87,_86,_85,_84,_83,_82,_81,_80,_79,_78,_77,_76,_75,_74,_73,_72,_71,_70,_69,_68,_67,_66,_65,_64,_63,_62,_61,_60,_59,_58,_57,_56,_55,_54,_53,_52,_51,_50,_49,_48,_47,_46,_45,_44,_43,_42,_41,_40,_39,_38,_37,_36,_35,_34,_33,_32,_31,_30,_29,_28,_27,_26,_25,_24,_23,_22,_21,_20,_19,_18,_17,_16,_15,_14,_13,_12,_11,_10,_9,_8,_7,_6,_5,_4,_3,_2,_1) (ACTION, ARG, __VA_ARGS__)

#define X_PACK_L1_DEF(ACT, M)      ACT(M) // here will expand to ACT(O(xxx)), ACT(A(a,x)), ACT(M(xxx))
#define X_PACK_L1_1(ACT, M)        X_PACK_L1_DEF(ACT, M)
#define X_PACK_L1_2(ACT, M,...)    X_PACK_L1_DEF(ACT, M)      X_PACK_L1_1(ACT, __VA_ARGS__)
#define X_PACK_L1_3(ACT, M,...)    X_PACK_L1_DEF(ACT, M)      X_PACK_L1_2(ACT, __VA_ARGS__)
#define X_PACK_L1_4(ACT, M,...)    X_PACK_L1_DEF(ACT, M)      X_PACK_L1_3(ACT, __VA_ARGS__)
#define X_PACK_L1_5(ACT, M,...)    X_PACK_L1_DEF(ACT, M)      X_PACK_L1_4(ACT, __VA_ARGS__)
#define X_PACK_L1_6(ACT, M,...)    X_PACK_L1_DEF(ACT, M)      X_PACK_L1_5(ACT, __VA_ARGS__)
#define X_PACK_L1_7(ACT, M,...)    X_PACK_L1_DEF(ACT, M)      X_PACK_L1_6(ACT, __VA_ARGS__)
#define X_PACK_L1_8(ACT, M,...)    X_PACK_L1_DEF(ACT, M)      X_PACK_L1_7(ACT, __VA_ARGS__)
#define X_PACK_L1_9(ACT, M,...)    X_PACK_L1_DEF(ACT, M)      X_PACK_L1_8(ACT, __VA_ARGS__)
#define X_PACK_L1_10(ACT, M,...)   X_PACK_L1_DEF(ACT, M)      X_PACK_L1_9(ACT, __VA_ARGS__)
#define X_PACK_L1_11(ACT, M,...)   X_PACK_L1_DEF(ACT, M)      X_PACK_L1_10(ACT, __VA_ARGS__)
#define X_PACK_L1_12(ACT, M,...)   X_PACK_L1_DEF(ACT, M)      X_PACK_L1_11(ACT, __VA_ARGS__)
#define X_PACK_L1_13(ACT, M,...)   X_PACK_L1_DEF(ACT, M)      X_PACK_L1_12(ACT, __VA_ARGS__)
#define X_PACK_L1_14(ACT, M,...)   X_PACK_L1_DEF(ACT, M)      X_PACK_L1_13(ACT, __VA_ARGS__)
#define X_PACK_L1_15(ACT, M,...)   X_PACK_L1_DEF(ACT, M)      X_PACK_L1_14(ACT, __VA_ARGS__)
#define X_PACK_L1_16(ACT, M,...)   X_PACK_L1_DEF(ACT, M)      X_PACK_L1_15(ACT, __VA_ARGS__)
#define X_PACK_L1_17(ACT, M,...)   X_PACK_L1_DEF(ACT, M)      X_PACK_L1_16(ACT, __VA_ARGS__)
#define X_PACK_L1_18(ACT, M,...)   X_PACK_L1_DEF(ACT, M)      X_PACK_L1_17(ACT, __VA_ARGS__)
#define X_PACK_L1_19(ACT, M,...)   X_PACK_L1_DEF(ACT, M)      X_PACK_L1_18(ACT, __VA_ARGS__)
#define X_PACK_L1_20(ACT, M,...)   X_PACK_L1_DEF(ACT, M)      X_PACK_L1_19(ACT, __VA_ARGS__)
#define X_PACK_L1_21(ACT, M,...)   X_PACK_L1_DEF(ACT, M)      X_PACK_L1_20(ACT, __VA_ARGS__)
#define X_PACK_L1_22(ACT, M,...)   X_PACK_L1_DEF(ACT, M)      X_PACK_L1_21(ACT, __VA_ARGS__)
#define X_PACK_L1_23(ACT, M,...)   X_PACK_L1_DEF(ACT, M)      X_PACK_L1_22(ACT, __VA_ARGS__)
#define X_PACK_L1_24(ACT, M,...)   X_PACK_L1_DEF(ACT, M)      X_PACK_L1_23(ACT, __VA_ARGS__)
#define X_PACK_L1_25(ACT, M,...)   X_PACK_L1_DEF(ACT, M)      X_PACK_L1_24(ACT, __VA_ARGS__)
#define X_PACK_L1_26(ACT, M,...)   X_PACK_L1_DEF(ACT, M)      X_PACK_L1_25(ACT, __VA_ARGS__)
#define X_PACK_L1_27(ACT, M,...)   X_PACK_L1_DEF(ACT, M)      X_PACK_L1_26(ACT, __VA_ARGS__)
#define X_PACK_L1_28(ACT, M,...)   X_PACK_L1_DEF(ACT, M)      X_PACK_L1_27(ACT, __VA_ARGS__)
#define X_PACK_L1_29(ACT, M,...)   X_PACK_L1_DEF(ACT, M)      X_PACK_L1_28(ACT, __VA_ARGS__)
#define X_PACK_L1_30(ACT, M,...)   X_PACK_L1_DEF(ACT, M)      X_PACK_L1_29(ACT, __VA_ARGS__)
#define X_PACK_L1_31(ACT, M,...)   X_PACK_L1_DEF(ACT, M)      X_PACK_L1_30(ACT, __VA_ARGS__)
#define X_PACK_L1_32(ACT, M,...)   X_PACK_L1_DEF(ACT, M)      X_PACK_L1_31(ACT, __VA_ARGS__)
#define X_PACK_L1_33(ACT, M,...)   X_PACK_L1_DEF(ACT, M)      X_PACK_L1_32(ACT, __VA_ARGS__)
#define X_PACK_L1_34(ACT, M,...)   X_PACK_L1_DEF(ACT, M)      X_PACK_L1_33(ACT, __VA_ARGS__)
#define X_PACK_L1_35(ACT, M,...)   X_PACK_L1_DEF(ACT, M)      X_PACK_L1_34(ACT, __VA_ARGS__)
#define X_PACK_L1_36(ACT, M,...)   X_PACK_L1_DEF(ACT, M)      X_PACK_L1_35(ACT, __VA_ARGS__)
#define X_PACK_L1_37(ACT, M,...)   X_PACK_L1_DEF(ACT, M)      X_PACK_L1_36(ACT, __VA_ARGS__)
#define X_PACK_L1_38(ACT, M,...)   X_PACK_L1_DEF(ACT, M)      X_PACK_L1_37(ACT, __VA_ARGS__)
#define X_PACK_L1_39(ACT, M,...)   X_PACK_L1_DEF(ACT, M)      X_PACK_L1_38(ACT, __VA_ARGS__)
#define X_PACK_L1_40(ACT, M,...)   X_PACK_L1_DEF(ACT, M)      X_PACK_L1_39(ACT, __VA_ARGS__)
#define X_PACK_L1_41(ACT, M,...)   X_PACK_L1_DEF(ACT, M)      X_PACK_L1_40(ACT, __VA_ARGS__)
#define X_PACK_L1_42(ACT, M,...)   X_PACK_L1_DEF(ACT, M)      X_PACK_L1_41(ACT, __VA_ARGS__)
#define X_PACK_L1_43(ACT, M,...)   X_PACK_L1_DEF(ACT, M)      X_PACK_L1_42(ACT, __VA_ARGS__)
#define X_PACK_L1_44(ACT, M,...)   X_PACK_L1_DEF(ACT, M)      X_PACK_L1_43(ACT, __VA_ARGS__)
#define X_PACK_L1_45(ACT, M,...)   X_PACK_L1_DEF(ACT, M)      X_PACK_L1_44(ACT, __VA_ARGS__)
#define X_PACK_L1_46(ACT, M,...)   X_PACK_L1_DEF(ACT, M)      X_PACK_L1_45(ACT, __VA_ARGS__)
#define X_PACK_L1_47(ACT, M,...)   X_PACK_L1_DEF(ACT, M)      X_PACK_L1_46(ACT, __VA_ARGS__)
#define X_PACK_L1_48(ACT, M,...)   X_PACK_L1_DEF(ACT, M)      X_PACK_L1_47(ACT, __VA_ARGS__)
#define X_PACK_L1_49(ACT, M,...)   X_PACK_L1_DEF(ACT, M)      X_PACK_L1_48(ACT, __VA_ARGS__)
#define X_PACK_L1_50(ACT, M,...)   X_PACK_L1_DEF(ACT, M)      X_PACK_L1_49(ACT, __VA_ARGS__)
#define X_PACK_L1_51(ACT, M,...)   X_PACK_L1_DEF(ACT, M)      X_PACK_L1_50(ACT, __VA_ARGS__)
#define X_PACK_L1_52(ACT, M,...)   X_PACK_L1_DEF(ACT, M)      X_PACK_L1_51(ACT, __VA_ARGS__)
#define X_PACK_L1_53(ACT, M,...)   X_PACK_L1_DEF(ACT, M)      X_PACK_L1_52(ACT, __VA_ARGS__)
#define X_PACK_L1_54(ACT, M,...)   X_PACK_L1_DEF(ACT, M)      X_PACK_L1_53(ACT, __VA_ARGS__)
#define X_PACK_L1_55(ACT, M,...)   X_PACK_L1_DEF(ACT, M)      X_PACK_L1_54(ACT, __VA_ARGS__)
#define X_PACK_L1_56(ACT, M,...)   X_PACK_L1_DEF(ACT, M)      X_PACK_L1_55(ACT, __VA_ARGS__)
#define X_PACK_L1_57(ACT, M,...)   X_PACK_L1_DEF(ACT, M)      X_PACK_L1_56(ACT, __VA_ARGS__)
#define X_PACK_L1_58(ACT, M,...)   X_PACK_L1_DEF(ACT, M)      X_PACK_L1_57(ACT, __VA_ARGS__)
#define X_PACK_L1_59(ACT, M,...)   X_PACK_L1_DEF(ACT, M)      X_PACK_L1_58(ACT, __VA_ARGS__)
#define X_PACK_L1_60(ACT, M,...)   X_PACK_L1_DEF(ACT, M)      X_PACK_L1_59(ACT, __VA_ARGS__)
#define X_PACK_L1_61(ACT, M,...)   X_PACK_L1_DEF(ACT, M)      X_PACK_L1_60(ACT, __VA_ARGS__)
#define X_PACK_L1_62(ACT, M,...)   X_PACK_L1_DEF(ACT, M)      X_PACK_L1_61(ACT, __VA_ARGS__)
#define X_PACK_L1_63(ACT, M,...)   X_PACK_L1_DEF(ACT, M)      X_PACK_L1_62(ACT, __VA_ARGS__)
#define X_PACK_L1_64(ACT, M,...)   X_PACK_L1_DEF(ACT, M)      X_PACK_L1_63(ACT, __VA_ARGS__)
#define X_PACK_L1_65(ACT, M,...)   X_PACK_L1_DEF(ACT, M)      X_PACK_L1_64(ACT, __VA_ARGS__)
#define X_PACK_L1_66(ACT, M,...)   X_PACK_L1_DEF(ACT, M)      X_PACK_L1_65(ACT, __VA_ARGS__)
#define X_PACK_L1_67(ACT, M,...)   X_PACK_L1_DEF(ACT, M)      X_PACK_L1_66(ACT, __VA_ARGS__)
#define X_PACK_L1_68(ACT, M,...)   X_PACK_L1_DEF(ACT, M)      X_PACK_L1_67(ACT, __VA_ARGS__)
#define X_PACK_L1_69(ACT, M,...)   X_PACK_L1_DEF(ACT, M)      X_PACK_L1_68(ACT, __VA_ARGS__)
#define X_PACK_L1_70(ACT, M,...)   X_PACK_L1_DEF(ACT, M)      X_PACK_L1_69(ACT, __VA_ARGS__)
#define X_PACK_L1_71(ACT, M,...)   X_PACK_L1_DEF(ACT, M)      X_PACK_L1_70(ACT, __VA_ARGS__)
#define X_PACK_L1_72(ACT, M,...)   X_PACK_L1_DEF(ACT, M)      X_PACK_L1_71(ACT, __VA_ARGS__)
#define X_PACK_L1_73(ACT, M,...)   X_PACK_L1_DEF(ACT, M)      X_PACK_L1_72(ACT, __VA_ARGS__)
#define X_PACK_L1_74(ACT, M,...)   X_PACK_L1_DEF(ACT, M)      X_PACK_L1_73(ACT, __VA_ARGS__)
#define X_PACK_L1_75(ACT, M,...)   X_PACK_L1_DEF(ACT, M)      X_PACK_L1_74(ACT, __VA_ARGS__)
#define X_PACK_L1_76(ACT, M,...)   X_PACK_L1_DEF(ACT, M)      X_PACK_L1_75(ACT, __VA_ARGS__)
#define X_PACK_L1_77(ACT, M,...)   X_PACK_L1_DEF(ACT, M)      X_PACK_L1_76(ACT, __VA_ARGS__)
#define X_PACK_L1_78(ACT, M,...)   X_PACK_L1_DEF(ACT, M)      X_PACK_L1_77(ACT, __VA_ARGS__)
#define X_PACK_L1_79(ACT, M,...)   X_PACK_L1_DEF(ACT, M)      X_PACK_L1_78(ACT, __VA_ARGS__)
#define X_PACK_L1_80(ACT, M,...)   X_PACK_L1_DEF(ACT, M)      X_PACK_L1_79(ACT, __VA_ARGS__)
#define X_PACK_L1_81(ACT, M,...)   X_PACK_L1_DEF(ACT, M)      X_PACK_L1_80(ACT, __VA_ARGS__)
#define X_PACK_L1_82(ACT, M,...)   X_PACK_L1_DEF(ACT, M)      X_PACK_L1_81(ACT, __VA_ARGS__)
#define X_PACK_L1_83(ACT, M,...)   X_PACK_L1_DEF(ACT, M)      X_PACK_L1_82(ACT, __VA_ARGS__)
#define X_PACK_L1_84(ACT, M,...)   X_PACK_L1_DEF(ACT, M)      X_PACK_L1_83(ACT, __VA_ARGS__)
#define X_PACK_L1_85(ACT, M,...)   X_PACK_L1_DEF(ACT, M)      X_PACK_L1_84(ACT, __VA_ARGS__)
#define X_PACK_L1_86(ACT, M,...)   X_PACK_L1_DEF(ACT, M)      X_PACK_L1_85(ACT, __VA_ARGS__)
#define X_PACK_L1_87(ACT, M,...)   X_PACK_L1_DEF(ACT, M)      X_PACK_L1_86(ACT, __VA_ARGS__)
#define X_PACK_L1_88(ACT, M,...)   X_PACK_L1_DEF(ACT, M)      X_PACK_L1_87(ACT, __VA_ARGS__)
#define X_PACK_L1_89(ACT, M,...)   X_PACK_L1_DEF(ACT, M)      X_PACK_L1_88(ACT, __VA_ARGS__)
#define X_PACK_L1_90(ACT, M,...)   X_PACK_L1_DEF(ACT, M)      X_PACK_L1_89(ACT, __VA_ARGS__)
#define X_PACK_L1_91(ACT, M,...)   X_PACK_L1_DEF(ACT, M)      X_PACK_L1_90(ACT, __VA_ARGS__)
#define X_PACK_L1_92(ACT, M,...)   X_PACK_L1_DEF(ACT, M)      X_PACK_L1_91(ACT, __VA_ARGS__)
#define X_PACK_L1_93(ACT, M,...)   X_PACK_L1_DEF(ACT, M)      X_PACK_L1_92(ACT, __VA_ARGS__)
#define X_PACK_L1_94(ACT, M,...)   X_PACK_L1_DEF(ACT, M)      X_PACK_L1_93(ACT, __VA_ARGS__)
#define X_PACK_L1_95(ACT, M,...)   X_PACK_L1_DEF(ACT, M)      X_PACK_L1_94(ACT, __VA_ARGS__)
#define X_PACK_L1_96(ACT, M,...)   X_PACK_L1_DEF(ACT, M)      X_PACK_L1_95(ACT, __VA_ARGS__)
#define X_PACK_L1_97(ACT, M,...)   X_PACK_L1_DEF(ACT, M)      X_PACK_L1_96(ACT, __VA_ARGS__)
#define X_PACK_L1_98(ACT, M,...)   X_PACK_L1_DEF(ACT, M)      X_PACK_L1_97(ACT, __VA_ARGS__)
#define X_PACK_L1_99(ACT, M,...)   X_PACK_L1_DEF(ACT, M)      X_PACK_L1_98(ACT, __VA_ARGS__)

#define X_PACK_L2_DEF(ACT, ARG, M)     ACT(ARG, M)
#define X_PACK_L2_1(ACT, ARG, M)       X_PACK_L2_DEF(ACT, ARG, M)
#define X_PACK_L2_2(ACT, ARG, M,...)   X_PACK_L2_DEF(ACT, ARG, M)     X_PACK_L2_1(ACT, ARG, __VA_ARGS__)
#define X_PACK_L2_3(ACT, ARG, M,...)   X_PACK_L2_DEF(ACT, ARG, M)     X_PACK_L2_2(ACT, ARG, __VA_ARGS__)
#define X_PACK_L2_4(ACT, ARG, M,...)   X_PACK_L2_DEF(ACT, ARG, M)     X_PACK_L2_3(ACT, ARG, __VA_ARGS__)
#define X_PACK_L2_5(ACT, ARG, M,...)   X_PACK_L2_DEF(ACT, ARG, M)     X_PACK_L2_4(ACT, ARG, __VA_ARGS__)
#define X_PACK_L2_6(ACT, ARG, M,...)   X_PACK_L2_DEF(ACT, ARG, M)     X_PACK_L2_5(ACT, ARG, __VA_ARGS__)
#define X_PACK_L2_7(ACT, ARG, M,...)   X_PACK_L2_DEF(ACT, ARG, M)     X_PACK_L2_6(ACT, ARG, __VA_ARGS__)
#define X_PACK_L2_8(ACT, ARG, M,...)   X_PACK_L2_DEF(ACT, ARG, M)     X_PACK_L2_7(ACT, ARG, __VA_ARGS__)
#define X_PACK_L2_9(ACT, ARG, M,...)   X_PACK_L2_DEF(ACT, ARG, M)     X_PACK_L2_8(ACT, ARG, __VA_ARGS__)
#define X_PACK_L2_10(ACT, ARG, M,...)  X_PACK_L2_DEF(ACT, ARG, M)     X_PACK_L2_9(ACT, ARG, __VA_ARGS__)
#define X_PACK_L2_11(ACT, ARG, M,...)  X_PACK_L2_DEF(ACT, ARG, M)     X_PACK_L2_10(ACT, ARG, __VA_ARGS__)
#define X_PACK_L2_12(ACT, ARG, M,...)  X_PACK_L2_DEF(ACT, ARG, M)     X_PACK_L2_11(ACT, ARG, __VA_ARGS__)
#define X_PACK_L2_13(ACT, ARG, M,...)  X_PACK_L2_DEF(ACT, ARG, M)     X_PACK_L2_12(ACT, ARG, __VA_ARGS__)
#define X_PACK_L2_14(ACT, ARG, M,...)  X_PACK_L2_DEF(ACT, ARG, M)     X_PACK_L2_13(ACT, ARG, __VA_ARGS__)
#define X_PACK_L2_15(ACT, ARG, M,...)  X_PACK_L2_DEF(ACT, ARG, M)     X_PACK_L2_14(ACT, ARG, __VA_ARGS__)
#define X_PACK_L2_16(ACT, ARG, M,...)  X_PACK_L2_DEF(ACT, ARG, M)     X_PACK_L2_15(ACT, ARG, __VA_ARGS__)
#define X_PACK_L2_17(ACT, ARG, M,...)  X_PACK_L2_DEF(ACT, ARG, M)     X_PACK_L2_16(ACT, ARG, __VA_ARGS__)
#define X_PACK_L2_18(ACT, ARG, M,...)  X_PACK_L2_DEF(ACT, ARG, M)     X_PACK_L2_17(ACT, ARG, __VA_ARGS__)
#define X_PACK_L2_19(ACT, ARG, M,...)  X_PACK_L2_DEF(ACT, ARG, M)     X_PACK_L2_18(ACT, ARG, __VA_ARGS__)
#define X_PACK_L2_20(ACT, ARG, M,...)  X_PACK_L2_DEF(ACT, ARG, M)     X_PACK_L2_19(ACT, ARG, __VA_ARGS__)
#define X_PACK_L2_21(ACT, ARG, M,...)  X_PACK_L2_DEF(ACT, ARG, M)     X_PACK_L2_20(ACT, ARG, __VA_ARGS__)
#define X_PACK_L2_22(ACT, ARG, M,...)  X_PACK_L2_DEF(ACT, ARG, M)     X_PACK_L2_21(ACT, ARG, __VA_ARGS__)
#define X_PACK_L2_23(ACT, ARG, M,...)  X_PACK_L2_DEF(ACT, ARG, M)     X_PACK_L2_22(ACT, ARG, __VA_ARGS__)
#define X_PACK_L2_24(ACT, ARG, M,...)  X_PACK_L2_DEF(ACT, ARG, M)     X_PACK_L2_23(ACT, ARG, __VA_ARGS__)
#define X_PACK_L2_25(ACT, ARG, M,...)  X_PACK_L2_DEF(ACT, ARG, M)     X_PACK_L2_24(ACT, ARG, __VA_ARGS__)
#define X_PACK_L2_26(ACT, ARG, M,...)  X_PACK_L2_DEF(ACT, ARG, M)     X_PACK_L2_25(ACT, ARG, __VA_ARGS__)
#define X_PACK_L2_27(ACT, ARG, M,...)  X_PACK_L2_DEF(ACT, ARG, M)     X_PACK_L2_26(ACT, ARG, __VA_ARGS__)
#define X_PACK_L2_28(ACT, ARG, M,...)  X_PACK_L2_DEF(ACT, ARG, M)     X_PACK_L2_27(ACT, ARG, __VA_ARGS__)
#define X_PACK_L2_29(ACT, ARG, M,...)  X_PACK_L2_DEF(ACT, ARG, M)     X_PACK_L2_28(ACT, ARG, __VA_ARGS__)
#define X_PACK_L2_30(ACT, ARG, M,...)  X_PACK_L2_DEF(ACT, ARG, M)     X_PACK_L2_29(ACT, ARG, __VA_ARGS__)
#define X_PACK_L2_31(ACT, ARG, M,...)  X_PACK_L2_DEF(ACT, ARG, M)     X_PACK_L2_30(ACT, ARG, __VA_ARGS__)
#define X_PACK_L2_32(ACT, ARG, M,...)  X_PACK_L2_DEF(ACT, ARG, M)     X_PACK_L2_31(ACT, ARG, __VA_ARGS__)
#define X_PACK_L2_33(ACT, ARG, M,...)  X_PACK_L2_DEF(ACT, ARG, M)     X_PACK_L2_32(ACT, ARG, __VA_ARGS__)
#define X_PACK_L2_34(ACT, ARG, M,...)  X_PACK_L2_DEF(ACT, ARG, M)     X_PACK_L2_33(ACT, ARG, __VA_ARGS__)
#define X_PACK_L2_35(ACT, ARG, M,...)  X_PACK_L2_DEF(ACT, ARG, M)     X_PACK_L2_34(ACT, ARG, __VA_ARGS__)
#define X_PACK_L2_36(ACT, ARG, M,...)  X_PACK_L2_DEF(ACT, ARG, M)     X_PACK_L2_35(ACT, ARG, __VA_ARGS__)
#define X_PACK_L2_37(ACT, ARG, M,...)  X_PACK_L2_DEF(ACT, ARG, M)     X_PACK_L2_36(ACT, ARG, __VA_ARGS__)
#define X_PACK_L2_38(ACT, ARG, M,...)  X_PACK_L2_DEF(ACT, ARG, M)     X_PACK_L2_37(ACT, ARG, __VA_ARGS__)
#define X_PACK_L2_39(ACT, ARG, M,...)  X_PACK_L2_DEF(ACT, ARG, M)     X_PACK_L2_38(ACT, ARG, __VA_ARGS__)
#define X_PACK_L2_40(ACT, ARG, M,...)  X_PACK_L2_DEF(ACT, ARG, M)     X_PACK_L2_39(ACT, ARG, __VA_ARGS__)
#define X_PACK_L2_41(ACT, ARG, M,...)  X_PACK_L2_DEF(ACT, ARG, M)     X_PACK_L2_40(ACT, ARG, __VA_ARGS__)
#define X_PACK_L2_42(ACT, ARG, M,...)  X_PACK_L2_DEF(ACT, ARG, M)     X_PACK_L2_41(ACT, ARG, __VA_ARGS__)
#define X_PACK_L2_43(ACT, ARG, M,...)  X_PACK_L2_DEF(ACT, ARG, M)     X_PACK_L2_42(ACT, ARG, __VA_ARGS__)
#define X_PACK_L2_44(ACT, ARG, M,...)  X_PACK_L2_DEF(ACT, ARG, M)     X_PACK_L2_43(ACT, ARG, __VA_ARGS__)
#define X_PACK_L2_45(ACT, ARG, M,...)  X_PACK_L2_DEF(ACT, ARG, M)     X_PACK_L2_44(ACT, ARG, __VA_ARGS__)
#define X_PACK_L2_46(ACT, ARG, M,...)  X_PACK_L2_DEF(ACT, ARG, M)     X_PACK_L2_45(ACT, ARG, __VA_ARGS__)
#define X_PACK_L2_47(ACT, ARG, M,...)  X_PACK_L2_DEF(ACT, ARG, M)     X_PACK_L2_46(ACT, ARG, __VA_ARGS__)
#define X_PACK_L2_48(ACT, ARG, M,...)  X_PACK_L2_DEF(ACT, ARG, M)     X_PACK_L2_47(ACT, ARG, __VA_ARGS__)
#define X_PACK_L2_49(ACT, ARG, M,...)  X_PACK_L2_DEF(ACT, ARG, M)     X_PACK_L2_48(ACT, ARG, __VA_ARGS__)
#define X_PACK_L2_50(ACT, ARG, M,...)  X_PACK_L2_DEF(ACT, ARG, M)     X_PACK_L2_49(ACT, ARG, __VA_ARGS__)
#define X_PACK_L2_51(ACT, ARG, M,...)  X_PACK_L2_DEF(ACT, ARG, M)     X_PACK_L2_50(ACT, ARG, __VA_ARGS__)
#define X_PACK_L2_52(ACT, ARG, M,...)  X_PACK_L2_DEF(ACT, ARG, M)     X_PACK_L2_51(ACT, ARG, __VA_ARGS__)
#define X_PACK_L2_53(ACT, ARG, M,...)  X_PACK_L2_DEF(ACT, ARG, M)     X_PACK_L2_52(ACT, ARG, __VA_ARGS__)
#define X_PACK_L2_54(ACT, ARG, M,...)  X_PACK_L2_DEF(ACT, ARG, M)     X_PACK_L2_53(ACT, ARG, __VA_ARGS__)
#define X_PACK_L2_55(ACT, ARG, M,...)  X_PACK_L2_DEF(ACT, ARG, M)     X_PACK_L2_54(ACT, ARG, __VA_ARGS__)
#define X_PACK_L2_56(ACT, ARG, M,...)  X_PACK_L2_DEF(ACT, ARG, M)     X_PACK_L2_55(ACT, ARG, __VA_ARGS__)
#define X_PACK_L2_57(ACT, ARG, M,...)  X_PACK_L2_DEF(ACT, ARG, M)     X_PACK_L2_56(ACT, ARG, __VA_ARGS__)
#define X_PACK_L2_58(ACT, ARG, M,...)  X_PACK_L2_DEF(ACT, ARG, M)     X_PACK_L2_57(ACT, ARG, __VA_ARGS__)
#define X_PACK_L2_59(ACT, ARG, M,...)  X_PACK_L2_DEF(ACT, ARG, M)     X_PACK_L2_58(ACT, ARG, __VA_ARGS__)
#define X_PACK_L2_60(ACT, ARG, M,...)  X_PACK_L2_DEF(ACT, ARG, M)     X_PACK_L2_59(ACT, ARG, __VA_ARGS__)
#define X_PACK_L2_61(ACT, ARG, M,...)  X_PACK_L2_DEF(ACT, ARG, M)     X_PACK_L2_60(ACT, ARG, __VA_ARGS__)
#define X_PACK_L2_62(ACT, ARG, M,...)  X_PACK_L2_DEF(ACT, ARG, M)     X_PACK_L2_61(ACT, ARG, __VA_ARGS__)
#define X_PACK_L2_63(ACT, ARG, M,...)  X_PACK_L2_DEF(ACT, ARG, M)     X_PACK_L2_62(ACT, ARG, __VA_ARGS__)
#define X_PACK_L2_64(ACT, ARG, M,...)  X_PACK_L2_DEF(ACT, ARG, M)     X_PACK_L2_63(ACT, ARG, __VA_ARGS__)
#define X_PACK_L2_65(ACT, ARG, M,...)  X_PACK_L2_DEF(ACT, ARG, M)     X_PACK_L2_64(ACT, ARG, __VA_ARGS__)
#define X_PACK_L2_66(ACT, ARG, M,...)  X_PACK_L2_DEF(ACT, ARG, M)     X_PACK_L2_65(ACT, ARG, __VA_ARGS__)
#define X_PACK_L2_67(ACT, ARG, M,...)  X_PACK_L2_DEF(ACT, ARG, M)     X_PACK_L2_66(ACT, ARG, __VA_ARGS__)
#define X_PACK_L2_68(ACT, ARG, M,...)  X_PACK_L2_DEF(ACT, ARG, M)     X_PACK_L2_67(ACT, ARG, __VA_ARGS__)
#define X_PACK_L2_69(ACT, ARG, M,...)  X_PACK_L2_DEF(ACT, ARG, M)     X_PACK_L2_68(ACT, ARG, __VA_ARGS__)
#define X_PACK_L2_70(ACT, ARG, M,...)  X_PACK_L2_DEF(ACT, ARG, M)     X_PACK_L2_69(ACT, ARG, __VA_ARGS__)
#define X_PACK_L2_71(ACT, ARG, M,...)  X_PACK_L2_DEF(ACT, ARG, M)     X_PACK_L2_70(ACT, ARG, __VA_ARGS__)
#define X_PACK_L2_72(ACT, ARG, M,...)  X_PACK_L2_DEF(ACT, ARG, M)     X_PACK_L2_71(ACT, ARG, __VA_ARGS__)
#define X_PACK_L2_73(ACT, ARG, M,...)  X_PACK_L2_DEF(ACT, ARG, M)     X_PACK_L2_72(ACT, ARG, __VA_ARGS__)
#define X_PACK_L2_74(ACT, ARG, M,...)  X_PACK_L2_DEF(ACT, ARG, M)     X_PACK_L2_73(ACT, ARG, __VA_ARGS__)
#define X_PACK_L2_75(ACT, ARG, M,...)  X_PACK_L2_DEF(ACT, ARG, M)     X_PACK_L2_74(ACT, ARG, __VA_ARGS__)
#define X_PACK_L2_76(ACT, ARG, M,...)  X_PACK_L2_DEF(ACT, ARG, M)     X_PACK_L2_75(ACT, ARG, __VA_ARGS__)
#define X_PACK_L2_77(ACT, ARG, M,...)  X_PACK_L2_DEF(ACT, ARG, M)     X_PACK_L2_76(ACT, ARG, __VA_ARGS__)
#define X_PACK_L2_78(ACT, ARG, M,...)  X_PACK_L2_DEF(ACT, ARG, M)     X_PACK_L2_77(ACT, ARG, __VA_ARGS__)
#define X_PACK_L2_79(ACT, ARG, M,...)  X_PACK_L2_DEF(ACT, ARG, M)     X_PACK_L2_78(ACT, ARG, __VA_ARGS__)
#define X_PACK_L2_80(ACT, ARG, M,...)  X_PACK_L2_DEF(ACT, ARG, M)     X_PACK_L2_79(ACT, ARG, __VA_ARGS__)
#define X_PACK_L2_81(ACT, ARG, M,...)  X_PACK_L2_DEF(ACT, ARG, M)     X_PACK_L2_80(ACT, ARG, __VA_ARGS__)
#define X_PACK_L2_82(ACT, ARG, M,...)  X_PACK_L2_DEF(ACT, ARG, M)     X_PACK_L2_81(ACT, ARG, __VA_ARGS__)
#define X_PACK_L2_83(ACT, ARG, M,...)  X_PACK_L2_DEF(ACT, ARG, M)     X_PACK_L2_82(ACT, ARG, __VA_ARGS__)
#define X_PACK_L2_84(ACT, ARG, M,...)  X_PACK_L2_DEF(ACT, ARG, M)     X_PACK_L2_83(ACT, ARG, __VA_ARGS__)
#define X_PACK_L2_85(ACT, ARG, M,...)  X_PACK_L2_DEF(ACT, ARG, M)     X_PACK_L2_84(ACT, ARG, __VA_ARGS__)
#define X_PACK_L2_86(ACT, ARG, M,...)  X_PACK_L2_DEF(ACT, ARG, M)     X_PACK_L2_85(ACT, ARG, __VA_ARGS__)
#define X_PACK_L2_87(ACT, ARG, M,...)  X_PACK_L2_DEF(ACT, ARG, M)     X_PACK_L2_86(ACT, ARG, __VA_ARGS__)
#define X_PACK_L2_88(ACT, ARG, M,...)  X_PACK_L2_DEF(ACT, ARG, M)     X_PACK_L2_87(ACT, ARG, __VA_ARGS__)
#define X_PACK_L2_89(ACT, ARG, M,...)  X_PACK_L2_DEF(ACT, ARG, M)     X_PACK_L2_88(ACT, ARG, __VA_ARGS__)
#define X_PACK_L2_90(ACT, ARG, M,...)  X_PACK_L2_DEF(ACT, ARG, M)     X_PACK_L2_89(ACT, ARG, __VA_ARGS__)
#define X_PACK_L2_91(ACT, ARG, M,...)  X_PACK_L2_DEF(ACT, ARG, M)     X_PACK_L2_90(ACT, ARG, __VA_ARGS__)
#define X_PACK_L2_92(ACT, ARG, M,...)  X_PACK_L2_DEF(ACT, ARG, M)     X_PACK_L2_91(ACT, ARG, __VA_ARGS__)
#define X_PACK_L2_93(ACT, ARG, M,...)  X_PACK_L2_DEF(ACT, ARG, M)     X_PACK_L2_92(ACT, ARG, __VA_ARGS__)
#define X_PACK_L2_94(ACT, ARG, M,...)  X_PACK_L2_DEF(ACT, ARG, M)     X_PACK_L2_93(ACT, ARG, __VA_ARGS__)
#define X_PACK_L2_95(ACT, ARG, M,...)  X_PACK_L2_DEF(ACT, ARG, M)     X_PACK_L2_94(ACT, ARG, __VA_ARGS__)
#define X_PACK_L2_96(ACT, ARG, M,...)  X_PACK_L2_DEF(ACT, ARG, M)     X_PACK_L2_95(ACT, ARG, __VA_ARGS__)
#define X_PACK_L2_97(ACT, ARG, M,...)  X_PACK_L2_DEF(ACT, ARG, M)     X_PACK_L2_96(ACT, ARG, __VA_ARGS__)
#define X_PACK_L2_98(ACT, ARG, M,...)  X_PACK_L2_DEF(ACT, ARG, M)     X_PACK_L2_97(ACT, ARG, __VA_ARGS__)
#define X_PACK_L2_99(ACT, ARG, M,...)  X_PACK_L2_DEF(ACT, ARG, M)     X_PACK_L2_98(ACT, ARG, __VA_ARGS__)


// two para every time. for alias
#define X_PACK_L2_2_DEF(ACT, ARG, M, NAME)     ACT(ARG, M, NAME)
#define X_PACK_L2_2_2(ACT, ARG, M, NAME)      X_PACK_L2_2_DEF(ACT, ARG, M, NAME)
#define X_PACK_L2_2_4(ACT, ARG, M, NAME,...)  X_PACK_L2_2_DEF(ACT, ARG, M, NAME)     X_PACK_L2_2_2(ACT,ARG, __VA_ARGS__)
#define X_PACK_L2_2_6(ACT, ARG, M, NAME,...)  X_PACK_L2_2_DEF(ACT, ARG, M, NAME)     X_PACK_L2_2_4(ACT,ARG, __VA_ARGS__)
#define X_PACK_L2_2_8(ACT, ARG, M, NAME,...)  X_PACK_L2_2_DEF(ACT, ARG, M, NAME)     X_PACK_L2_2_6(ACT,ARG, __VA_ARGS__)
#define X_PACK_L2_2_10(ACT, ARG,M, NAME,...)  X_PACK_L2_2_DEF(ACT, ARG, M, NAME)     X_PACK_L2_2_8(ACT,ARG, __VA_ARGS__)
#define X_PACK_L2_2_12(ACT, ARG,M, NAME,...)  X_PACK_L2_2_DEF(ACT, ARG, M, NAME)     X_PACK_L2_2_10(ACT, ARG,__VA_ARGS__)
#define X_PACK_L2_2_14(ACT, ARG,M, NAME,...)  X_PACK_L2_2_DEF(ACT, ARG, M, NAME)     X_PACK_L2_2_12(ACT, ARG,__VA_ARGS__)
#define X_PACK_L2_2_16(ACT, ARG,M, NAME,...)  X_PACK_L2_2_DEF(ACT, ARG, M, NAME)     X_PACK_L2_2_14(ACT, ARG,__VA_ARGS__)
#define X_PACK_L2_2_18(ACT, ARG,M, NAME,...)  X_PACK_L2_2_DEF(ACT, ARG, M, NAME)     X_PACK_L2_2_16(ACT, ARG,__VA_ARGS__)
#define X_PACK_L2_2_20(ACT, ARG,M, NAME,...)  X_PACK_L2_2_DEF(ACT, ARG, M, NAME)     X_PACK_L2_2_18(ACT, ARG,__VA_ARGS__)
#define X_PACK_L2_2_22(ACT, ARG,M, NAME,...)  X_PACK_L2_2_DEF(ACT, ARG, M, NAME)     X_PACK_L2_2_20(ACT, ARG,__VA_ARGS__)
#define X_PACK_L2_2_24(ACT, ARG,M, NAME,...)  X_PACK_L2_2_DEF(ACT, ARG, M, NAME)     X_PACK_L2_2_22(ACT, ARG,__VA_ARGS__)
#define X_PACK_L2_2_26(ACT, ARG,M, NAME,...)  X_PACK_L2_2_DEF(ACT, ARG, M, NAME)     X_PACK_L2_2_24(ACT, ARG,__VA_ARGS__)
#define X_PACK_L2_2_28(ACT, ARG,M, NAME,...)  X_PACK_L2_2_DEF(ACT, ARG, M, NAME)     X_PACK_L2_2_26(ACT, ARG,__VA_ARGS__)
#define X_PACK_L2_2_30(ACT, ARG,M, NAME,...)  X_PACK_L2_2_DEF(ACT, ARG, M, NAME)     X_PACK_L2_2_28(ACT, ARG,__VA_ARGS__)
#define X_PACK_L2_2_32(ACT, ARG,M, NAME,...)  X_PACK_L2_2_DEF(ACT, ARG, M, NAME)     X_PACK_L2_2_30(ACT, ARG,__VA_ARGS__)
#define X_PACK_L2_2_34(ACT, ARG,M, NAME,...)  X_PACK_L2_2_DEF(ACT, ARG, M, NAME)     X_PACK_L2_2_32(ACT, ARG,__VA_ARGS__)
#define X_PACK_L2_2_36(ACT, ARG,M, NAME,...)  X_PACK_L2_2_DEF(ACT, ARG, M, NAME)     X_PACK_L2_2_34(ACT, ARG,__VA_ARGS__)
#define X_PACK_L2_2_38(ACT, ARG,M, NAME,...)  X_PACK_L2_2_DEF(ACT, ARG, M, NAME)     X_PACK_L2_2_36(ACT, ARG,__VA_ARGS__)
#define X_PACK_L2_2_40(ACT, ARG,M, NAME,...)  X_PACK_L2_2_DEF(ACT, ARG, M, NAME)     X_PACK_L2_2_38(ACT, ARG,__VA_ARGS__)
#define X_PACK_L2_2_42(ACT, ARG,M, NAME,...)  X_PACK_L2_2_DEF(ACT, ARG, M, NAME)     X_PACK_L2_2_40(ACT, ARG,__VA_ARGS__)
#define X_PACK_L2_2_44(ACT, ARG,M, NAME,...)  X_PACK_L2_2_DEF(ACT, ARG, M, NAME)     X_PACK_L2_2_42(ACT, ARG,__VA_ARGS__)
#define X_PACK_L2_2_46(ACT, ARG,M, NAME,...)  X_PACK_L2_2_DEF(ACT, ARG, M, NAME)     X_PACK_L2_2_44(ACT, ARG,__VA_ARGS__)
#define X_PACK_L2_2_48(ACT, ARG,M, NAME,...)  X_PACK_L2_2_DEF(ACT, ARG, M, NAME)     X_PACK_L2_2_46(ACT, ARG,__VA_ARGS__)
#define X_PACK_L2_2_50(ACT, ARG,M, NAME,...)  X_PACK_L2_2_DEF(ACT, ARG, M, NAME)     X_PACK_L2_2_48(ACT, ARG,__VA_ARGS__)

#else // msvc
// thx https://stackoverflow.com/questions/5134523/msvc-doesnt-expand-va-args-correctly
// in MSVC's preprocessor, __VA_ARGS__ is treat as a normal parameter, so it will expand at last, and in gcc, it's expand at first. so we need expand it first
#define X_PACK_N(LEVEL, ACTION, ...)  X_PACK_EXPAND(X_PACK_COUNT(LEVEL, ACTION, __VA_ARGS__,_99,_98,_97,_96,_95,_94,_93,_92,_91,_90,_89,_88,_87,_86,_85,_84,_83,_82,_81,_80,_79,_78,_77,_76,_75,_74,_73,_72,_71,_70,_69,_68,_67,_66,_65,_64,_63,_62,_61,_60,_59,_58,_57,_56,_55,_54,_53,_52,_51,_50,_49,_48,_47,_46,_45,_44,_43,_42,_41,_40,_39,_38,_37,_36,_35,_34,_33,_32,_31,_30,_29,_28,_27,_26,_25,_24,_23,_22,_21,_20,_19,_18,_17,_16,_15,_14,_13,_12,_11,_10,_9,_8,_7,_6,_5,_4,_3,_2,_1)) X_PACK_EXPAND((ACTION, __VA_ARGS__))

#define X_PACK_N2(LEVEL, ACTION, ARG, ...) X_PACK_EXPAND(X_PACK_COUNT(LEVEL, ACTION, __VA_ARGS__,_99,_98,_97,_96,_95,_94,_93,_92,_91,_90,_89,_88,_87,_86,_85,_84,_83,_82,_81,_80,_79,_78,_77,_76,_75,_74,_73,_72,_71,_70,_69,_68,_67,_66,_65,_64,_63,_62,_61,_60,_59,_58,_57,_56,_55,_54,_53,_52,_51,_50,_49,_48,_47,_46,_45,_44,_43,_42,_41,_40,_39,_38,_37,_36,_35,_34,_33,_32,_31,_30,_29,_28,_27,_26,_25,_24,_23,_22,_21,_20,_19,_18,_17,_16,_15,_14,_13,_12,_11,_10,_9,_8,_7,_6,_5,_4,_3,_2,_1)) X_PACK_EXPAND((ACTION, ARG, __VA_ARGS__))

#define X_PACK_L1_DEF(ACT, M)      ACT(M) // here will expand to ACT(O(xxx)), ACT(A(a,x)), ACT(M(xxx))
#define X_PACK_L1_1(ACT, M)        X_PACK_L1_DEF(ACT, M)
#define X_PACK_L1_2(ACT, M,...)    X_PACK_L1_DEF(ACT, M)      X_PACK_L1_1 X_PACK_EXPAND((ACT, __VA_ARGS__))
#define X_PACK_L1_3(ACT, M,...)    X_PACK_L1_DEF(ACT, M)      X_PACK_L1_2 X_PACK_EXPAND((ACT, __VA_ARGS__))
#define X_PACK_L1_4(ACT, M,...)    X_PACK_L1_DEF(ACT, M)      X_PACK_L1_3 X_PACK_EXPAND((ACT, __VA_ARGS__))
#define X_PACK_L1_5(ACT, M,...)    X_PACK_L1_DEF(ACT, M)      X_PACK_L1_4 X_PACK_EXPAND((ACT, __VA_ARGS__))
#define X_PACK_L1_6(ACT, M,...)    X_PACK_L1_DEF(ACT, M)      X_PACK_L1_5 X_PACK_EXPAND((ACT, __VA_ARGS__))
#define X_PACK_L1_7(ACT, M,...)    X_PACK_L1_DEF(ACT, M)      X_PACK_L1_6 X_PACK_EXPAND((ACT, __VA_ARGS__))
#define X_PACK_L1_8(ACT, M,...)    X_PACK_L1_DEF(ACT, M)      X_PACK_L1_7 X_PACK_EXPAND((ACT, __VA_ARGS__))
#define X_PACK_L1_9(ACT, M,...)    X_PACK_L1_DEF(ACT, M)      X_PACK_L1_8 X_PACK_EXPAND((ACT, __VA_ARGS__))
#define X_PACK_L1_10(ACT, M,...)   X_PACK_L1_DEF(ACT, M)      X_PACK_L1_9 X_PACK_EXPAND((ACT, __VA_ARGS__))
#define X_PACK_L1_11(ACT, M,...)   X_PACK_L1_DEF(ACT, M)      X_PACK_L1_10 X_PACK_EXPAND((ACT, __VA_ARGS__))
#define X_PACK_L1_12(ACT, M,...)   X_PACK_L1_DEF(ACT, M)      X_PACK_L1_11 X_PACK_EXPAND((ACT, __VA_ARGS__))
#define X_PACK_L1_13(ACT, M,...)   X_PACK_L1_DEF(ACT, M)      X_PACK_L1_12 X_PACK_EXPAND((ACT, __VA_ARGS__))
#define X_PACK_L1_14(ACT, M,...)   X_PACK_L1_DEF(ACT, M)      X_PACK_L1_13 X_PACK_EXPAND((ACT, __VA_ARGS__))
#define X_PACK_L1_15(ACT, M,...)   X_PACK_L1_DEF(ACT, M)      X_PACK_L1_14 X_PACK_EXPAND((ACT, __VA_ARGS__))
#define X_PACK_L1_16(ACT, M,...)   X_PACK_L1_DEF(ACT, M)      X_PACK_L1_15 X_PACK_EXPAND((ACT, __VA_ARGS__))
#define X_PACK_L1_17(ACT, M,...)   X_PACK_L1_DEF(ACT, M)      X_PACK_L1_16 X_PACK_EXPAND((ACT, __VA_ARGS__))
#define X_PACK_L1_18(ACT, M,...)   X_PACK_L1_DEF(ACT, M)      X_PACK_L1_17 X_PACK_EXPAND((ACT, __VA_ARGS__))
#define X_PACK_L1_19(ACT, M,...)   X_PACK_L1_DEF(ACT, M)      X_PACK_L1_18 X_PACK_EXPAND((ACT, __VA_ARGS__))
#define X_PACK_L1_20(ACT, M,...)   X_PACK_L1_DEF(ACT, M)      X_PACK_L1_19 X_PACK_EXPAND((ACT, __VA_ARGS__))
#define X_PACK_L1_21(ACT, M,...)   X_PACK_L1_DEF(ACT, M)      X_PACK_L1_20 X_PACK_EXPAND((ACT, __VA_ARGS__))
#define X_PACK_L1_22(ACT, M,...)   X_PACK_L1_DEF(ACT, M)      X_PACK_L1_21 X_PACK_EXPAND((ACT, __VA_ARGS__))
#define X_PACK_L1_23(ACT, M,...)   X_PACK_L1_DEF(ACT, M)      X_PACK_L1_22 X_PACK_EXPAND((ACT, __VA_ARGS__))
#define X_PACK_L1_24(ACT, M,...)   X_PACK_L1_DEF(ACT, M)      X_PACK_L1_23 X_PACK_EXPAND((ACT, __VA_ARGS__))
#define X_PACK_L1_25(ACT, M,...)   X_PACK_L1_DEF(ACT, M)      X_PACK_L1_24 X_PACK_EXPAND((ACT, __VA_ARGS__))
#define X_PACK_L1_26(ACT, M,...)   X_PACK_L1_DEF(ACT, M)      X_PACK_L1_25 X_PACK_EXPAND((ACT, __VA_ARGS__))
#define X_PACK_L1_27(ACT, M,...)   X_PACK_L1_DEF(ACT, M)      X_PACK_L1_26 X_PACK_EXPAND((ACT, __VA_ARGS__))
#define X_PACK_L1_28(ACT, M,...)   X_PACK_L1_DEF(ACT, M)      X_PACK_L1_27 X_PACK_EXPAND((ACT, __VA_ARGS__))
#define X_PACK_L1_29(ACT, M,...)   X_PACK_L1_DEF(ACT, M)      X_PACK_L1_28 X_PACK_EXPAND((ACT, __VA_ARGS__))
#define X_PACK_L1_30(ACT, M,...)   X_PACK_L1_DEF(ACT, M)      X_PACK_L1_29 X_PACK_EXPAND((ACT, __VA_ARGS__))
#define X_PACK_L1_31(ACT, M,...)   X_PACK_L1_DEF(ACT, M)      X_PACK_L1_30 X_PACK_EXPAND((ACT, __VA_ARGS__))
#define X_PACK_L1_32(ACT, M,...)   X_PACK_L1_DEF(ACT, M)      X_PACK_L1_31 X_PACK_EXPAND((ACT, __VA_ARGS__))
#define X_PACK_L1_33(ACT, M,...)   X_PACK_L1_DEF(ACT, M)      X_PACK_L1_32 X_PACK_EXPAND((ACT, __VA_ARGS__))
#define X_PACK_L1_34(ACT, M,...)   X_PACK_L1_DEF(ACT, M)      X_PACK_L1_33 X_PACK_EXPAND((ACT, __VA_ARGS__))
#define X_PACK_L1_35(ACT, M,...)   X_PACK_L1_DEF(ACT, M)      X_PACK_L1_34 X_PACK_EXPAND((ACT, __VA_ARGS__))
#define X_PACK_L1_36(ACT, M,...)   X_PACK_L1_DEF(ACT, M)      X_PACK_L1_35 X_PACK_EXPAND((ACT, __VA_ARGS__))
#define X_PACK_L1_37(ACT, M,...)   X_PACK_L1_DEF(ACT, M)      X_PACK_L1_36 X_PACK_EXPAND((ACT, __VA_ARGS__))
#define X_PACK_L1_38(ACT, M,...)   X_PACK_L1_DEF(ACT, M)      X_PACK_L1_37 X_PACK_EXPAND((ACT, __VA_ARGS__))
#define X_PACK_L1_39(ACT, M,...)   X_PACK_L1_DEF(ACT, M)      X_PACK_L1_38 X_PACK_EXPAND((ACT, __VA_ARGS__))
#define X_PACK_L1_40(ACT, M,...)   X_PACK_L1_DEF(ACT, M)      X_PACK_L1_39 X_PACK_EXPAND((ACT, __VA_ARGS__))
#define X_PACK_L1_41(ACT, M,...)   X_PACK_L1_DEF(ACT, M)      X_PACK_L1_40 X_PACK_EXPAND((ACT, __VA_ARGS__))
#define X_PACK_L1_42(ACT, M,...)   X_PACK_L1_DEF(ACT, M)      X_PACK_L1_41 X_PACK_EXPAND((ACT, __VA_ARGS__))
#define X_PACK_L1_43(ACT, M,...)   X_PACK_L1_DEF(ACT, M)      X_PACK_L1_42 X_PACK_EXPAND((ACT, __VA_ARGS__))
#define X_PACK_L1_44(ACT, M,...)   X_PACK_L1_DEF(ACT, M)      X_PACK_L1_43 X_PACK_EXPAND((ACT, __VA_ARGS__))
#define X_PACK_L1_45(ACT, M,...)   X_PACK_L1_DEF(ACT, M)      X_PACK_L1_44 X_PACK_EXPAND((ACT, __VA_ARGS__))
#define X_PACK_L1_46(ACT, M,...)   X_PACK_L1_DEF(ACT, M)      X_PACK_L1_45 X_PACK_EXPAND((ACT, __VA_ARGS__))
#define X_PACK_L1_47(ACT, M,...)   X_PACK_L1_DEF(ACT, M)      X_PACK_L1_46 X_PACK_EXPAND((ACT, __VA_ARGS__))
#define X_PACK_L1_48(ACT, M,...)   X_PACK_L1_DEF(ACT, M)      X_PACK_L1_47 X_PACK_EXPAND((ACT, __VA_ARGS__))
#define X_PACK_L1_49(ACT, M,...)   X_PACK_L1_DEF(ACT, M)      X_PACK_L1_48 X_PACK_EXPAND((ACT, __VA_ARGS__))
#define X_PACK_L1_50(ACT, M,...)   X_PACK_L1_DEF(ACT, M)      X_PACK_L1_49 X_PACK_EXPAND((ACT, __VA_ARGS__))
#define X_PACK_L1_51(ACT, M,...)   X_PACK_L1_DEF(ACT, M)      X_PACK_L1_50 X_PACK_EXPAND((ACT, __VA_ARGS__))
#define X_PACK_L1_52(ACT, M,...)   X_PACK_L1_DEF(ACT, M)      X_PACK_L1_51 X_PACK_EXPAND((ACT, __VA_ARGS__))
#define X_PACK_L1_53(ACT, M,...)   X_PACK_L1_DEF(ACT, M)      X_PACK_L1_52 X_PACK_EXPAND((ACT, __VA_ARGS__))
#define X_PACK_L1_54(ACT, M,...)   X_PACK_L1_DEF(ACT, M)      X_PACK_L1_53 X_PACK_EXPAND((ACT, __VA_ARGS__))
#define X_PACK_L1_55(ACT, M,...)   X_PACK_L1_DEF(ACT, M)      X_PACK_L1_54 X_PACK_EXPAND((ACT, __VA_ARGS__))
#define X_PACK_L1_56(ACT, M,...)   X_PACK_L1_DEF(ACT, M)      X_PACK_L1_55 X_PACK_EXPAND((ACT, __VA_ARGS__))
#define X_PACK_L1_57(ACT, M,...)   X_PACK_L1_DEF(ACT, M)      X_PACK_L1_56 X_PACK_EXPAND((ACT, __VA_ARGS__))
#define X_PACK_L1_58(ACT, M,...)   X_PACK_L1_DEF(ACT, M)      X_PACK_L1_57 X_PACK_EXPAND((ACT, __VA_ARGS__))
#define X_PACK_L1_59(ACT, M,...)   X_PACK_L1_DEF(ACT, M)      X_PACK_L1_58 X_PACK_EXPAND((ACT, __VA_ARGS__))
#define X_PACK_L1_60(ACT, M,...)   X_PACK_L1_DEF(ACT, M)      X_PACK_L1_59 X_PACK_EXPAND((ACT, __VA_ARGS__))
#define X_PACK_L1_61(ACT, M,...)   X_PACK_L1_DEF(ACT, M)      X_PACK_L1_60 X_PACK_EXPAND((ACT, __VA_ARGS__))
#define X_PACK_L1_62(ACT, M,...)   X_PACK_L1_DEF(ACT, M)      X_PACK_L1_61 X_PACK_EXPAND((ACT, __VA_ARGS__))
#define X_PACK_L1_63(ACT, M,...)   X_PACK_L1_DEF(ACT, M)      X_PACK_L1_62 X_PACK_EXPAND((ACT, __VA_ARGS__))
#define X_PACK_L1_64(ACT, M,...)   X_PACK_L1_DEF(ACT, M)      X_PACK_L1_63 X_PACK_EXPAND((ACT, __VA_ARGS__))
#define X_PACK_L1_65(ACT, M,...)   X_PACK_L1_DEF(ACT, M)      X_PACK_L1_64 X_PACK_EXPAND((ACT, __VA_ARGS__))
#define X_PACK_L1_66(ACT, M,...)   X_PACK_L1_DEF(ACT, M)      X_PACK_L1_65 X_PACK_EXPAND((ACT, __VA_ARGS__))
#define X_PACK_L1_67(ACT, M,...)   X_PACK_L1_DEF(ACT, M)      X_PACK_L1_66 X_PACK_EXPAND((ACT, __VA_ARGS__))
#define X_PACK_L1_68(ACT, M,...)   X_PACK_L1_DEF(ACT, M)      X_PACK_L1_67 X_PACK_EXPAND((ACT, __VA_ARGS__))
#define X_PACK_L1_69(ACT, M,...)   X_PACK_L1_DEF(ACT, M)      X_PACK_L1_68 X_PACK_EXPAND((ACT, __VA_ARGS__))
#define X_PACK_L1_70(ACT, M,...)   X_PACK_L1_DEF(ACT, M)      X_PACK_L1_69 X_PACK_EXPAND((ACT, __VA_ARGS__))
#define X_PACK_L1_71(ACT, M,...)   X_PACK_L1_DEF(ACT, M)      X_PACK_L1_70 X_PACK_EXPAND((ACT, __VA_ARGS__))
#define X_PACK_L1_72(ACT, M,...)   X_PACK_L1_DEF(ACT, M)      X_PACK_L1_71 X_PACK_EXPAND((ACT, __VA_ARGS__))
#define X_PACK_L1_73(ACT, M,...)   X_PACK_L1_DEF(ACT, M)      X_PACK_L1_72 X_PACK_EXPAND((ACT, __VA_ARGS__))
#define X_PACK_L1_74(ACT, M,...)   X_PACK_L1_DEF(ACT, M)      X_PACK_L1_73 X_PACK_EXPAND((ACT, __VA_ARGS__))
#define X_PACK_L1_75(ACT, M,...)   X_PACK_L1_DEF(ACT, M)      X_PACK_L1_74 X_PACK_EXPAND((ACT, __VA_ARGS__))
#define X_PACK_L1_76(ACT, M,...)   X_PACK_L1_DEF(ACT, M)      X_PACK_L1_75 X_PACK_EXPAND((ACT, __VA_ARGS__))
#define X_PACK_L1_77(ACT, M,...)   X_PACK_L1_DEF(ACT, M)      X_PACK_L1_76 X_PACK_EXPAND((ACT, __VA_ARGS__))
#define X_PACK_L1_78(ACT, M,...)   X_PACK_L1_DEF(ACT, M)      X_PACK_L1_77 X_PACK_EXPAND((ACT, __VA_ARGS__))
#define X_PACK_L1_79(ACT, M,...)   X_PACK_L1_DEF(ACT, M)      X_PACK_L1_78 X_PACK_EXPAND((ACT, __VA_ARGS__))
#define X_PACK_L1_80(ACT, M,...)   X_PACK_L1_DEF(ACT, M)      X_PACK_L1_79 X_PACK_EXPAND((ACT, __VA_ARGS__))
#define X_PACK_L1_81(ACT, M,...)   X_PACK_L1_DEF(ACT, M)      X_PACK_L1_80 X_PACK_EXPAND((ACT, __VA_ARGS__))
#define X_PACK_L1_82(ACT, M,...)   X_PACK_L1_DEF(ACT, M)      X_PACK_L1_81 X_PACK_EXPAND((ACT, __VA_ARGS__))
#define X_PACK_L1_83(ACT, M,...)   X_PACK_L1_DEF(ACT, M)      X_PACK_L1_82 X_PACK_EXPAND((ACT, __VA_ARGS__))
#define X_PACK_L1_84(ACT, M,...)   X_PACK_L1_DEF(ACT, M)      X_PACK_L1_83 X_PACK_EXPAND((ACT, __VA_ARGS__))
#define X_PACK_L1_85(ACT, M,...)   X_PACK_L1_DEF(ACT, M)      X_PACK_L1_84 X_PACK_EXPAND((ACT, __VA_ARGS__))
#define X_PACK_L1_86(ACT, M,...)   X_PACK_L1_DEF(ACT, M)      X_PACK_L1_85 X_PACK_EXPAND((ACT, __VA_ARGS__))
#define X_PACK_L1_87(ACT, M,...)   X_PACK_L1_DEF(ACT, M)      X_PACK_L1_86 X_PACK_EXPAND((ACT, __VA_ARGS__))
#define X_PACK_L1_88(ACT, M,...)   X_PACK_L1_DEF(ACT, M)      X_PACK_L1_87 X_PACK_EXPAND((ACT, __VA_ARGS__))
#define X_PACK_L1_89(ACT, M,...)   X_PACK_L1_DEF(ACT, M)      X_PACK_L1_88 X_PACK_EXPAND((ACT, __VA_ARGS__))
#define X_PACK_L1_90(ACT, M,...)   X_PACK_L1_DEF(ACT, M)      X_PACK_L1_89 X_PACK_EXPAND((ACT, __VA_ARGS__))
#define X_PACK_L1_91(ACT, M,...)   X_PACK_L1_DEF(ACT, M)      X_PACK_L1_90 X_PACK_EXPAND((ACT, __VA_ARGS__))
#define X_PACK_L1_92(ACT, M,...)   X_PACK_L1_DEF(ACT, M)      X_PACK_L1_91 X_PACK_EXPAND((ACT, __VA_ARGS__))
#define X_PACK_L1_93(ACT, M,...)   X_PACK_L1_DEF(ACT, M)      X_PACK_L1_92 X_PACK_EXPAND((ACT, __VA_ARGS__))
#define X_PACK_L1_94(ACT, M,...)   X_PACK_L1_DEF(ACT, M)      X_PACK_L1_93 X_PACK_EXPAND((ACT, __VA_ARGS__))
#define X_PACK_L1_95(ACT, M,...)   X_PACK_L1_DEF(ACT, M)      X_PACK_L1_94 X_PACK_EXPAND((ACT, __VA_ARGS__))
#define X_PACK_L1_96(ACT, M,...)   X_PACK_L1_DEF(ACT, M)      X_PACK_L1_95 X_PACK_EXPAND((ACT, __VA_ARGS__))
#define X_PACK_L1_97(ACT, M,...)   X_PACK_L1_DEF(ACT, M)      X_PACK_L1_96 X_PACK_EXPAND((ACT, __VA_ARGS__))
#define X_PACK_L1_98(ACT, M,...)   X_PACK_L1_DEF(ACT, M)      X_PACK_L1_97 X_PACK_EXPAND((ACT, __VA_ARGS__))
#define X_PACK_L1_99(ACT, M,...)   X_PACK_L1_DEF(ACT, M)      X_PACK_L1_98 X_PACK_EXPAND((ACT, __VA_ARGS__))

#define X_PACK_L2_DEF(ACT, ARG, M)     ACT(ARG, M)
#define X_PACK_L2_1(ACT, ARG, M)       X_PACK_L2_DEF(ACT, ARG, M)
#define X_PACK_L2_2(ACT, ARG, M,...)   X_PACK_L2_DEF(ACT, ARG, M)     X_PACK_L2_1 X_PACK_EXPAND((ACT, ARG, __VA_ARGS__))
#define X_PACK_L2_3(ACT, ARG, M,...)   X_PACK_L2_DEF(ACT, ARG, M)     X_PACK_L2_2 X_PACK_EXPAND((ACT, ARG, __VA_ARGS__))
#define X_PACK_L2_4(ACT, ARG, M,...)   X_PACK_L2_DEF(ACT, ARG, M)     X_PACK_L2_3 X_PACK_EXPAND((ACT, ARG, __VA_ARGS__))
#define X_PACK_L2_5(ACT, ARG, M,...)   X_PACK_L2_DEF(ACT, ARG, M)     X_PACK_L2_4 X_PACK_EXPAND((ACT, ARG, __VA_ARGS__))
#define X_PACK_L2_6(ACT, ARG, M,...)   X_PACK_L2_DEF(ACT, ARG, M)     X_PACK_L2_5 X_PACK_EXPAND((ACT, ARG, __VA_ARGS__))
#define X_PACK_L2_7(ACT, ARG, M,...)   X_PACK_L2_DEF(ACT, ARG, M)     X_PACK_L2_6 X_PACK_EXPAND((ACT, ARG, __VA_ARGS__))
#define X_PACK_L2_8(ACT, ARG, M,...)   X_PACK_L2_DEF(ACT, ARG, M)     X_PACK_L2_7 X_PACK_EXPAND((ACT, ARG, __VA_ARGS__))
#define X_PACK_L2_9(ACT, ARG, M,...)   X_PACK_L2_DEF(ACT, ARG, M)     X_PACK_L2_8 X_PACK_EXPAND((ACT, ARG, __VA_ARGS__))
#define X_PACK_L2_10(ACT, ARG, M,...)  X_PACK_L2_DEF(ACT, ARG, M)     X_PACK_L2_9 X_PACK_EXPAND((ACT, ARG, __VA_ARGS__))
#define X_PACK_L2_11(ACT, ARG, M,...)  X_PACK_L2_DEF(ACT, ARG, M)     X_PACK_L2_10 X_PACK_EXPAND((ACT, ARG, __VA_ARGS__))
#define X_PACK_L2_12(ACT, ARG, M,...)  X_PACK_L2_DEF(ACT, ARG, M)     X_PACK_L2_11 X_PACK_EXPAND((ACT, ARG, __VA_ARGS__))
#define X_PACK_L2_13(ACT, ARG, M,...)  X_PACK_L2_DEF(ACT, ARG, M)     X_PACK_L2_12 X_PACK_EXPAND((ACT, ARG, __VA_ARGS__))
#define X_PACK_L2_14(ACT, ARG, M,...)  X_PACK_L2_DEF(ACT, ARG, M)     X_PACK_L2_13 X_PACK_EXPAND((ACT, ARG, __VA_ARGS__))
#define X_PACK_L2_15(ACT, ARG, M,...)  X_PACK_L2_DEF(ACT, ARG, M)     X_PACK_L2_14 X_PACK_EXPAND((ACT, ARG, __VA_ARGS__))
#define X_PACK_L2_16(ACT, ARG, M,...)  X_PACK_L2_DEF(ACT, ARG, M)     X_PACK_L2_15 X_PACK_EXPAND((ACT, ARG, __VA_ARGS__))
#define X_PACK_L2_17(ACT, ARG, M,...)  X_PACK_L2_DEF(ACT, ARG, M)     X_PACK_L2_16 X_PACK_EXPAND((ACT, ARG, __VA_ARGS__))
#define X_PACK_L2_18(ACT, ARG, M,...)  X_PACK_L2_DEF(ACT, ARG, M)     X_PACK_L2_17 X_PACK_EXPAND((ACT, ARG, __VA_ARGS__))
#define X_PACK_L2_19(ACT, ARG, M,...)  X_PACK_L2_DEF(ACT, ARG, M)     X_PACK_L2_18 X_PACK_EXPAND((ACT, ARG, __VA_ARGS__))
#define X_PACK_L2_20(ACT, ARG, M,...)  X_PACK_L2_DEF(ACT, ARG, M)     X_PACK_L2_19 X_PACK_EXPAND((ACT, ARG, __VA_ARGS__))
#define X_PACK_L2_21(ACT, ARG, M,...)  X_PACK_L2_DEF(ACT, ARG, M)     X_PACK_L2_20 X_PACK_EXPAND((ACT, ARG, __VA_ARGS__))
#define X_PACK_L2_22(ACT, ARG, M,...)  X_PACK_L2_DEF(ACT, ARG, M)     X_PACK_L2_21 X_PACK_EXPAND((ACT, ARG, __VA_ARGS__))
#define X_PACK_L2_23(ACT, ARG, M,...)  X_PACK_L2_DEF(ACT, ARG, M)     X_PACK_L2_22 X_PACK_EXPAND((ACT, ARG, __VA_ARGS__))
#define X_PACK_L2_24(ACT, ARG, M,...)  X_PACK_L2_DEF(ACT, ARG, M)     X_PACK_L2_23 X_PACK_EXPAND((ACT, ARG, __VA_ARGS__))
#define X_PACK_L2_25(ACT, ARG, M,...)  X_PACK_L2_DEF(ACT, ARG, M)     X_PACK_L2_24 X_PACK_EXPAND((ACT, ARG, __VA_ARGS__))
#define X_PACK_L2_26(ACT, ARG, M,...)  X_PACK_L2_DEF(ACT, ARG, M)     X_PACK_L2_25 X_PACK_EXPAND((ACT, ARG, __VA_ARGS__))
#define X_PACK_L2_27(ACT, ARG, M,...)  X_PACK_L2_DEF(ACT, ARG, M)     X_PACK_L2_26 X_PACK_EXPAND((ACT, ARG, __VA_ARGS__))
#define X_PACK_L2_28(ACT, ARG, M,...)  X_PACK_L2_DEF(ACT, ARG, M)     X_PACK_L2_27 X_PACK_EXPAND((ACT, ARG, __VA_ARGS__))
#define X_PACK_L2_29(ACT, ARG, M,...)  X_PACK_L2_DEF(ACT, ARG, M)     X_PACK_L2_28 X_PACK_EXPAND((ACT, ARG, __VA_ARGS__))
#define X_PACK_L2_30(ACT, ARG, M,...)  X_PACK_L2_DEF(ACT, ARG, M)     X_PACK_L2_29 X_PACK_EXPAND((ACT, ARG, __VA_ARGS__))
#define X_PACK_L2_31(ACT, ARG, M,...)  X_PACK_L2_DEF(ACT, ARG, M)     X_PACK_L2_30 X_PACK_EXPAND((ACT, ARG, __VA_ARGS__))
#define X_PACK_L2_32(ACT, ARG, M,...)  X_PACK_L2_DEF(ACT, ARG, M)     X_PACK_L2_31 X_PACK_EXPAND((ACT, ARG, __VA_ARGS__))
#define X_PACK_L2_33(ACT, ARG, M,...)  X_PACK_L2_DEF(ACT, ARG, M)     X_PACK_L2_32 X_PACK_EXPAND((ACT, ARG, __VA_ARGS__))
#define X_PACK_L2_34(ACT, ARG, M,...)  X_PACK_L2_DEF(ACT, ARG, M)     X_PACK_L2_33 X_PACK_EXPAND((ACT, ARG, __VA_ARGS__))
#define X_PACK_L2_35(ACT, ARG, M,...)  X_PACK_L2_DEF(ACT, ARG, M)     X_PACK_L2_34 X_PACK_EXPAND((ACT, ARG, __VA_ARGS__))
#define X_PACK_L2_36(ACT, ARG, M,...)  X_PACK_L2_DEF(ACT, ARG, M)     X_PACK_L2_35 X_PACK_EXPAND((ACT, ARG, __VA_ARGS__))
#define X_PACK_L2_37(ACT, ARG, M,...)  X_PACK_L2_DEF(ACT, ARG, M)     X_PACK_L2_36 X_PACK_EXPAND((ACT, ARG, __VA_ARGS__))
#define X_PACK_L2_38(ACT, ARG, M,...)  X_PACK_L2_DEF(ACT, ARG, M)     X_PACK_L2_37 X_PACK_EXPAND((ACT, ARG, __VA_ARGS__))
#define X_PACK_L2_39(ACT, ARG, M,...)  X_PACK_L2_DEF(ACT, ARG, M)     X_PACK_L2_38 X_PACK_EXPAND((ACT, ARG, __VA_ARGS__))
#define X_PACK_L2_40(ACT, ARG, M,...)  X_PACK_L2_DEF(ACT, ARG, M)     X_PACK_L2_39 X_PACK_EXPAND((ACT, ARG, __VA_ARGS__))
#define X_PACK_L2_41(ACT, ARG, M,...)  X_PACK_L2_DEF(ACT, ARG, M)     X_PACK_L2_40 X_PACK_EXPAND((ACT, ARG, __VA_ARGS__))
#define X_PACK_L2_42(ACT, ARG, M,...)  X_PACK_L2_DEF(ACT, ARG, M)     X_PACK_L2_41 X_PACK_EXPAND((ACT, ARG, __VA_ARGS__))
#define X_PACK_L2_43(ACT, ARG, M,...)  X_PACK_L2_DEF(ACT, ARG, M)     X_PACK_L2_42 X_PACK_EXPAND((ACT, ARG, __VA_ARGS__))
#define X_PACK_L2_44(ACT, ARG, M,...)  X_PACK_L2_DEF(ACT, ARG, M)     X_PACK_L2_43 X_PACK_EXPAND((ACT, ARG, __VA_ARGS__))
#define X_PACK_L2_45(ACT, ARG, M,...)  X_PACK_L2_DEF(ACT, ARG, M)     X_PACK_L2_44 X_PACK_EXPAND((ACT, ARG, __VA_ARGS__))
#define X_PACK_L2_46(ACT, ARG, M,...)  X_PACK_L2_DEF(ACT, ARG, M)     X_PACK_L2_45 X_PACK_EXPAND((ACT, ARG, __VA_ARGS__))
#define X_PACK_L2_47(ACT, ARG, M,...)  X_PACK_L2_DEF(ACT, ARG, M)     X_PACK_L2_46 X_PACK_EXPAND((ACT, ARG, __VA_ARGS__))
#define X_PACK_L2_48(ACT, ARG, M,...)  X_PACK_L2_DEF(ACT, ARG, M)     X_PACK_L2_47 X_PACK_EXPAND((ACT, ARG, __VA_ARGS__))
#define X_PACK_L2_49(ACT, ARG, M,...)  X_PACK_L2_DEF(ACT, ARG, M)     X_PACK_L2_48 X_PACK_EXPAND((ACT, ARG, __VA_ARGS__))
#define X_PACK_L2_50(ACT, ARG, M,...)  X_PACK_L2_DEF(ACT, ARG, M)     X_PACK_L2_49 X_PACK_EXPAND((ACT, ARG, __VA_ARGS__))
#define X_PACK_L2_51(ACT, ARG, M,...)  X_PACK_L2_DEF(ACT, ARG, M)     X_PACK_L2_50 X_PACK_EXPAND((ACT, ARG, __VA_ARGS__))
#define X_PACK_L2_52(ACT, ARG, M,...)  X_PACK_L2_DEF(ACT, ARG, M)     X_PACK_L2_51 X_PACK_EXPAND((ACT, ARG, __VA_ARGS__))
#define X_PACK_L2_53(ACT, ARG, M,...)  X_PACK_L2_DEF(ACT, ARG, M)     X_PACK_L2_52 X_PACK_EXPAND((ACT, ARG, __VA_ARGS__))
#define X_PACK_L2_54(ACT, ARG, M,...)  X_PACK_L2_DEF(ACT, ARG, M)     X_PACK_L2_53 X_PACK_EXPAND((ACT, ARG, __VA_ARGS__))
#define X_PACK_L2_55(ACT, ARG, M,...)  X_PACK_L2_DEF(ACT, ARG, M)     X_PACK_L2_54 X_PACK_EXPAND((ACT, ARG, __VA_ARGS__))
#define X_PACK_L2_56(ACT, ARG, M,...)  X_PACK_L2_DEF(ACT, ARG, M)     X_PACK_L2_55 X_PACK_EXPAND((ACT, ARG, __VA_ARGS__))
#define X_PACK_L2_57(ACT, ARG, M,...)  X_PACK_L2_DEF(ACT, ARG, M)     X_PACK_L2_56 X_PACK_EXPAND((ACT, ARG, __VA_ARGS__))
#define X_PACK_L2_58(ACT, ARG, M,...)  X_PACK_L2_DEF(ACT, ARG, M)     X_PACK_L2_57 X_PACK_EXPAND((ACT, ARG, __VA_ARGS__))
#define X_PACK_L2_59(ACT, ARG, M,...)  X_PACK_L2_DEF(ACT, ARG, M)     X_PACK_L2_58 X_PACK_EXPAND((ACT, ARG, __VA_ARGS__))
#define X_PACK_L2_60(ACT, ARG, M,...)  X_PACK_L2_DEF(ACT, ARG, M)     X_PACK_L2_59 X_PACK_EXPAND((ACT, ARG, __VA_ARGS__))
#define X_PACK_L2_61(ACT, ARG, M,...)  X_PACK_L2_DEF(ACT, ARG, M)     X_PACK_L2_60 X_PACK_EXPAND((ACT, ARG, __VA_ARGS__))
#define X_PACK_L2_62(ACT, ARG, M,...)  X_PACK_L2_DEF(ACT, ARG, M)     X_PACK_L2_61 X_PACK_EXPAND((ACT, ARG, __VA_ARGS__))
#define X_PACK_L2_63(ACT, ARG, M,...)  X_PACK_L2_DEF(ACT, ARG, M)     X_PACK_L2_62 X_PACK_EXPAND((ACT, ARG, __VA_ARGS__))
#define X_PACK_L2_64(ACT, ARG, M,...)  X_PACK_L2_DEF(ACT, ARG, M)     X_PACK_L2_63 X_PACK_EXPAND((ACT, ARG, __VA_ARGS__))
#define X_PACK_L2_65(ACT, ARG, M,...)  X_PACK_L2_DEF(ACT, ARG, M)     X_PACK_L2_64 X_PACK_EXPAND((ACT, ARG, __VA_ARGS__))
#define X_PACK_L2_66(ACT, ARG, M,...)  X_PACK_L2_DEF(ACT, ARG, M)     X_PACK_L2_65 X_PACK_EXPAND((ACT, ARG, __VA_ARGS__))
#define X_PACK_L2_67(ACT, ARG, M,...)  X_PACK_L2_DEF(ACT, ARG, M)     X_PACK_L2_66 X_PACK_EXPAND((ACT, ARG, __VA_ARGS__))
#define X_PACK_L2_68(ACT, ARG, M,...)  X_PACK_L2_DEF(ACT, ARG, M)     X_PACK_L2_67 X_PACK_EXPAND((ACT, ARG, __VA_ARGS__))
#define X_PACK_L2_69(ACT, ARG, M,...)  X_PACK_L2_DEF(ACT, ARG, M)     X_PACK_L2_68 X_PACK_EXPAND((ACT, ARG, __VA_ARGS__))
#define X_PACK_L2_70(ACT, ARG, M,...)  X_PACK_L2_DEF(ACT, ARG, M)     X_PACK_L2_69 X_PACK_EXPAND((ACT, ARG, __VA_ARGS__))
#define X_PACK_L2_71(ACT, ARG, M,...)  X_PACK_L2_DEF(ACT, ARG, M)     X_PACK_L2_70 X_PACK_EXPAND((ACT, ARG, __VA_ARGS__))
#define X_PACK_L2_72(ACT, ARG, M,...)  X_PACK_L2_DEF(ACT, ARG, M)     X_PACK_L2_71 X_PACK_EXPAND((ACT, ARG, __VA_ARGS__))
#define X_PACK_L2_73(ACT, ARG, M,...)  X_PACK_L2_DEF(ACT, ARG, M)     X_PACK_L2_72 X_PACK_EXPAND((ACT, ARG, __VA_ARGS__))
#define X_PACK_L2_74(ACT, ARG, M,...)  X_PACK_L2_DEF(ACT, ARG, M)     X_PACK_L2_73 X_PACK_EXPAND((ACT, ARG, __VA_ARGS__))
#define X_PACK_L2_75(ACT, ARG, M,...)  X_PACK_L2_DEF(ACT, ARG, M)     X_PACK_L2_74 X_PACK_EXPAND((ACT, ARG, __VA_ARGS__))
#define X_PACK_L2_76(ACT, ARG, M,...)  X_PACK_L2_DEF(ACT, ARG, M)     X_PACK_L2_75 X_PACK_EXPAND((ACT, ARG, __VA_ARGS__))
#define X_PACK_L2_77(ACT, ARG, M,...)  X_PACK_L2_DEF(ACT, ARG, M)     X_PACK_L2_76 X_PACK_EXPAND((ACT, ARG, __VA_ARGS__))
#define X_PACK_L2_78(ACT, ARG, M,...)  X_PACK_L2_DEF(ACT, ARG, M)     X_PACK_L2_77 X_PACK_EXPAND((ACT, ARG, __VA_ARGS__))
#define X_PACK_L2_79(ACT, ARG, M,...)  X_PACK_L2_DEF(ACT, ARG, M)     X_PACK_L2_78 X_PACK_EXPAND((ACT, ARG, __VA_ARGS__))
#define X_PACK_L2_80(ACT, ARG, M,...)  X_PACK_L2_DEF(ACT, ARG, M)     X_PACK_L2_79 X_PACK_EXPAND((ACT, ARG, __VA_ARGS__))
#define X_PACK_L2_81(ACT, ARG, M,...)  X_PACK_L2_DEF(ACT, ARG, M)     X_PACK_L2_80 X_PACK_EXPAND((ACT, ARG, __VA_ARGS__))
#define X_PACK_L2_82(ACT, ARG, M,...)  X_PACK_L2_DEF(ACT, ARG, M)     X_PACK_L2_81 X_PACK_EXPAND((ACT, ARG, __VA_ARGS__))
#define X_PACK_L2_83(ACT, ARG, M,...)  X_PACK_L2_DEF(ACT, ARG, M)     X_PACK_L2_82 X_PACK_EXPAND((ACT, ARG, __VA_ARGS__))
#define X_PACK_L2_84(ACT, ARG, M,...)  X_PACK_L2_DEF(ACT, ARG, M)     X_PACK_L2_83 X_PACK_EXPAND((ACT, ARG, __VA_ARGS__))
#define X_PACK_L2_85(ACT, ARG, M,...)  X_PACK_L2_DEF(ACT, ARG, M)     X_PACK_L2_84 X_PACK_EXPAND((ACT, ARG, __VA_ARGS__))
#define X_PACK_L2_86(ACT, ARG, M,...)  X_PACK_L2_DEF(ACT, ARG, M)     X_PACK_L2_85 X_PACK_EXPAND((ACT, ARG, __VA_ARGS__))
#define X_PACK_L2_87(ACT, ARG, M,...)  X_PACK_L2_DEF(ACT, ARG, M)     X_PACK_L2_86 X_PACK_EXPAND((ACT, ARG, __VA_ARGS__))
#define X_PACK_L2_88(ACT, ARG, M,...)  X_PACK_L2_DEF(ACT, ARG, M)     X_PACK_L2_87 X_PACK_EXPAND((ACT, ARG, __VA_ARGS__))
#define X_PACK_L2_89(ACT, ARG, M,...)  X_PACK_L2_DEF(ACT, ARG, M)     X_PACK_L2_88 X_PACK_EXPAND((ACT, ARG, __VA_ARGS__))
#define X_PACK_L2_90(ACT, ARG, M,...)  X_PACK_L2_DEF(ACT, ARG, M)     X_PACK_L2_89 X_PACK_EXPAND((ACT, ARG, __VA_ARGS__))
#define X_PACK_L2_91(ACT, ARG, M,...)  X_PACK_L2_DEF(ACT, ARG, M)     X_PACK_L2_90 X_PACK_EXPAND((ACT, ARG, __VA_ARGS__))
#define X_PACK_L2_92(ACT, ARG, M,...)  X_PACK_L2_DEF(ACT, ARG, M)     X_PACK_L2_91 X_PACK_EXPAND((ACT, ARG, __VA_ARGS__))
#define X_PACK_L2_93(ACT, ARG, M,...)  X_PACK_L2_DEF(ACT, ARG, M)     X_PACK_L2_92 X_PACK_EXPAND((ACT, ARG, __VA_ARGS__))
#define X_PACK_L2_94(ACT, ARG, M,...)  X_PACK_L2_DEF(ACT, ARG, M)     X_PACK_L2_93 X_PACK_EXPAND((ACT, ARG, __VA_ARGS__))
#define X_PACK_L2_95(ACT, ARG, M,...)  X_PACK_L2_DEF(ACT, ARG, M)     X_PACK_L2_94 X_PACK_EXPAND((ACT, ARG, __VA_ARGS__))
#define X_PACK_L2_96(ACT, ARG, M,...)  X_PACK_L2_DEF(ACT, ARG, M)     X_PACK_L2_95 X_PACK_EXPAND((ACT, ARG, __VA_ARGS__))
#define X_PACK_L2_97(ACT, ARG, M,...)  X_PACK_L2_DEF(ACT, ARG, M)     X_PACK_L2_96 X_PACK_EXPAND((ACT, ARG, __VA_ARGS__))
#define X_PACK_L2_98(ACT, ARG, M,...)  X_PACK_L2_DEF(ACT, ARG, M)     X_PACK_L2_97 X_PACK_EXPAND((ACT, ARG, __VA_ARGS__))
#define X_PACK_L2_99(ACT, ARG, M,...)  X_PACK_L2_DEF(ACT, ARG, M)     X_PACK_L2_98 X_PACK_EXPAND((ACT, ARG, __VA_ARGS__))

// two para every time. for alias
#define X_PACK_L2_2_DEF(ACT, ARG, M, NAME)     ACT(ARG, M, NAME)
#define X_PACK_L2_2_2(ACT, ARG, M, NAME)       X_PACK_L2_2_DEF(ACT, ARG, M, NAME)
#define X_PACK_L2_2_4(ACT, ARG, M, NAME,...)   X_PACK_L2_2_DEF(ACT, ARG, M, NAME)     X_PACK_L2_2_2 X_PACK_EXPAND((ACT, ARG, __VA_ARGS__))
#define X_PACK_L2_2_6(ACT, ARG, M, NAME,...)   X_PACK_L2_2_DEF(ACT, ARG, M, NAME)     X_PACK_L2_2_4 X_PACK_EXPAND((ACT, ARG, __VA_ARGS__))
#define X_PACK_L2_2_8(ACT, ARG, M, NAME,...)   X_PACK_L2_2_DEF(ACT, ARG, M, NAME)     X_PACK_L2_2_6 X_PACK_EXPAND((ACT, ARG, __VA_ARGS__))
#define X_PACK_L2_2_10(ACT, ARG, M, NAME,...)  X_PACK_L2_2_DEF(ACT, ARG, M, NAME)     X_PACK_L2_2_8 X_PACK_EXPAND((ACT, ARG, __VA_ARGS__))
#define X_PACK_L2_2_12(ACT, ARG, M, NAME,...)  X_PACK_L2_2_DEF(ACT, ARG, M, NAME)     X_PACK_L2_2_10 X_PACK_EXPAND((ACT, ARG, __VA_ARGS__))
#define X_PACK_L2_2_14(ACT, ARG, M, NAME,...)  X_PACK_L2_2_DEF(ACT, ARG, M, NAME)     X_PACK_L2_2_12 X_PACK_EXPAND((ACT, ARG, __VA_ARGS__))
#define X_PACK_L2_2_16(ACT, ARG, M, NAME,...)  X_PACK_L2_2_DEF(ACT, ARG, M, NAME)     X_PACK_L2_2_14 X_PACK_EXPAND((ACT, ARG, __VA_ARGS__))
#define X_PACK_L2_2_18(ACT, ARG, M, NAME,...)  X_PACK_L2_2_DEF(ACT, ARG, M, NAME)     X_PACK_L2_2_16 X_PACK_EXPAND((ACT, ARG, __VA_ARGS__))
#define X_PACK_L2_2_20(ACT, ARG, M, NAME,...)  X_PACK_L2_2_DEF(ACT, ARG, M, NAME)     X_PACK_L2_2_18 X_PACK_EXPAND((ACT, ARG, __VA_ARGS__))
#define X_PACK_L2_2_22(ACT, ARG, M, NAME,...)  X_PACK_L2_2_DEF(ACT, ARG, M, NAME)     X_PACK_L2_2_20 X_PACK_EXPAND((ACT, ARG, __VA_ARGS__))
#define X_PACK_L2_2_24(ACT, ARG, M, NAME,...)  X_PACK_L2_2_DEF(ACT, ARG, M, NAME)     X_PACK_L2_2_22 X_PACK_EXPAND((ACT, ARG, __VA_ARGS__))
#define X_PACK_L2_2_26(ACT, ARG, M, NAME,...)  X_PACK_L2_2_DEF(ACT, ARG, M, NAME)     X_PACK_L2_2_24 X_PACK_EXPAND((ACT, ARG, __VA_ARGS__))
#define X_PACK_L2_2_28(ACT, ARG, M, NAME,...)  X_PACK_L2_2_DEF(ACT, ARG, M, NAME)     X_PACK_L2_2_26 X_PACK_EXPAND((ACT, ARG, __VA_ARGS__))
#define X_PACK_L2_2_30(ACT, ARG, M, NAME,...)  X_PACK_L2_2_DEF(ACT, ARG, M, NAME)     X_PACK_L2_2_28 X_PACK_EXPAND((ACT, ARG, __VA_ARGS__))
#define X_PACK_L2_2_32(ACT, ARG, M, NAME,...)  X_PACK_L2_2_DEF(ACT, ARG, M, NAME)     X_PACK_L2_2_30 X_PACK_EXPAND((ACT, ARG, __VA_ARGS__))
#define X_PACK_L2_2_34(ACT, ARG, M, NAME,...)  X_PACK_L2_2_DEF(ACT, ARG, M, NAME)     X_PACK_L2_2_32 X_PACK_EXPAND((ACT, ARG, __VA_ARGS__))
#define X_PACK_L2_2_36(ACT, ARG, M, NAME,...)  X_PACK_L2_2_DEF(ACT, ARG, M, NAME)     X_PACK_L2_2_34 X_PACK_EXPAND((ACT, ARG, __VA_ARGS__))
#define X_PACK_L2_2_38(ACT, ARG, M, NAME,...)  X_PACK_L2_2_DEF(ACT, ARG, M, NAME)     X_PACK_L2_2_36 X_PACK_EXPAND((ACT, ARG, __VA_ARGS__))
#define X_PACK_L2_2_40(ACT, ARG, M, NAME,...)  X_PACK_L2_2_DEF(ACT, ARG, M, NAME)     X_PACK_L2_2_38 X_PACK_EXPAND((ACT, ARG, __VA_ARGS__))
#define X_PACK_L2_2_42(ACT, ARG, M, NAME,...)  X_PACK_L2_2_DEF(ACT, ARG, M, NAME)     X_PACK_L2_2_40 X_PACK_EXPAND((ACT, ARG, __VA_ARGS__))
#define X_PACK_L2_2_44(ACT, ARG, M, NAME,...)  X_PACK_L2_2_DEF(ACT, ARG, M, NAME)     X_PACK_L2_2_42 X_PACK_EXPAND((ACT, ARG, __VA_ARGS__))
#define X_PACK_L2_2_46(ACT, ARG, M, NAME,...)  X_PACK_L2_2_DEF(ACT, ARG, M, NAME)     X_PACK_L2_2_44 X_PACK_EXPAND((ACT, ARG, __VA_ARGS__))
#define X_PACK_L2_2_48(ACT, ARG, M, NAME,...)  X_PACK_L2_2_DEF(ACT, ARG, M, NAME)     X_PACK_L2_2_46 X_PACK_EXPAND((ACT, ARG, __VA_ARGS__))
#define X_PACK_L2_2_50(ACT, ARG, M, NAME,...)  X_PACK_L2_2_DEF(ACT, ARG, M, NAME)     X_PACK_L2_2_48 X_PACK_EXPAND((ACT, ARG, __VA_ARGS__))

#endif

#endif
