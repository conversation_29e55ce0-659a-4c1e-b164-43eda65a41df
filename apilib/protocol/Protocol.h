#pragma once

#ifdef PROTOCOL_STREAM
#ifdef QT_FRAME
#include <QDataStream>
#else
#include <Poco/BinaryReader.h>
#include <Poco/BinaryWriter.h>
#include <Poco/Bugcheck.h>


#endif
#else
#include <stdint.h>
#include <string.h>
#endif

#define MESSAGE_HEAD_FLAG (uint16_t)0xAAAA
#define MESSAGE_TAIL_FLAG (uint16_t)0x5555

#define NS_ZERO_STRUCT(PROTOCOL)                                               \
  PROTOCOL() { ::memset(this, 0, sizeof(PROTOCOL)); }

#define NS_ZERO_PROTOCOL(PROTOCOL, MSGID)                                      \
  PROTOCOL() {                                                                 \
    ::memset(this, 0, sizeof(PROTOCOL));                                       \
    m_head.m_nFlag = MESSAGE_HEAD_FLAG;                                        \
    m_head.m_nMsgId = MSGID;                                                   \
    m_tail.m_nFlag = MESSAGE_TAIL_FLAG;                                        \
  }

#define MAKE_MSGID(nType, nId) (short(nType) << 8) | (short(nId) & 0x00FF)

#define ARRAY_TO_STREAM(STREAM, ARRAY)                                         \
  do {                                                                         \
    for (int i = 0; i < sizeof(ARRAY); ++i) {                                  \
      STREAM << ARRAY[i];                                                      \
    }                                                                          \
  } while (0)

#define STREAM_TO_ARRAY(STREAM, ARRAY)                                         \
  do {                                                                         \
    for (int i = 0; i < sizeof(ARRAY); ++i) {                                  \
      STREAM >> ARRAY[i];                                                      \
    }                                                                          \
  } while (0)

#ifdef PROTOCOL_STREAM
#ifdef QT_FRAME
#define NS_ASSERT Q_ASSERT
#else
#define NS_ASSERT poco_assert
#endif

#ifdef QT_FRAME
using nsStreamWriter = QDataStream;
using nsStreamReader = QDataStream;
#else
using nsStreamWriter = Poco::BinaryWriter;
using nsStreamReader = Poco::BinaryReader;
#endif
#endif