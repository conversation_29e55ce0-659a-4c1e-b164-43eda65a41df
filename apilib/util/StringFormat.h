#ifndef STRING_FORMAT_H
#define STRING_FORMAT_H

#include "fmt/printf.h"
#include <json/json.h>
#include <chrono>
#include <cstdint>
#include <ctime>
#include <iomanip>
#include <random>
#include <sstream>
#include <string>
#include <uuid/uuid.h>
#include <vector>

namespace Util {
/// Default TC string format function.
template <typename Format, typename... Args>
inline std::string StringFormat(Format &&fmt, Args &&...args) {
    try {
        return fmt::sprintf(std::forward<Format>(fmt),
                            std::forward<Args>(args)...);
    } catch (const fmt::FormatError &formatError) {
        std::string error = "An error occurred formatting string \"" +
                            std::string(fmt) +
                            "\" : " + std::string(formatError.what());
        return error;
    }
}

/// Returns true if the given char pointer is null.
inline bool IsFormatEmptyOrNull(char const *fmt) { return fmt == nullptr; }

/// Returns true if the given std::string is empty.
inline bool IsFormatEmptyOrNull(std::string const &fmt) { return fmt.empty(); }
template <typename T>
inline void vectorToString(const std::vector<T> &vec, std::string &str,
                           const std::string &delim = ",") {
    str.clear();
    std::ostringstream ss;
    for (size_t i = 0; i < vec.size(); i++) {
        if (i != 0) {
            ss << delim;
        }
        ss << vec[i];
    }
    str = ss.str();
}

inline std::string jsonToString(const Json::Value &json) {
    Json::StreamWriterBuilder builder;
    return Json::writeString(builder, json);
}

inline Json::Value stringToJson(const std::string &str, std::string errs) {
    Json::Value json;

    Json::CharReaderBuilder builder;
    std::istringstream stream{str};
    
    if (!Json::parseFromStream(builder, stream, &json, &errs)) {
        return "";
    }

    return json;
}

inline  void GenerateGUID(uint8_t guid[16]) { 
    uuid_generate(guid); 
}


inline std::string getCurrentTimeDayString() {
    auto now = std::chrono::system_clock::now();
    auto now_c = std::chrono::system_clock::to_time_t(now);
    auto ms = std::chrono::duration_cast<std::chrono::milliseconds>(now.time_since_epoch()) % 1000;

    std::tm tm = *std::localtime(&now_c);

    std::ostringstream oss;
    oss << std::put_time(&tm, "%Y%m%d");

    return oss.str();
}


inline std::string getCurrentTimeHmssString() {
    auto now = std::chrono::system_clock::now();
    auto now_c = std::chrono::system_clock::to_time_t(now);
    auto ms = std::chrono::duration_cast<std::chrono::milliseconds>(now.time_since_epoch()) % 1000;

    std::tm tm = *std::localtime(&now_c);

    std::ostringstream oss;
    oss << std::put_time(&tm, "%H%M%S");
    oss << std::setfill('0') << std::setw(3) << ms.count();

    return oss.str();
}

inline std::string guidToHexString1111(const uint8_t *guid, size_t length = 16) {
    std::ostringstream oss;
    for (size_t i = 0; i < length; ++i) {
        oss << std::hex << std::setw(2) << std::setfill('0') << (int)guid[i];
    }
    return oss.str();
}

inline uint64_t generate_compressed_uuid() {
    // ��32λǶ�����ʱ���
    // auto now = std::chrono::system_clock::now();
    // uint32_t time_part = now.time_since_epoch().count() & 0xFFFFFFFF;

    // // ��32λ����������
    // std::random_device rd;
    // uint32_t rand_part = rd();

    // return (uint64_t(time_part) << 32) | rand_part;
    return 999;
}

inline int secondsSinceMidnight() {
    auto now = std::chrono::system_clock::now();
    time_t t = std::chrono::system_clock::to_time_t(now);
    tm *local = std::localtime(&t);

    return local->tm_hour * 3600 + local->tm_min * 60 + local->tm_sec;
}

} // namespace Util


#endif