# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.28

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Produce verbose output by default.
VERBOSE = 1

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/EnergyStorage/AI_Box

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/EnergyStorage/AI_Box

# Include any dependencies generated for this target.
include apilib/util/CMakeFiles/Util.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include apilib/util/CMakeFiles/Util.dir/compiler_depend.make

# Include the progress variables for this target.
include apilib/util/CMakeFiles/Util.dir/progress.make

# Include the compile flags for this target's objects.
include apilib/util/CMakeFiles/Util.dir/flags.make

apilib/util/CMakeFiles/Util.dir/BigNumber.cpp.o: apilib/util/CMakeFiles/Util.dir/flags.make
apilib/util/CMakeFiles/Util.dir/BigNumber.cpp.o: apilib/util/BigNumber.cpp
apilib/util/CMakeFiles/Util.dir/BigNumber.cpp.o: apilib/util/CMakeFiles/Util.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/EnergyStorage/AI_Box/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building CXX object apilib/util/CMakeFiles/Util.dir/BigNumber.cpp.o"
	cd /home/<USER>/EnergyStorage/AI_Box/apilib/util && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT apilib/util/CMakeFiles/Util.dir/BigNumber.cpp.o -MF CMakeFiles/Util.dir/BigNumber.cpp.o.d -o CMakeFiles/Util.dir/BigNumber.cpp.o -c /home/<USER>/EnergyStorage/AI_Box/apilib/util/BigNumber.cpp

apilib/util/CMakeFiles/Util.dir/BigNumber.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/Util.dir/BigNumber.cpp.i"
	cd /home/<USER>/EnergyStorage/AI_Box/apilib/util && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/EnergyStorage/AI_Box/apilib/util/BigNumber.cpp > CMakeFiles/Util.dir/BigNumber.cpp.i

apilib/util/CMakeFiles/Util.dir/BigNumber.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/Util.dir/BigNumber.cpp.s"
	cd /home/<USER>/EnergyStorage/AI_Box/apilib/util && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/EnergyStorage/AI_Box/apilib/util/BigNumber.cpp -o CMakeFiles/Util.dir/BigNumber.cpp.s

apilib/util/CMakeFiles/Util.dir/DataQueue.cpp.o: apilib/util/CMakeFiles/Util.dir/flags.make
apilib/util/CMakeFiles/Util.dir/DataQueue.cpp.o: apilib/util/DataQueue.cpp
apilib/util/CMakeFiles/Util.dir/DataQueue.cpp.o: apilib/util/CMakeFiles/Util.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/EnergyStorage/AI_Box/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Building CXX object apilib/util/CMakeFiles/Util.dir/DataQueue.cpp.o"
	cd /home/<USER>/EnergyStorage/AI_Box/apilib/util && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT apilib/util/CMakeFiles/Util.dir/DataQueue.cpp.o -MF CMakeFiles/Util.dir/DataQueue.cpp.o.d -o CMakeFiles/Util.dir/DataQueue.cpp.o -c /home/<USER>/EnergyStorage/AI_Box/apilib/util/DataQueue.cpp

apilib/util/CMakeFiles/Util.dir/DataQueue.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/Util.dir/DataQueue.cpp.i"
	cd /home/<USER>/EnergyStorage/AI_Box/apilib/util && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/EnergyStorage/AI_Box/apilib/util/DataQueue.cpp > CMakeFiles/Util.dir/DataQueue.cpp.i

apilib/util/CMakeFiles/Util.dir/DataQueue.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/Util.dir/DataQueue.cpp.s"
	cd /home/<USER>/EnergyStorage/AI_Box/apilib/util && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/EnergyStorage/AI_Box/apilib/util/DataQueue.cpp -o CMakeFiles/Util.dir/DataQueue.cpp.s

apilib/util/CMakeFiles/Util.dir/GitVision.cpp.o: apilib/util/CMakeFiles/Util.dir/flags.make
apilib/util/CMakeFiles/Util.dir/GitVision.cpp.o: apilib/util/GitVision.cpp
apilib/util/CMakeFiles/Util.dir/GitVision.cpp.o: apilib/util/CMakeFiles/Util.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/EnergyStorage/AI_Box/CMakeFiles --progress-num=$(CMAKE_PROGRESS_3) "Building CXX object apilib/util/CMakeFiles/Util.dir/GitVision.cpp.o"
	cd /home/<USER>/EnergyStorage/AI_Box/apilib/util && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT apilib/util/CMakeFiles/Util.dir/GitVision.cpp.o -MF CMakeFiles/Util.dir/GitVision.cpp.o.d -o CMakeFiles/Util.dir/GitVision.cpp.o -c /home/<USER>/EnergyStorage/AI_Box/apilib/util/GitVision.cpp

apilib/util/CMakeFiles/Util.dir/GitVision.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/Util.dir/GitVision.cpp.i"
	cd /home/<USER>/EnergyStorage/AI_Box/apilib/util && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/EnergyStorage/AI_Box/apilib/util/GitVision.cpp > CMakeFiles/Util.dir/GitVision.cpp.i

apilib/util/CMakeFiles/Util.dir/GitVision.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/Util.dir/GitVision.cpp.s"
	cd /home/<USER>/EnergyStorage/AI_Box/apilib/util && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/EnergyStorage/AI_Box/apilib/util/GitVision.cpp -o CMakeFiles/Util.dir/GitVision.cpp.s

apilib/util/CMakeFiles/Util.dir/INIReader.cpp.o: apilib/util/CMakeFiles/Util.dir/flags.make
apilib/util/CMakeFiles/Util.dir/INIReader.cpp.o: apilib/util/INIReader.cpp
apilib/util/CMakeFiles/Util.dir/INIReader.cpp.o: apilib/util/CMakeFiles/Util.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/EnergyStorage/AI_Box/CMakeFiles --progress-num=$(CMAKE_PROGRESS_4) "Building CXX object apilib/util/CMakeFiles/Util.dir/INIReader.cpp.o"
	cd /home/<USER>/EnergyStorage/AI_Box/apilib/util && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT apilib/util/CMakeFiles/Util.dir/INIReader.cpp.o -MF CMakeFiles/Util.dir/INIReader.cpp.o.d -o CMakeFiles/Util.dir/INIReader.cpp.o -c /home/<USER>/EnergyStorage/AI_Box/apilib/util/INIReader.cpp

apilib/util/CMakeFiles/Util.dir/INIReader.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/Util.dir/INIReader.cpp.i"
	cd /home/<USER>/EnergyStorage/AI_Box/apilib/util && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/EnergyStorage/AI_Box/apilib/util/INIReader.cpp > CMakeFiles/Util.dir/INIReader.cpp.i

apilib/util/CMakeFiles/Util.dir/INIReader.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/Util.dir/INIReader.cpp.s"
	cd /home/<USER>/EnergyStorage/AI_Box/apilib/util && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/EnergyStorage/AI_Box/apilib/util/INIReader.cpp -o CMakeFiles/Util.dir/INIReader.cpp.s

apilib/util/CMakeFiles/Util.dir/MessageBuffer.cpp.o: apilib/util/CMakeFiles/Util.dir/flags.make
apilib/util/CMakeFiles/Util.dir/MessageBuffer.cpp.o: apilib/util/MessageBuffer.cpp
apilib/util/CMakeFiles/Util.dir/MessageBuffer.cpp.o: apilib/util/CMakeFiles/Util.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/EnergyStorage/AI_Box/CMakeFiles --progress-num=$(CMAKE_PROGRESS_5) "Building CXX object apilib/util/CMakeFiles/Util.dir/MessageBuffer.cpp.o"
	cd /home/<USER>/EnergyStorage/AI_Box/apilib/util && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT apilib/util/CMakeFiles/Util.dir/MessageBuffer.cpp.o -MF CMakeFiles/Util.dir/MessageBuffer.cpp.o.d -o CMakeFiles/Util.dir/MessageBuffer.cpp.o -c /home/<USER>/EnergyStorage/AI_Box/apilib/util/MessageBuffer.cpp

apilib/util/CMakeFiles/Util.dir/MessageBuffer.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/Util.dir/MessageBuffer.cpp.i"
	cd /home/<USER>/EnergyStorage/AI_Box/apilib/util && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/EnergyStorage/AI_Box/apilib/util/MessageBuffer.cpp > CMakeFiles/Util.dir/MessageBuffer.cpp.i

apilib/util/CMakeFiles/Util.dir/MessageBuffer.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/Util.dir/MessageBuffer.cpp.s"
	cd /home/<USER>/EnergyStorage/AI_Box/apilib/util && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/EnergyStorage/AI_Box/apilib/util/MessageBuffer.cpp -o CMakeFiles/Util.dir/MessageBuffer.cpp.s

apilib/util/CMakeFiles/Util.dir/SHA1.cpp.o: apilib/util/CMakeFiles/Util.dir/flags.make
apilib/util/CMakeFiles/Util.dir/SHA1.cpp.o: apilib/util/SHA1.cpp
apilib/util/CMakeFiles/Util.dir/SHA1.cpp.o: apilib/util/CMakeFiles/Util.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/EnergyStorage/AI_Box/CMakeFiles --progress-num=$(CMAKE_PROGRESS_6) "Building CXX object apilib/util/CMakeFiles/Util.dir/SHA1.cpp.o"
	cd /home/<USER>/EnergyStorage/AI_Box/apilib/util && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT apilib/util/CMakeFiles/Util.dir/SHA1.cpp.o -MF CMakeFiles/Util.dir/SHA1.cpp.o.d -o CMakeFiles/Util.dir/SHA1.cpp.o -c /home/<USER>/EnergyStorage/AI_Box/apilib/util/SHA1.cpp

apilib/util/CMakeFiles/Util.dir/SHA1.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/Util.dir/SHA1.cpp.i"
	cd /home/<USER>/EnergyStorage/AI_Box/apilib/util && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/EnergyStorage/AI_Box/apilib/util/SHA1.cpp > CMakeFiles/Util.dir/SHA1.cpp.i

apilib/util/CMakeFiles/Util.dir/SHA1.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/Util.dir/SHA1.cpp.s"
	cd /home/<USER>/EnergyStorage/AI_Box/apilib/util && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/EnergyStorage/AI_Box/apilib/util/SHA1.cpp -o CMakeFiles/Util.dir/SHA1.cpp.s

apilib/util/CMakeFiles/Util.dir/StringUtility.cpp.o: apilib/util/CMakeFiles/Util.dir/flags.make
apilib/util/CMakeFiles/Util.dir/StringUtility.cpp.o: apilib/util/StringUtility.cpp
apilib/util/CMakeFiles/Util.dir/StringUtility.cpp.o: apilib/util/CMakeFiles/Util.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/EnergyStorage/AI_Box/CMakeFiles --progress-num=$(CMAKE_PROGRESS_7) "Building CXX object apilib/util/CMakeFiles/Util.dir/StringUtility.cpp.o"
	cd /home/<USER>/EnergyStorage/AI_Box/apilib/util && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT apilib/util/CMakeFiles/Util.dir/StringUtility.cpp.o -MF CMakeFiles/Util.dir/StringUtility.cpp.o.d -o CMakeFiles/Util.dir/StringUtility.cpp.o -c /home/<USER>/EnergyStorage/AI_Box/apilib/util/StringUtility.cpp

apilib/util/CMakeFiles/Util.dir/StringUtility.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/Util.dir/StringUtility.cpp.i"
	cd /home/<USER>/EnergyStorage/AI_Box/apilib/util && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/EnergyStorage/AI_Box/apilib/util/StringUtility.cpp > CMakeFiles/Util.dir/StringUtility.cpp.i

apilib/util/CMakeFiles/Util.dir/StringUtility.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/Util.dir/StringUtility.cpp.s"
	cd /home/<USER>/EnergyStorage/AI_Box/apilib/util && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/EnergyStorage/AI_Box/apilib/util/StringUtility.cpp -o CMakeFiles/Util.dir/StringUtility.cpp.s

# Object files for target Util
Util_OBJECTS = \
"CMakeFiles/Util.dir/BigNumber.cpp.o" \
"CMakeFiles/Util.dir/DataQueue.cpp.o" \
"CMakeFiles/Util.dir/GitVision.cpp.o" \
"CMakeFiles/Util.dir/INIReader.cpp.o" \
"CMakeFiles/Util.dir/MessageBuffer.cpp.o" \
"CMakeFiles/Util.dir/SHA1.cpp.o" \
"CMakeFiles/Util.dir/StringUtility.cpp.o"

# External object files for target Util
Util_EXTERNAL_OBJECTS =

apilib/util/Util.so: apilib/util/CMakeFiles/Util.dir/BigNumber.cpp.o
apilib/util/Util.so: apilib/util/CMakeFiles/Util.dir/DataQueue.cpp.o
apilib/util/Util.so: apilib/util/CMakeFiles/Util.dir/GitVision.cpp.o
apilib/util/Util.so: apilib/util/CMakeFiles/Util.dir/INIReader.cpp.o
apilib/util/Util.so: apilib/util/CMakeFiles/Util.dir/MessageBuffer.cpp.o
apilib/util/Util.so: apilib/util/CMakeFiles/Util.dir/SHA1.cpp.o
apilib/util/Util.so: apilib/util/CMakeFiles/Util.dir/StringUtility.cpp.o
apilib/util/Util.so: apilib/util/CMakeFiles/Util.dir/build.make
apilib/util/Util.so: apilib/dep/fmt/fmt.a
apilib/util/Util.so: /usr/lib/x86_64-linux-gnu/libssl.so
apilib/util/Util.so: /usr/lib/x86_64-linux-gnu/libcrypto.so
apilib/util/Util.so: apilib/util/CMakeFiles/Util.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --bold --progress-dir=/home/<USER>/EnergyStorage/AI_Box/CMakeFiles --progress-num=$(CMAKE_PROGRESS_8) "Linking CXX shared library Util.so"
	cd /home/<USER>/EnergyStorage/AI_Box/apilib/util && $(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/Util.dir/link.txt --verbose=$(VERBOSE)
	cd /home/<USER>/EnergyStorage/AI_Box/apilib/util && /usr/bin/cmake -E copy /home/<USER>/EnergyStorage/AI_Box/apilib/util/Util.so /home/<USER>/EnergyStorage/AI_Box/bin/

# Rule to build all files generated by this target.
apilib/util/CMakeFiles/Util.dir/build: apilib/util/Util.so
.PHONY : apilib/util/CMakeFiles/Util.dir/build

apilib/util/CMakeFiles/Util.dir/clean:
	cd /home/<USER>/EnergyStorage/AI_Box/apilib/util && $(CMAKE_COMMAND) -P CMakeFiles/Util.dir/cmake_clean.cmake
.PHONY : apilib/util/CMakeFiles/Util.dir/clean

apilib/util/CMakeFiles/Util.dir/depend:
	cd /home/<USER>/EnergyStorage/AI_Box && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /home/<USER>/EnergyStorage/AI_Box /home/<USER>/EnergyStorage/AI_Box/apilib/util /home/<USER>/EnergyStorage/AI_Box /home/<USER>/EnergyStorage/AI_Box/apilib/util /home/<USER>/EnergyStorage/AI_Box/apilib/util/CMakeFiles/Util.dir/DependInfo.cmake "--color=$(COLOR)"
.PHONY : apilib/util/CMakeFiles/Util.dir/depend

