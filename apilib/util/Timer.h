#ifndef TIMER_H
#define TIMER_H

#include "Define.h"
#include <chrono>

namespace DB {
inline std::chrono::steady_clock::time_point GetApplicationStartTime() {
    using namespace std::chrono;

    static const steady_clock::time_point ApplicationStartTime = steady_clock::now();

    return ApplicationStartTime;
}

inline uint32 getMSTime() {
    using namespace std::chrono;

    return uint32(duration_cast<milliseconds>(steady_clock::now() - GetApplicationStartTime()).count());
}

inline uint32 getMSTimeDiff(uint32 oldMSTime, uint32 newMSTime) {
    if (oldMSTime > newMSTime)
        return (0xFFFFFFFF - oldMSTime) + newMSTime;
    else
        return newMSTime - oldMSTime;
}

inline uint32 getMSTimeDiff(uint32 oldMSTime, std::chrono::steady_clock::time_point newTime) {
    using namespace std::chrono;

    uint32 newMSTime = uint32(duration_cast<milliseconds>(newTime - GetApplicationStartTime()).count());
    return getMSTimeDiff(oldMSTime, newMSTime);
}

inline uint32 GetMSTimeDiffToNow(uint32 oldMSTime) { return getMSTimeDiff(oldMSTime, getMSTime()); }

inline long getCurrentTimeSec() {
    using namespace std::chrono;
    return duration_cast<seconds>(system_clock::now().time_since_epoch()).count();
}

inline long getSomeDayTimeSec(int days) {
    using namespace std::chrono;
    auto now = system_clock::now();
    time_t tnow = system_clock::to_time_t(now);
    tm *ptm = localtime(&tnow);
    ptm->tm_hour = 0;
    ptm->tm_min = 0;
    ptm->tm_sec = 0;
    auto midnight = system_clock::from_time_t(mktime(ptm));
    auto someDay = midnight - std::chrono::hours(24 * days);
    auto someDayTime = system_clock::to_time_t(someDay);
    return static_cast<long>(someDayTime);
}
} // namespace DB

#endif