# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.28

# Default target executed when no arguments are given to make.
default_target: all
.PHONY : default_target

# Allow only one "make -f Makefile2" at a time, but pass parallelism.
.NOTPARALLEL:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Produce verbose output by default.
VERBOSE = 1

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/EnergyStorage/AI_Box

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/EnergyStorage/AI_Box

#=============================================================================
# Targets provided globally by CMake.

# Special rule for the target edit_cache
edit_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "No interactive CMake dialog available..."
	/usr/bin/cmake -E echo No\ interactive\ CMake\ dialog\ available.
.PHONY : edit_cache

# Special rule for the target edit_cache
edit_cache/fast: edit_cache
.PHONY : edit_cache/fast

# Special rule for the target rebuild_cache
rebuild_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Running CMake to regenerate build system..."
	/usr/bin/cmake --regenerate-during-build -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : rebuild_cache

# Special rule for the target rebuild_cache
rebuild_cache/fast: rebuild_cache
.PHONY : rebuild_cache/fast

# Special rule for the target list_install_components
list_install_components:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Available install components are: \"Unspecified\""
.PHONY : list_install_components

# Special rule for the target list_install_components
list_install_components/fast: list_install_components
.PHONY : list_install_components/fast

# Special rule for the target install
install: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Install the project..."
	/usr/bin/cmake -P cmake_install.cmake
.PHONY : install

# Special rule for the target install
install/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Install the project..."
	/usr/bin/cmake -P cmake_install.cmake
.PHONY : install/fast

# Special rule for the target install/local
install/local: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Installing only the local directory..."
	/usr/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local

# Special rule for the target install/local
install/local/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Installing only the local directory..."
	/usr/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local/fast

# Special rule for the target install/strip
install/strip: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Installing the project stripped..."
	/usr/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip

# Special rule for the target install/strip
install/strip/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Installing the project stripped..."
	/usr/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip/fast

# The main all target
all: cmake_check_build_system
	cd /home/<USER>/EnergyStorage/AI_Box && $(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/EnergyStorage/AI_Box/CMakeFiles /home/<USER>/EnergyStorage/AI_Box/apilib/util//CMakeFiles/progress.marks
	cd /home/<USER>/EnergyStorage/AI_Box && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 apilib/util/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/EnergyStorage/AI_Box/CMakeFiles 0
.PHONY : all

# The main clean target
clean:
	cd /home/<USER>/EnergyStorage/AI_Box && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 apilib/util/clean
.PHONY : clean

# The main clean target
clean/fast: clean
.PHONY : clean/fast

# Prepare targets for installation.
preinstall: all
	cd /home/<USER>/EnergyStorage/AI_Box && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 apilib/util/preinstall
.PHONY : preinstall

# Prepare targets for installation.
preinstall/fast:
	cd /home/<USER>/EnergyStorage/AI_Box && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 apilib/util/preinstall
.PHONY : preinstall/fast

# clear depends
depend:
	cd /home/<USER>/EnergyStorage/AI_Box && $(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 1
.PHONY : depend

# Convenience name for target.
apilib/util/CMakeFiles/Util.dir/rule:
	cd /home/<USER>/EnergyStorage/AI_Box && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 apilib/util/CMakeFiles/Util.dir/rule
.PHONY : apilib/util/CMakeFiles/Util.dir/rule

# Convenience name for target.
Util: apilib/util/CMakeFiles/Util.dir/rule
.PHONY : Util

# fast build rule for target.
Util/fast:
	cd /home/<USER>/EnergyStorage/AI_Box && $(MAKE) $(MAKESILENT) -f apilib/util/CMakeFiles/Util.dir/build.make apilib/util/CMakeFiles/Util.dir/build
.PHONY : Util/fast

BigNumber.o: BigNumber.cpp.o
.PHONY : BigNumber.o

# target to build an object file
BigNumber.cpp.o:
	cd /home/<USER>/EnergyStorage/AI_Box && $(MAKE) $(MAKESILENT) -f apilib/util/CMakeFiles/Util.dir/build.make apilib/util/CMakeFiles/Util.dir/BigNumber.cpp.o
.PHONY : BigNumber.cpp.o

BigNumber.i: BigNumber.cpp.i
.PHONY : BigNumber.i

# target to preprocess a source file
BigNumber.cpp.i:
	cd /home/<USER>/EnergyStorage/AI_Box && $(MAKE) $(MAKESILENT) -f apilib/util/CMakeFiles/Util.dir/build.make apilib/util/CMakeFiles/Util.dir/BigNumber.cpp.i
.PHONY : BigNumber.cpp.i

BigNumber.s: BigNumber.cpp.s
.PHONY : BigNumber.s

# target to generate assembly for a file
BigNumber.cpp.s:
	cd /home/<USER>/EnergyStorage/AI_Box && $(MAKE) $(MAKESILENT) -f apilib/util/CMakeFiles/Util.dir/build.make apilib/util/CMakeFiles/Util.dir/BigNumber.cpp.s
.PHONY : BigNumber.cpp.s

DataQueue.o: DataQueue.cpp.o
.PHONY : DataQueue.o

# target to build an object file
DataQueue.cpp.o:
	cd /home/<USER>/EnergyStorage/AI_Box && $(MAKE) $(MAKESILENT) -f apilib/util/CMakeFiles/Util.dir/build.make apilib/util/CMakeFiles/Util.dir/DataQueue.cpp.o
.PHONY : DataQueue.cpp.o

DataQueue.i: DataQueue.cpp.i
.PHONY : DataQueue.i

# target to preprocess a source file
DataQueue.cpp.i:
	cd /home/<USER>/EnergyStorage/AI_Box && $(MAKE) $(MAKESILENT) -f apilib/util/CMakeFiles/Util.dir/build.make apilib/util/CMakeFiles/Util.dir/DataQueue.cpp.i
.PHONY : DataQueue.cpp.i

DataQueue.s: DataQueue.cpp.s
.PHONY : DataQueue.s

# target to generate assembly for a file
DataQueue.cpp.s:
	cd /home/<USER>/EnergyStorage/AI_Box && $(MAKE) $(MAKESILENT) -f apilib/util/CMakeFiles/Util.dir/build.make apilib/util/CMakeFiles/Util.dir/DataQueue.cpp.s
.PHONY : DataQueue.cpp.s

GitVision.o: GitVision.cpp.o
.PHONY : GitVision.o

# target to build an object file
GitVision.cpp.o:
	cd /home/<USER>/EnergyStorage/AI_Box && $(MAKE) $(MAKESILENT) -f apilib/util/CMakeFiles/Util.dir/build.make apilib/util/CMakeFiles/Util.dir/GitVision.cpp.o
.PHONY : GitVision.cpp.o

GitVision.i: GitVision.cpp.i
.PHONY : GitVision.i

# target to preprocess a source file
GitVision.cpp.i:
	cd /home/<USER>/EnergyStorage/AI_Box && $(MAKE) $(MAKESILENT) -f apilib/util/CMakeFiles/Util.dir/build.make apilib/util/CMakeFiles/Util.dir/GitVision.cpp.i
.PHONY : GitVision.cpp.i

GitVision.s: GitVision.cpp.s
.PHONY : GitVision.s

# target to generate assembly for a file
GitVision.cpp.s:
	cd /home/<USER>/EnergyStorage/AI_Box && $(MAKE) $(MAKESILENT) -f apilib/util/CMakeFiles/Util.dir/build.make apilib/util/CMakeFiles/Util.dir/GitVision.cpp.s
.PHONY : GitVision.cpp.s

INIReader.o: INIReader.cpp.o
.PHONY : INIReader.o

# target to build an object file
INIReader.cpp.o:
	cd /home/<USER>/EnergyStorage/AI_Box && $(MAKE) $(MAKESILENT) -f apilib/util/CMakeFiles/Util.dir/build.make apilib/util/CMakeFiles/Util.dir/INIReader.cpp.o
.PHONY : INIReader.cpp.o

INIReader.i: INIReader.cpp.i
.PHONY : INIReader.i

# target to preprocess a source file
INIReader.cpp.i:
	cd /home/<USER>/EnergyStorage/AI_Box && $(MAKE) $(MAKESILENT) -f apilib/util/CMakeFiles/Util.dir/build.make apilib/util/CMakeFiles/Util.dir/INIReader.cpp.i
.PHONY : INIReader.cpp.i

INIReader.s: INIReader.cpp.s
.PHONY : INIReader.s

# target to generate assembly for a file
INIReader.cpp.s:
	cd /home/<USER>/EnergyStorage/AI_Box && $(MAKE) $(MAKESILENT) -f apilib/util/CMakeFiles/Util.dir/build.make apilib/util/CMakeFiles/Util.dir/INIReader.cpp.s
.PHONY : INIReader.cpp.s

MessageBuffer.o: MessageBuffer.cpp.o
.PHONY : MessageBuffer.o

# target to build an object file
MessageBuffer.cpp.o:
	cd /home/<USER>/EnergyStorage/AI_Box && $(MAKE) $(MAKESILENT) -f apilib/util/CMakeFiles/Util.dir/build.make apilib/util/CMakeFiles/Util.dir/MessageBuffer.cpp.o
.PHONY : MessageBuffer.cpp.o

MessageBuffer.i: MessageBuffer.cpp.i
.PHONY : MessageBuffer.i

# target to preprocess a source file
MessageBuffer.cpp.i:
	cd /home/<USER>/EnergyStorage/AI_Box && $(MAKE) $(MAKESILENT) -f apilib/util/CMakeFiles/Util.dir/build.make apilib/util/CMakeFiles/Util.dir/MessageBuffer.cpp.i
.PHONY : MessageBuffer.cpp.i

MessageBuffer.s: MessageBuffer.cpp.s
.PHONY : MessageBuffer.s

# target to generate assembly for a file
MessageBuffer.cpp.s:
	cd /home/<USER>/EnergyStorage/AI_Box && $(MAKE) $(MAKESILENT) -f apilib/util/CMakeFiles/Util.dir/build.make apilib/util/CMakeFiles/Util.dir/MessageBuffer.cpp.s
.PHONY : MessageBuffer.cpp.s

SHA1.o: SHA1.cpp.o
.PHONY : SHA1.o

# target to build an object file
SHA1.cpp.o:
	cd /home/<USER>/EnergyStorage/AI_Box && $(MAKE) $(MAKESILENT) -f apilib/util/CMakeFiles/Util.dir/build.make apilib/util/CMakeFiles/Util.dir/SHA1.cpp.o
.PHONY : SHA1.cpp.o

SHA1.i: SHA1.cpp.i
.PHONY : SHA1.i

# target to preprocess a source file
SHA1.cpp.i:
	cd /home/<USER>/EnergyStorage/AI_Box && $(MAKE) $(MAKESILENT) -f apilib/util/CMakeFiles/Util.dir/build.make apilib/util/CMakeFiles/Util.dir/SHA1.cpp.i
.PHONY : SHA1.cpp.i

SHA1.s: SHA1.cpp.s
.PHONY : SHA1.s

# target to generate assembly for a file
SHA1.cpp.s:
	cd /home/<USER>/EnergyStorage/AI_Box && $(MAKE) $(MAKESILENT) -f apilib/util/CMakeFiles/Util.dir/build.make apilib/util/CMakeFiles/Util.dir/SHA1.cpp.s
.PHONY : SHA1.cpp.s

StringUtility.o: StringUtility.cpp.o
.PHONY : StringUtility.o

# target to build an object file
StringUtility.cpp.o:
	cd /home/<USER>/EnergyStorage/AI_Box && $(MAKE) $(MAKESILENT) -f apilib/util/CMakeFiles/Util.dir/build.make apilib/util/CMakeFiles/Util.dir/StringUtility.cpp.o
.PHONY : StringUtility.cpp.o

StringUtility.i: StringUtility.cpp.i
.PHONY : StringUtility.i

# target to preprocess a source file
StringUtility.cpp.i:
	cd /home/<USER>/EnergyStorage/AI_Box && $(MAKE) $(MAKESILENT) -f apilib/util/CMakeFiles/Util.dir/build.make apilib/util/CMakeFiles/Util.dir/StringUtility.cpp.i
.PHONY : StringUtility.cpp.i

StringUtility.s: StringUtility.cpp.s
.PHONY : StringUtility.s

# target to generate assembly for a file
StringUtility.cpp.s:
	cd /home/<USER>/EnergyStorage/AI_Box && $(MAKE) $(MAKESILENT) -f apilib/util/CMakeFiles/Util.dir/build.make apilib/util/CMakeFiles/Util.dir/StringUtility.cpp.s
.PHONY : StringUtility.cpp.s

# Help Target
help:
	@echo "The following are some of the valid targets for this Makefile:"
	@echo "... all (the default if no target is provided)"
	@echo "... clean"
	@echo "... depend"
	@echo "... edit_cache"
	@echo "... install"
	@echo "... install/local"
	@echo "... install/strip"
	@echo "... list_install_components"
	@echo "... rebuild_cache"
	@echo "... Util"
	@echo "... BigNumber.o"
	@echo "... BigNumber.i"
	@echo "... BigNumber.s"
	@echo "... DataQueue.o"
	@echo "... DataQueue.i"
	@echo "... DataQueue.s"
	@echo "... GitVision.o"
	@echo "... GitVision.i"
	@echo "... GitVision.s"
	@echo "... INIReader.o"
	@echo "... INIReader.i"
	@echo "... INIReader.s"
	@echo "... MessageBuffer.o"
	@echo "... MessageBuffer.i"
	@echo "... MessageBuffer.s"
	@echo "... SHA1.o"
	@echo "... SHA1.i"
	@echo "... SHA1.s"
	@echo "... StringUtility.o"
	@echo "... StringUtility.i"
	@echo "... StringUtility.s"
.PHONY : help



#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	cd /home/<USER>/EnergyStorage/AI_Box && $(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system

