#ifndef LOG_COMM_H
#define LOG_COMM_H

namespace LogComm
{
	enum LogLevel
	{
		LOG_LEVEL_DISABLED = 0,
		LOG_LEVEL_TRACE = 1,
		LOG_LEVEL_DEBUG = 2,
		LOG_LEVEL_INFO = 3,
		LOG_LEVEL_WARN = 4,
		LOG_LEVEL_ERROR = 5,
		LOG_LEVEL_FATAL = 6,

		NUM_ENABLED_LOG_LEVELS = 6
	};

	enum AppenderType
	{
		APPENDER_NONE = 0x00,
		APPENDER_CONSOLE = 0x01,
		APPENDER_FILE = 0x02,
		APPENDER_DB = 0x04
	};

	enum AppenderFlags
	{
		AFLAGS_NONE = 0x00,
		AFLAGS_PREFIX_TIMESTAMP = 0x01,
		AFLAGS_PREFIX_LOGLEVEL = 0x02,
		AFLAGS_PREFIX_LOGFILTERTYPE = 0x04,
		AFLAGS_USE_TIMESTAMP = 0x08,
		AFLAGS_MAKE_FILE_BACKUP = 0x10
	};
}

#endif