
# Consider dependencies only in project.
set(CMAKE_DEPENDS_IN_PROJECT_ONLY OFF)

# The set of languages for which implicit dependencies are needed:
set(CMAKE_DEPENDS_LANGUAGES
  )

# The set of dependency files which are needed:
set(CMAKE_DEPENDS_DEPENDENCY_FILES
  "/home/<USER>/EnergyStorage/AI_Box/apilib/log/Log.cpp" "apilib/log/CMakeFiles/Log.dir/Log.cpp.o" "gcc" "apilib/log/CMakeFiles/Log.dir/Log.cpp.o.d"
  "/home/<USER>/EnergyStorage/AI_Box/apilib/log/LogConsole.cpp" "apilib/log/CMakeFiles/Log.dir/LogConsole.cpp.o" "gcc" "apilib/log/CMakeFiles/Log.dir/LogConsole.cpp.o.d"
  "/home/<USER>/EnergyStorage/AI_Box/apilib/log/LogFile.cpp" "apilib/log/CMakeFiles/Log.dir/LogFile.cpp.o" "gcc" "apilib/log/CMakeFiles/Log.dir/LogFile.cpp.o.d"
  "/home/<USER>/EnergyStorage/AI_Box/apilib/log/LogMessage.cpp" "apilib/log/CMakeFiles/Log.dir/LogMessage.cpp.o" "gcc" "apilib/log/CMakeFiles/Log.dir/LogMessage.cpp.o.d"
  )

# Targets to which this target links which contain Fortran sources.
set(CMAKE_Fortran_TARGET_LINKED_INFO_FILES
  )

# Targets to which this target links which contain Fortran sources.
set(CMAKE_Fortran_TARGET_FORWARD_LINKED_INFO_FILES
  )

# Fortran module output directory.
set(CMAKE_Fortran_TARGET_MODULE_DIR "")
