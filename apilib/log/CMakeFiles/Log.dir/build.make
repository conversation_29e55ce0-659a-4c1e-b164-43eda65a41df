# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.28

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Produce verbose output by default.
VERBOSE = 1

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/EnergyStorage/AI_Box

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/EnergyStorage/AI_Box

# Include any dependencies generated for this target.
include apilib/log/CMakeFiles/Log.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include apilib/log/CMakeFiles/Log.dir/compiler_depend.make

# Include the progress variables for this target.
include apilib/log/CMakeFiles/Log.dir/progress.make

# Include the compile flags for this target's objects.
include apilib/log/CMakeFiles/Log.dir/flags.make

apilib/log/CMakeFiles/Log.dir/Log.cpp.o: apilib/log/CMakeFiles/Log.dir/flags.make
apilib/log/CMakeFiles/Log.dir/Log.cpp.o: apilib/log/Log.cpp
apilib/log/CMakeFiles/Log.dir/Log.cpp.o: apilib/log/CMakeFiles/Log.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/EnergyStorage/AI_Box/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building CXX object apilib/log/CMakeFiles/Log.dir/Log.cpp.o"
	cd /home/<USER>/EnergyStorage/AI_Box/apilib/log && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT apilib/log/CMakeFiles/Log.dir/Log.cpp.o -MF CMakeFiles/Log.dir/Log.cpp.o.d -o CMakeFiles/Log.dir/Log.cpp.o -c /home/<USER>/EnergyStorage/AI_Box/apilib/log/Log.cpp

apilib/log/CMakeFiles/Log.dir/Log.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/Log.dir/Log.cpp.i"
	cd /home/<USER>/EnergyStorage/AI_Box/apilib/log && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/EnergyStorage/AI_Box/apilib/log/Log.cpp > CMakeFiles/Log.dir/Log.cpp.i

apilib/log/CMakeFiles/Log.dir/Log.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/Log.dir/Log.cpp.s"
	cd /home/<USER>/EnergyStorage/AI_Box/apilib/log && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/EnergyStorage/AI_Box/apilib/log/Log.cpp -o CMakeFiles/Log.dir/Log.cpp.s

apilib/log/CMakeFiles/Log.dir/LogConsole.cpp.o: apilib/log/CMakeFiles/Log.dir/flags.make
apilib/log/CMakeFiles/Log.dir/LogConsole.cpp.o: apilib/log/LogConsole.cpp
apilib/log/CMakeFiles/Log.dir/LogConsole.cpp.o: apilib/log/CMakeFiles/Log.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/EnergyStorage/AI_Box/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Building CXX object apilib/log/CMakeFiles/Log.dir/LogConsole.cpp.o"
	cd /home/<USER>/EnergyStorage/AI_Box/apilib/log && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT apilib/log/CMakeFiles/Log.dir/LogConsole.cpp.o -MF CMakeFiles/Log.dir/LogConsole.cpp.o.d -o CMakeFiles/Log.dir/LogConsole.cpp.o -c /home/<USER>/EnergyStorage/AI_Box/apilib/log/LogConsole.cpp

apilib/log/CMakeFiles/Log.dir/LogConsole.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/Log.dir/LogConsole.cpp.i"
	cd /home/<USER>/EnergyStorage/AI_Box/apilib/log && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/EnergyStorage/AI_Box/apilib/log/LogConsole.cpp > CMakeFiles/Log.dir/LogConsole.cpp.i

apilib/log/CMakeFiles/Log.dir/LogConsole.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/Log.dir/LogConsole.cpp.s"
	cd /home/<USER>/EnergyStorage/AI_Box/apilib/log && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/EnergyStorage/AI_Box/apilib/log/LogConsole.cpp -o CMakeFiles/Log.dir/LogConsole.cpp.s

apilib/log/CMakeFiles/Log.dir/LogFile.cpp.o: apilib/log/CMakeFiles/Log.dir/flags.make
apilib/log/CMakeFiles/Log.dir/LogFile.cpp.o: apilib/log/LogFile.cpp
apilib/log/CMakeFiles/Log.dir/LogFile.cpp.o: apilib/log/CMakeFiles/Log.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/EnergyStorage/AI_Box/CMakeFiles --progress-num=$(CMAKE_PROGRESS_3) "Building CXX object apilib/log/CMakeFiles/Log.dir/LogFile.cpp.o"
	cd /home/<USER>/EnergyStorage/AI_Box/apilib/log && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT apilib/log/CMakeFiles/Log.dir/LogFile.cpp.o -MF CMakeFiles/Log.dir/LogFile.cpp.o.d -o CMakeFiles/Log.dir/LogFile.cpp.o -c /home/<USER>/EnergyStorage/AI_Box/apilib/log/LogFile.cpp

apilib/log/CMakeFiles/Log.dir/LogFile.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/Log.dir/LogFile.cpp.i"
	cd /home/<USER>/EnergyStorage/AI_Box/apilib/log && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/EnergyStorage/AI_Box/apilib/log/LogFile.cpp > CMakeFiles/Log.dir/LogFile.cpp.i

apilib/log/CMakeFiles/Log.dir/LogFile.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/Log.dir/LogFile.cpp.s"
	cd /home/<USER>/EnergyStorage/AI_Box/apilib/log && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/EnergyStorage/AI_Box/apilib/log/LogFile.cpp -o CMakeFiles/Log.dir/LogFile.cpp.s

apilib/log/CMakeFiles/Log.dir/LogMessage.cpp.o: apilib/log/CMakeFiles/Log.dir/flags.make
apilib/log/CMakeFiles/Log.dir/LogMessage.cpp.o: apilib/log/LogMessage.cpp
apilib/log/CMakeFiles/Log.dir/LogMessage.cpp.o: apilib/log/CMakeFiles/Log.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/EnergyStorage/AI_Box/CMakeFiles --progress-num=$(CMAKE_PROGRESS_4) "Building CXX object apilib/log/CMakeFiles/Log.dir/LogMessage.cpp.o"
	cd /home/<USER>/EnergyStorage/AI_Box/apilib/log && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT apilib/log/CMakeFiles/Log.dir/LogMessage.cpp.o -MF CMakeFiles/Log.dir/LogMessage.cpp.o.d -o CMakeFiles/Log.dir/LogMessage.cpp.o -c /home/<USER>/EnergyStorage/AI_Box/apilib/log/LogMessage.cpp

apilib/log/CMakeFiles/Log.dir/LogMessage.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/Log.dir/LogMessage.cpp.i"
	cd /home/<USER>/EnergyStorage/AI_Box/apilib/log && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/EnergyStorage/AI_Box/apilib/log/LogMessage.cpp > CMakeFiles/Log.dir/LogMessage.cpp.i

apilib/log/CMakeFiles/Log.dir/LogMessage.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/Log.dir/LogMessage.cpp.s"
	cd /home/<USER>/EnergyStorage/AI_Box/apilib/log && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/EnergyStorage/AI_Box/apilib/log/LogMessage.cpp -o CMakeFiles/Log.dir/LogMessage.cpp.s

# Object files for target Log
Log_OBJECTS = \
"CMakeFiles/Log.dir/Log.cpp.o" \
"CMakeFiles/Log.dir/LogConsole.cpp.o" \
"CMakeFiles/Log.dir/LogFile.cpp.o" \
"CMakeFiles/Log.dir/LogMessage.cpp.o"

# External object files for target Log
Log_EXTERNAL_OBJECTS =

apilib/log/Log.so: apilib/log/CMakeFiles/Log.dir/Log.cpp.o
apilib/log/Log.so: apilib/log/CMakeFiles/Log.dir/LogConsole.cpp.o
apilib/log/Log.so: apilib/log/CMakeFiles/Log.dir/LogFile.cpp.o
apilib/log/Log.so: apilib/log/CMakeFiles/Log.dir/LogMessage.cpp.o
apilib/log/Log.so: apilib/log/CMakeFiles/Log.dir/build.make
apilib/log/Log.so: apilib/util/Util.so
apilib/log/Log.so: apilib/dep/fmt/fmt.a
apilib/log/Log.so: /usr/lib/x86_64-linux-gnu/libssl.so
apilib/log/Log.so: /usr/lib/x86_64-linux-gnu/libcrypto.so
apilib/log/Log.so: apilib/log/CMakeFiles/Log.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --bold --progress-dir=/home/<USER>/EnergyStorage/AI_Box/CMakeFiles --progress-num=$(CMAKE_PROGRESS_5) "Linking CXX shared library Log.so"
	cd /home/<USER>/EnergyStorage/AI_Box/apilib/log && $(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/Log.dir/link.txt --verbose=$(VERBOSE)
	cd /home/<USER>/EnergyStorage/AI_Box/apilib/log && /usr/bin/cmake -E copy /home/<USER>/EnergyStorage/AI_Box/apilib/log/Log.so /home/<USER>/EnergyStorage/AI_Box/bin/

# Rule to build all files generated by this target.
apilib/log/CMakeFiles/Log.dir/build: apilib/log/Log.so
.PHONY : apilib/log/CMakeFiles/Log.dir/build

apilib/log/CMakeFiles/Log.dir/clean:
	cd /home/<USER>/EnergyStorage/AI_Box/apilib/log && $(CMAKE_COMMAND) -P CMakeFiles/Log.dir/cmake_clean.cmake
.PHONY : apilib/log/CMakeFiles/Log.dir/clean

apilib/log/CMakeFiles/Log.dir/depend:
	cd /home/<USER>/EnergyStorage/AI_Box && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /home/<USER>/EnergyStorage/AI_Box /home/<USER>/EnergyStorage/AI_Box/apilib/log /home/<USER>/EnergyStorage/AI_Box /home/<USER>/EnergyStorage/AI_Box/apilib/log /home/<USER>/EnergyStorage/AI_Box/apilib/log/CMakeFiles/Log.dir/DependInfo.cmake "--color=$(COLOR)"
.PHONY : apilib/log/CMakeFiles/Log.dir/depend

