//
// experimental.hpp
// ~~~~~~~~~~~~~~~~
//
// Copyright (c) 2003-2017 <PERSON> (chris at kohlhoff dot com)
//
// Distributed under the Boost Software License, Version 1.0. (See accompanying
// file LICENSE_1_0.txt or copy at http://www.boost.org/LICENSE_1_0.txt)
//

#ifndef ASIO_EXPERIMENTAL_HPP
#define ASIO_EXPERIMENTAL_HPP

#if defined(_MSC_VER) && (_MSC_VER >= 1200)
# pragma once
#endif // defined(_MSC_VER) && (_MSC_VER >= 1200)

#include "asio/experimental/co_spawn.hpp"
#include "asio/experimental/detached.hpp"
#include "asio/experimental/redirect_error.hpp"

#endif // ASIO_EXPERIMENTAL_HPP
