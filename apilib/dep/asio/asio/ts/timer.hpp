//
// ts/timer.hpp
// ~~~~~~~~~~~~
//
// Copyright (c) 2003-2018 <PERSON> (chris at kohlhoff dot com)
//
// Distributed under the Boost Software License, Version 1.0. (See accompanying
// file LICENSE_1_0.txt or copy at http://www.boost.org/LICENSE_1_0.txt)
//

#ifndef ASIO_TS_TIMER_HPP
#define ASIO_TS_TIMER_HPP

#if defined(_MSC_VER) && (_MSC_VER >= 1200)
# pragma once
#endif // defined(_MSC_VER) && (_MSC_VER >= 1200)

#include "asio/detail/chrono.hpp"

#include "asio/wait_traits.hpp"
#include "asio/basic_waitable_timer.hpp"
#include "asio/system_timer.hpp"
#include "asio/steady_timer.hpp"
#include "asio/high_resolution_timer.hpp"

#endif // ASIO_TS_TIMER_HPP
