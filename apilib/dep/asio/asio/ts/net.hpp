//
// ts/net.hpp
// ~~~~~~~~~~
//
// Copyright (c) 2003-2018 <PERSON> (chris at kohlhoff dot com)
//
// Distributed under the Boost Software License, Version 1.0. (See accompanying
// file LICENSE_1_0.txt or copy at http://www.boost.org/LICENSE_1_0.txt)
//

#ifndef ASIO_TS_NET_HPP
#define ASIO_TS_NET_HPP

#if defined(_MSC_VER) && (_MSC_VER >= 1200)
# pragma once
#endif // defined(_MSC_VER) && (_MSC_VER >= 1200)

#include "asio/ts/netfwd.hpp"
#include "asio/ts/executor.hpp"
#include "asio/ts/io_context.hpp"
#include "asio/ts/timer.hpp"
#include "asio/ts/buffer.hpp"
#include "asio/ts/socket.hpp"
#include "asio/ts/internet.hpp"

#endif // ASIO_TS_NET_HPP
