//
// ts/buffer.hpp
// ~~~~~~~~~~~~~
//
// Copyright (c) 2003-2018 <PERSON> (chris at kohlhoff dot com)
//
// Distributed under the Boost Software License, Version 1.0. (See accompanying
// file LICENSE_1_0.txt or copy at http://www.boost.org/LICENSE_1_0.txt)
//

#ifndef ASIO_TS_BUFFER_HPP
#define ASIO_TS_BUFFER_HPP

#if defined(_MSC_VER) && (_MSC_VER >= 1200)
# pragma once
#endif // defined(_MSC_VER) && (_MSC_VER >= 1200)

#include "asio/buffer.hpp"
#include "asio/completion_condition.hpp"
#include "asio/read.hpp"
#include "asio/write.hpp"
#include "asio/read_until.hpp"

#endif // ASIO_TS_BUFFER_HPP
