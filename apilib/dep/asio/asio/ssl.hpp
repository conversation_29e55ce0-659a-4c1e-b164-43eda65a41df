//
// ssl.hpp
// ~~~~~~~
//
// Copyright (c) 2003-2018 <PERSON> (chris at kohlhoff dot com)
//
// Distributed under the Boost Software License, Version 1.0. (See accompanying
// file LICENSE_1_0.txt or copy at http://www.boost.org/LICENSE_1_0.txt)
//

#ifndef ASIO_SSL_HPP
#define ASIO_SSL_HPP

#if defined(_MSC_VER) && (_MSC_VER >= 1200)
# pragma once
#endif // defined(_MSC_VER) && (_MSC_VER >= 1200)

#include "asio/ssl/context.hpp"
#include "asio/ssl/context_base.hpp"
#include "asio/ssl/error.hpp"
#include "asio/ssl/rfc2818_verification.hpp"
#include "asio/ssl/stream.hpp"
#include "asio/ssl/stream_base.hpp"
#include "asio/ssl/verify_context.hpp"
#include "asio/ssl/verify_mode.hpp"

#endif // ASIO_SSL_HPP
