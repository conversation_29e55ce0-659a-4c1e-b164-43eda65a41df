# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.28

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Produce verbose output by default.
VERBOSE = 1

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/EnergyStorage/AI_Box

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/EnergyStorage/AI_Box

# Include any dependencies generated for this target.
include apilib/dep/fmt/CMakeFiles/fmt.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include apilib/dep/fmt/CMakeFiles/fmt.dir/compiler_depend.make

# Include the progress variables for this target.
include apilib/dep/fmt/CMakeFiles/fmt.dir/progress.make

# Include the compile flags for this target's objects.
include apilib/dep/fmt/CMakeFiles/fmt.dir/flags.make

apilib/dep/fmt/CMakeFiles/fmt.dir/fmt/format.cc.o: apilib/dep/fmt/CMakeFiles/fmt.dir/flags.make
apilib/dep/fmt/CMakeFiles/fmt.dir/fmt/format.cc.o: apilib/dep/fmt/fmt/format.cc
apilib/dep/fmt/CMakeFiles/fmt.dir/fmt/format.cc.o: apilib/dep/fmt/CMakeFiles/fmt.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/EnergyStorage/AI_Box/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building CXX object apilib/dep/fmt/CMakeFiles/fmt.dir/fmt/format.cc.o"
	cd /home/<USER>/EnergyStorage/AI_Box/apilib/dep/fmt && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT apilib/dep/fmt/CMakeFiles/fmt.dir/fmt/format.cc.o -MF CMakeFiles/fmt.dir/fmt/format.cc.o.d -o CMakeFiles/fmt.dir/fmt/format.cc.o -c /home/<USER>/EnergyStorage/AI_Box/apilib/dep/fmt/fmt/format.cc

apilib/dep/fmt/CMakeFiles/fmt.dir/fmt/format.cc.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/fmt.dir/fmt/format.cc.i"
	cd /home/<USER>/EnergyStorage/AI_Box/apilib/dep/fmt && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/EnergyStorage/AI_Box/apilib/dep/fmt/fmt/format.cc > CMakeFiles/fmt.dir/fmt/format.cc.i

apilib/dep/fmt/CMakeFiles/fmt.dir/fmt/format.cc.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/fmt.dir/fmt/format.cc.s"
	cd /home/<USER>/EnergyStorage/AI_Box/apilib/dep/fmt && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/EnergyStorage/AI_Box/apilib/dep/fmt/fmt/format.cc -o CMakeFiles/fmt.dir/fmt/format.cc.s

apilib/dep/fmt/CMakeFiles/fmt.dir/fmt/ostream.cc.o: apilib/dep/fmt/CMakeFiles/fmt.dir/flags.make
apilib/dep/fmt/CMakeFiles/fmt.dir/fmt/ostream.cc.o: apilib/dep/fmt/fmt/ostream.cc
apilib/dep/fmt/CMakeFiles/fmt.dir/fmt/ostream.cc.o: apilib/dep/fmt/CMakeFiles/fmt.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/EnergyStorage/AI_Box/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Building CXX object apilib/dep/fmt/CMakeFiles/fmt.dir/fmt/ostream.cc.o"
	cd /home/<USER>/EnergyStorage/AI_Box/apilib/dep/fmt && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT apilib/dep/fmt/CMakeFiles/fmt.dir/fmt/ostream.cc.o -MF CMakeFiles/fmt.dir/fmt/ostream.cc.o.d -o CMakeFiles/fmt.dir/fmt/ostream.cc.o -c /home/<USER>/EnergyStorage/AI_Box/apilib/dep/fmt/fmt/ostream.cc

apilib/dep/fmt/CMakeFiles/fmt.dir/fmt/ostream.cc.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/fmt.dir/fmt/ostream.cc.i"
	cd /home/<USER>/EnergyStorage/AI_Box/apilib/dep/fmt && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/EnergyStorage/AI_Box/apilib/dep/fmt/fmt/ostream.cc > CMakeFiles/fmt.dir/fmt/ostream.cc.i

apilib/dep/fmt/CMakeFiles/fmt.dir/fmt/ostream.cc.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/fmt.dir/fmt/ostream.cc.s"
	cd /home/<USER>/EnergyStorage/AI_Box/apilib/dep/fmt && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/EnergyStorage/AI_Box/apilib/dep/fmt/fmt/ostream.cc -o CMakeFiles/fmt.dir/fmt/ostream.cc.s

apilib/dep/fmt/CMakeFiles/fmt.dir/fmt/printf.cc.o: apilib/dep/fmt/CMakeFiles/fmt.dir/flags.make
apilib/dep/fmt/CMakeFiles/fmt.dir/fmt/printf.cc.o: apilib/dep/fmt/fmt/printf.cc
apilib/dep/fmt/CMakeFiles/fmt.dir/fmt/printf.cc.o: apilib/dep/fmt/CMakeFiles/fmt.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/EnergyStorage/AI_Box/CMakeFiles --progress-num=$(CMAKE_PROGRESS_3) "Building CXX object apilib/dep/fmt/CMakeFiles/fmt.dir/fmt/printf.cc.o"
	cd /home/<USER>/EnergyStorage/AI_Box/apilib/dep/fmt && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT apilib/dep/fmt/CMakeFiles/fmt.dir/fmt/printf.cc.o -MF CMakeFiles/fmt.dir/fmt/printf.cc.o.d -o CMakeFiles/fmt.dir/fmt/printf.cc.o -c /home/<USER>/EnergyStorage/AI_Box/apilib/dep/fmt/fmt/printf.cc

apilib/dep/fmt/CMakeFiles/fmt.dir/fmt/printf.cc.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/fmt.dir/fmt/printf.cc.i"
	cd /home/<USER>/EnergyStorage/AI_Box/apilib/dep/fmt && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/EnergyStorage/AI_Box/apilib/dep/fmt/fmt/printf.cc > CMakeFiles/fmt.dir/fmt/printf.cc.i

apilib/dep/fmt/CMakeFiles/fmt.dir/fmt/printf.cc.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/fmt.dir/fmt/printf.cc.s"
	cd /home/<USER>/EnergyStorage/AI_Box/apilib/dep/fmt && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/EnergyStorage/AI_Box/apilib/dep/fmt/fmt/printf.cc -o CMakeFiles/fmt.dir/fmt/printf.cc.s

apilib/dep/fmt/CMakeFiles/fmt.dir/fmt/posix.cc.o: apilib/dep/fmt/CMakeFiles/fmt.dir/flags.make
apilib/dep/fmt/CMakeFiles/fmt.dir/fmt/posix.cc.o: apilib/dep/fmt/fmt/posix.cc
apilib/dep/fmt/CMakeFiles/fmt.dir/fmt/posix.cc.o: apilib/dep/fmt/CMakeFiles/fmt.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/EnergyStorage/AI_Box/CMakeFiles --progress-num=$(CMAKE_PROGRESS_4) "Building CXX object apilib/dep/fmt/CMakeFiles/fmt.dir/fmt/posix.cc.o"
	cd /home/<USER>/EnergyStorage/AI_Box/apilib/dep/fmt && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT apilib/dep/fmt/CMakeFiles/fmt.dir/fmt/posix.cc.o -MF CMakeFiles/fmt.dir/fmt/posix.cc.o.d -o CMakeFiles/fmt.dir/fmt/posix.cc.o -c /home/<USER>/EnergyStorage/AI_Box/apilib/dep/fmt/fmt/posix.cc

apilib/dep/fmt/CMakeFiles/fmt.dir/fmt/posix.cc.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/fmt.dir/fmt/posix.cc.i"
	cd /home/<USER>/EnergyStorage/AI_Box/apilib/dep/fmt && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/EnergyStorage/AI_Box/apilib/dep/fmt/fmt/posix.cc > CMakeFiles/fmt.dir/fmt/posix.cc.i

apilib/dep/fmt/CMakeFiles/fmt.dir/fmt/posix.cc.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/fmt.dir/fmt/posix.cc.s"
	cd /home/<USER>/EnergyStorage/AI_Box/apilib/dep/fmt && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/EnergyStorage/AI_Box/apilib/dep/fmt/fmt/posix.cc -o CMakeFiles/fmt.dir/fmt/posix.cc.s

# Object files for target fmt
fmt_OBJECTS = \
"CMakeFiles/fmt.dir/fmt/format.cc.o" \
"CMakeFiles/fmt.dir/fmt/ostream.cc.o" \
"CMakeFiles/fmt.dir/fmt/printf.cc.o" \
"CMakeFiles/fmt.dir/fmt/posix.cc.o"

# External object files for target fmt
fmt_EXTERNAL_OBJECTS =

apilib/dep/fmt/fmt.a: apilib/dep/fmt/CMakeFiles/fmt.dir/fmt/format.cc.o
apilib/dep/fmt/fmt.a: apilib/dep/fmt/CMakeFiles/fmt.dir/fmt/ostream.cc.o
apilib/dep/fmt/fmt.a: apilib/dep/fmt/CMakeFiles/fmt.dir/fmt/printf.cc.o
apilib/dep/fmt/fmt.a: apilib/dep/fmt/CMakeFiles/fmt.dir/fmt/posix.cc.o
apilib/dep/fmt/fmt.a: apilib/dep/fmt/CMakeFiles/fmt.dir/build.make
apilib/dep/fmt/fmt.a: apilib/dep/fmt/CMakeFiles/fmt.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --bold --progress-dir=/home/<USER>/EnergyStorage/AI_Box/CMakeFiles --progress-num=$(CMAKE_PROGRESS_5) "Linking CXX static library fmt.a"
	cd /home/<USER>/EnergyStorage/AI_Box/apilib/dep/fmt && $(CMAKE_COMMAND) -P CMakeFiles/fmt.dir/cmake_clean_target.cmake
	cd /home/<USER>/EnergyStorage/AI_Box/apilib/dep/fmt && $(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/fmt.dir/link.txt --verbose=$(VERBOSE)
	cd /home/<USER>/EnergyStorage/AI_Box/apilib/dep/fmt && /usr/bin/cmake -E copy /home/<USER>/EnergyStorage/AI_Box/apilib/dep/fmt/fmt.a /home/<USER>/EnergyStorage/AI_Box/bin/

# Rule to build all files generated by this target.
apilib/dep/fmt/CMakeFiles/fmt.dir/build: apilib/dep/fmt/fmt.a
.PHONY : apilib/dep/fmt/CMakeFiles/fmt.dir/build

apilib/dep/fmt/CMakeFiles/fmt.dir/clean:
	cd /home/<USER>/EnergyStorage/AI_Box/apilib/dep/fmt && $(CMAKE_COMMAND) -P CMakeFiles/fmt.dir/cmake_clean.cmake
.PHONY : apilib/dep/fmt/CMakeFiles/fmt.dir/clean

apilib/dep/fmt/CMakeFiles/fmt.dir/depend:
	cd /home/<USER>/EnergyStorage/AI_Box && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /home/<USER>/EnergyStorage/AI_Box /home/<USER>/EnergyStorage/AI_Box/apilib/dep/fmt /home/<USER>/EnergyStorage/AI_Box /home/<USER>/EnergyStorage/AI_Box/apilib/dep/fmt /home/<USER>/EnergyStorage/AI_Box/apilib/dep/fmt/CMakeFiles/fmt.dir/DependInfo.cmake "--color=$(COLOR)"
.PHONY : apilib/dep/fmt/CMakeFiles/fmt.dir/depend

