# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.28

# Default target executed when no arguments are given to make.
default_target: all
.PHONY : default_target

# Allow only one "make -f Makefile2" at a time, but pass parallelism.
.NOTPARALLEL:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Produce verbose output by default.
VERBOSE = 1

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/EnergyStorage/AI_Box

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/EnergyStorage/AI_Box

#=============================================================================
# Targets provided globally by CMake.

# Special rule for the target edit_cache
edit_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "No interactive CMake dialog available..."
	/usr/bin/cmake -E echo No\ interactive\ CMake\ dialog\ available.
.PHONY : edit_cache

# Special rule for the target edit_cache
edit_cache/fast: edit_cache
.PHONY : edit_cache/fast

# Special rule for the target rebuild_cache
rebuild_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Running CMake to regenerate build system..."
	/usr/bin/cmake --regenerate-during-build -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : rebuild_cache

# Special rule for the target rebuild_cache
rebuild_cache/fast: rebuild_cache
.PHONY : rebuild_cache/fast

# Special rule for the target list_install_components
list_install_components:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Available install components are: \"Unspecified\""
.PHONY : list_install_components

# Special rule for the target list_install_components
list_install_components/fast: list_install_components
.PHONY : list_install_components/fast

# Special rule for the target install
install: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Install the project..."
	/usr/bin/cmake -P cmake_install.cmake
.PHONY : install

# Special rule for the target install
install/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Install the project..."
	/usr/bin/cmake -P cmake_install.cmake
.PHONY : install/fast

# Special rule for the target install/local
install/local: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Installing only the local directory..."
	/usr/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local

# Special rule for the target install/local
install/local/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Installing only the local directory..."
	/usr/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local/fast

# Special rule for the target install/strip
install/strip: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Installing the project stripped..."
	/usr/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip

# Special rule for the target install/strip
install/strip/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Installing the project stripped..."
	/usr/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip/fast

# The main all target
all: cmake_check_build_system
	cd /home/<USER>/EnergyStorage/AI_Box && $(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/EnergyStorage/AI_Box/CMakeFiles /home/<USER>/EnergyStorage/AI_Box/apilib/dep/fmt//CMakeFiles/progress.marks
	cd /home/<USER>/EnergyStorage/AI_Box && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 apilib/dep/fmt/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/EnergyStorage/AI_Box/CMakeFiles 0
.PHONY : all

# The main clean target
clean:
	cd /home/<USER>/EnergyStorage/AI_Box && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 apilib/dep/fmt/clean
.PHONY : clean

# The main clean target
clean/fast: clean
.PHONY : clean/fast

# Prepare targets for installation.
preinstall: all
	cd /home/<USER>/EnergyStorage/AI_Box && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 apilib/dep/fmt/preinstall
.PHONY : preinstall

# Prepare targets for installation.
preinstall/fast:
	cd /home/<USER>/EnergyStorage/AI_Box && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 apilib/dep/fmt/preinstall
.PHONY : preinstall/fast

# clear depends
depend:
	cd /home/<USER>/EnergyStorage/AI_Box && $(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 1
.PHONY : depend

# Convenience name for target.
apilib/dep/fmt/CMakeFiles/fmt.dir/rule:
	cd /home/<USER>/EnergyStorage/AI_Box && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 apilib/dep/fmt/CMakeFiles/fmt.dir/rule
.PHONY : apilib/dep/fmt/CMakeFiles/fmt.dir/rule

# Convenience name for target.
fmt: apilib/dep/fmt/CMakeFiles/fmt.dir/rule
.PHONY : fmt

# fast build rule for target.
fmt/fast:
	cd /home/<USER>/EnergyStorage/AI_Box && $(MAKE) $(MAKESILENT) -f apilib/dep/fmt/CMakeFiles/fmt.dir/build.make apilib/dep/fmt/CMakeFiles/fmt.dir/build
.PHONY : fmt/fast

fmt/format.o: fmt/format.cc.o
.PHONY : fmt/format.o

# target to build an object file
fmt/format.cc.o:
	cd /home/<USER>/EnergyStorage/AI_Box && $(MAKE) $(MAKESILENT) -f apilib/dep/fmt/CMakeFiles/fmt.dir/build.make apilib/dep/fmt/CMakeFiles/fmt.dir/fmt/format.cc.o
.PHONY : fmt/format.cc.o

fmt/format.i: fmt/format.cc.i
.PHONY : fmt/format.i

# target to preprocess a source file
fmt/format.cc.i:
	cd /home/<USER>/EnergyStorage/AI_Box && $(MAKE) $(MAKESILENT) -f apilib/dep/fmt/CMakeFiles/fmt.dir/build.make apilib/dep/fmt/CMakeFiles/fmt.dir/fmt/format.cc.i
.PHONY : fmt/format.cc.i

fmt/format.s: fmt/format.cc.s
.PHONY : fmt/format.s

# target to generate assembly for a file
fmt/format.cc.s:
	cd /home/<USER>/EnergyStorage/AI_Box && $(MAKE) $(MAKESILENT) -f apilib/dep/fmt/CMakeFiles/fmt.dir/build.make apilib/dep/fmt/CMakeFiles/fmt.dir/fmt/format.cc.s
.PHONY : fmt/format.cc.s

fmt/ostream.o: fmt/ostream.cc.o
.PHONY : fmt/ostream.o

# target to build an object file
fmt/ostream.cc.o:
	cd /home/<USER>/EnergyStorage/AI_Box && $(MAKE) $(MAKESILENT) -f apilib/dep/fmt/CMakeFiles/fmt.dir/build.make apilib/dep/fmt/CMakeFiles/fmt.dir/fmt/ostream.cc.o
.PHONY : fmt/ostream.cc.o

fmt/ostream.i: fmt/ostream.cc.i
.PHONY : fmt/ostream.i

# target to preprocess a source file
fmt/ostream.cc.i:
	cd /home/<USER>/EnergyStorage/AI_Box && $(MAKE) $(MAKESILENT) -f apilib/dep/fmt/CMakeFiles/fmt.dir/build.make apilib/dep/fmt/CMakeFiles/fmt.dir/fmt/ostream.cc.i
.PHONY : fmt/ostream.cc.i

fmt/ostream.s: fmt/ostream.cc.s
.PHONY : fmt/ostream.s

# target to generate assembly for a file
fmt/ostream.cc.s:
	cd /home/<USER>/EnergyStorage/AI_Box && $(MAKE) $(MAKESILENT) -f apilib/dep/fmt/CMakeFiles/fmt.dir/build.make apilib/dep/fmt/CMakeFiles/fmt.dir/fmt/ostream.cc.s
.PHONY : fmt/ostream.cc.s

fmt/posix.o: fmt/posix.cc.o
.PHONY : fmt/posix.o

# target to build an object file
fmt/posix.cc.o:
	cd /home/<USER>/EnergyStorage/AI_Box && $(MAKE) $(MAKESILENT) -f apilib/dep/fmt/CMakeFiles/fmt.dir/build.make apilib/dep/fmt/CMakeFiles/fmt.dir/fmt/posix.cc.o
.PHONY : fmt/posix.cc.o

fmt/posix.i: fmt/posix.cc.i
.PHONY : fmt/posix.i

# target to preprocess a source file
fmt/posix.cc.i:
	cd /home/<USER>/EnergyStorage/AI_Box && $(MAKE) $(MAKESILENT) -f apilib/dep/fmt/CMakeFiles/fmt.dir/build.make apilib/dep/fmt/CMakeFiles/fmt.dir/fmt/posix.cc.i
.PHONY : fmt/posix.cc.i

fmt/posix.s: fmt/posix.cc.s
.PHONY : fmt/posix.s

# target to generate assembly for a file
fmt/posix.cc.s:
	cd /home/<USER>/EnergyStorage/AI_Box && $(MAKE) $(MAKESILENT) -f apilib/dep/fmt/CMakeFiles/fmt.dir/build.make apilib/dep/fmt/CMakeFiles/fmt.dir/fmt/posix.cc.s
.PHONY : fmt/posix.cc.s

fmt/printf.o: fmt/printf.cc.o
.PHONY : fmt/printf.o

# target to build an object file
fmt/printf.cc.o:
	cd /home/<USER>/EnergyStorage/AI_Box && $(MAKE) $(MAKESILENT) -f apilib/dep/fmt/CMakeFiles/fmt.dir/build.make apilib/dep/fmt/CMakeFiles/fmt.dir/fmt/printf.cc.o
.PHONY : fmt/printf.cc.o

fmt/printf.i: fmt/printf.cc.i
.PHONY : fmt/printf.i

# target to preprocess a source file
fmt/printf.cc.i:
	cd /home/<USER>/EnergyStorage/AI_Box && $(MAKE) $(MAKESILENT) -f apilib/dep/fmt/CMakeFiles/fmt.dir/build.make apilib/dep/fmt/CMakeFiles/fmt.dir/fmt/printf.cc.i
.PHONY : fmt/printf.cc.i

fmt/printf.s: fmt/printf.cc.s
.PHONY : fmt/printf.s

# target to generate assembly for a file
fmt/printf.cc.s:
	cd /home/<USER>/EnergyStorage/AI_Box && $(MAKE) $(MAKESILENT) -f apilib/dep/fmt/CMakeFiles/fmt.dir/build.make apilib/dep/fmt/CMakeFiles/fmt.dir/fmt/printf.cc.s
.PHONY : fmt/printf.cc.s

# Help Target
help:
	@echo "The following are some of the valid targets for this Makefile:"
	@echo "... all (the default if no target is provided)"
	@echo "... clean"
	@echo "... depend"
	@echo "... edit_cache"
	@echo "... install"
	@echo "... install/local"
	@echo "... install/strip"
	@echo "... list_install_components"
	@echo "... rebuild_cache"
	@echo "... fmt"
	@echo "... fmt/format.o"
	@echo "... fmt/format.i"
	@echo "... fmt/format.s"
	@echo "... fmt/ostream.o"
	@echo "... fmt/ostream.i"
	@echo "... fmt/ostream.s"
	@echo "... fmt/posix.o"
	@echo "... fmt/posix.i"
	@echo "... fmt/posix.s"
	@echo "... fmt/printf.o"
	@echo "... fmt/printf.i"
	@echo "... fmt/printf.s"
.PHONY : help



#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	cd /home/<USER>/EnergyStorage/AI_Box && $(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system

