# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.28

# Default target executed when no arguments are given to make.
default_target: all
.PHONY : default_target

# Allow only one "make -f Makefile2" at a time, but pass parallelism.
.NOTPARALLEL:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Produce verbose output by default.
VERBOSE = 1

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/EnergyStorage/AI_Box

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/EnergyStorage/AI_Box

#=============================================================================
# Targets provided globally by CMake.

# Special rule for the target edit_cache
edit_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "No interactive CMake dialog available..."
	/usr/bin/cmake -E echo No\ interactive\ CMake\ dialog\ available.
.PHONY : edit_cache

# Special rule for the target edit_cache
edit_cache/fast: edit_cache
.PHONY : edit_cache/fast

# Special rule for the target rebuild_cache
rebuild_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Running CMake to regenerate build system..."
	/usr/bin/cmake --regenerate-during-build -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : rebuild_cache

# Special rule for the target rebuild_cache
rebuild_cache/fast: rebuild_cache
.PHONY : rebuild_cache/fast

# Special rule for the target list_install_components
list_install_components:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Available install components are: \"Unspecified\""
.PHONY : list_install_components

# Special rule for the target list_install_components
list_install_components/fast: list_install_components
.PHONY : list_install_components/fast

# Special rule for the target install
install: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Install the project..."
	/usr/bin/cmake -P cmake_install.cmake
.PHONY : install

# Special rule for the target install
install/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Install the project..."
	/usr/bin/cmake -P cmake_install.cmake
.PHONY : install/fast

# Special rule for the target install/local
install/local: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Installing only the local directory..."
	/usr/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local

# Special rule for the target install/local
install/local/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Installing only the local directory..."
	/usr/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local/fast

# Special rule for the target install/strip
install/strip: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Installing the project stripped..."
	/usr/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip

# Special rule for the target install/strip
install/strip/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Installing the project stripped..."
	/usr/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip/fast

# The main all target
all: cmake_check_build_system
	cd /home/<USER>/EnergyStorage/AI_Box && $(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/EnergyStorage/AI_Box/CMakeFiles /home/<USER>/EnergyStorage/AI_Box/apilib/db//CMakeFiles/progress.marks
	cd /home/<USER>/EnergyStorage/AI_Box && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 apilib/db/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/EnergyStorage/AI_Box/CMakeFiles 0
.PHONY : all

# The main clean target
clean:
	cd /home/<USER>/EnergyStorage/AI_Box && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 apilib/db/clean
.PHONY : clean

# The main clean target
clean/fast: clean
.PHONY : clean/fast

# Prepare targets for installation.
preinstall: all
	cd /home/<USER>/EnergyStorage/AI_Box && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 apilib/db/preinstall
.PHONY : preinstall

# Prepare targets for installation.
preinstall/fast:
	cd /home/<USER>/EnergyStorage/AI_Box && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 apilib/db/preinstall
.PHONY : preinstall/fast

# clear depends
depend:
	cd /home/<USER>/EnergyStorage/AI_Box && $(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 1
.PHONY : depend

# Convenience name for target.
apilib/db/CMakeFiles/DataBase.dir/rule:
	cd /home/<USER>/EnergyStorage/AI_Box && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 apilib/db/CMakeFiles/DataBase.dir/rule
.PHONY : apilib/db/CMakeFiles/DataBase.dir/rule

# Convenience name for target.
DataBase: apilib/db/CMakeFiles/DataBase.dir/rule
.PHONY : DataBase

# fast build rule for target.
DataBase/fast:
	cd /home/<USER>/EnergyStorage/AI_Box && $(MAKE) $(MAKESILENT) -f apilib/db/CMakeFiles/DataBase.dir/build.make apilib/db/CMakeFiles/DataBase.dir/build
.PHONY : DataBase/fast

DBExports.o: DBExports.cpp.o
.PHONY : DBExports.o

# target to build an object file
DBExports.cpp.o:
	cd /home/<USER>/EnergyStorage/AI_Box && $(MAKE) $(MAKESILENT) -f apilib/db/CMakeFiles/DataBase.dir/build.make apilib/db/CMakeFiles/DataBase.dir/DBExports.cpp.o
.PHONY : DBExports.cpp.o

DBExports.i: DBExports.cpp.i
.PHONY : DBExports.i

# target to preprocess a source file
DBExports.cpp.i:
	cd /home/<USER>/EnergyStorage/AI_Box && $(MAKE) $(MAKESILENT) -f apilib/db/CMakeFiles/DataBase.dir/build.make apilib/db/CMakeFiles/DataBase.dir/DBExports.cpp.i
.PHONY : DBExports.cpp.i

DBExports.s: DBExports.cpp.s
.PHONY : DBExports.s

# target to generate assembly for a file
DBExports.cpp.s:
	cd /home/<USER>/EnergyStorage/AI_Box && $(MAKE) $(MAKESILENT) -f apilib/db/CMakeFiles/DataBase.dir/build.make apilib/db/CMakeFiles/DataBase.dir/DBExports.cpp.s
.PHONY : DBExports.cpp.s

DBUpdater.o: DBUpdater.cpp.o
.PHONY : DBUpdater.o

# target to build an object file
DBUpdater.cpp.o:
	cd /home/<USER>/EnergyStorage/AI_Box && $(MAKE) $(MAKESILENT) -f apilib/db/CMakeFiles/DataBase.dir/build.make apilib/db/CMakeFiles/DataBase.dir/DBUpdater.cpp.o
.PHONY : DBUpdater.cpp.o

DBUpdater.i: DBUpdater.cpp.i
.PHONY : DBUpdater.i

# target to preprocess a source file
DBUpdater.cpp.i:
	cd /home/<USER>/EnergyStorage/AI_Box && $(MAKE) $(MAKESILENT) -f apilib/db/CMakeFiles/DataBase.dir/build.make apilib/db/CMakeFiles/DataBase.dir/DBUpdater.cpp.i
.PHONY : DBUpdater.cpp.i

DBUpdater.s: DBUpdater.cpp.s
.PHONY : DBUpdater.s

# target to generate assembly for a file
DBUpdater.cpp.s:
	cd /home/<USER>/EnergyStorage/AI_Box && $(MAKE) $(MAKESILENT) -f apilib/db/CMakeFiles/DataBase.dir/build.make apilib/db/CMakeFiles/DataBase.dir/DBUpdater.cpp.s
.PHONY : DBUpdater.cpp.s

DBWorker.o: DBWorker.cpp.o
.PHONY : DBWorker.o

# target to build an object file
DBWorker.cpp.o:
	cd /home/<USER>/EnergyStorage/AI_Box && $(MAKE) $(MAKESILENT) -f apilib/db/CMakeFiles/DataBase.dir/build.make apilib/db/CMakeFiles/DataBase.dir/DBWorker.cpp.o
.PHONY : DBWorker.cpp.o

DBWorker.i: DBWorker.cpp.i
.PHONY : DBWorker.i

# target to preprocess a source file
DBWorker.cpp.i:
	cd /home/<USER>/EnergyStorage/AI_Box && $(MAKE) $(MAKESILENT) -f apilib/db/CMakeFiles/DataBase.dir/build.make apilib/db/CMakeFiles/DataBase.dir/DBWorker.cpp.i
.PHONY : DBWorker.cpp.i

DBWorker.s: DBWorker.cpp.s
.PHONY : DBWorker.s

# target to generate assembly for a file
DBWorker.cpp.s:
	cd /home/<USER>/EnergyStorage/AI_Box && $(MAKE) $(MAKESILENT) -f apilib/db/CMakeFiles/DataBase.dir/build.make apilib/db/CMakeFiles/DataBase.dir/DBWorker.cpp.s
.PHONY : DBWorker.cpp.s

DBWorkerPool.o: DBWorkerPool.cpp.o
.PHONY : DBWorkerPool.o

# target to build an object file
DBWorkerPool.cpp.o:
	cd /home/<USER>/EnergyStorage/AI_Box && $(MAKE) $(MAKESILENT) -f apilib/db/CMakeFiles/DataBase.dir/build.make apilib/db/CMakeFiles/DataBase.dir/DBWorkerPool.cpp.o
.PHONY : DBWorkerPool.cpp.o

DBWorkerPool.i: DBWorkerPool.cpp.i
.PHONY : DBWorkerPool.i

# target to preprocess a source file
DBWorkerPool.cpp.i:
	cd /home/<USER>/EnergyStorage/AI_Box && $(MAKE) $(MAKESILENT) -f apilib/db/CMakeFiles/DataBase.dir/build.make apilib/db/CMakeFiles/DataBase.dir/DBWorkerPool.cpp.i
.PHONY : DBWorkerPool.cpp.i

DBWorkerPool.s: DBWorkerPool.cpp.s
.PHONY : DBWorkerPool.s

# target to generate assembly for a file
DBWorkerPool.cpp.s:
	cd /home/<USER>/EnergyStorage/AI_Box && $(MAKE) $(MAKESILENT) -f apilib/db/CMakeFiles/DataBase.dir/build.make apilib/db/CMakeFiles/DataBase.dir/DBWorkerPool.cpp.s
.PHONY : DBWorkerPool.cpp.s

Field.o: Field.cpp.o
.PHONY : Field.o

# target to build an object file
Field.cpp.o:
	cd /home/<USER>/EnergyStorage/AI_Box && $(MAKE) $(MAKESILENT) -f apilib/db/CMakeFiles/DataBase.dir/build.make apilib/db/CMakeFiles/DataBase.dir/Field.cpp.o
.PHONY : Field.cpp.o

Field.i: Field.cpp.i
.PHONY : Field.i

# target to preprocess a source file
Field.cpp.i:
	cd /home/<USER>/EnergyStorage/AI_Box && $(MAKE) $(MAKESILENT) -f apilib/db/CMakeFiles/DataBase.dir/build.make apilib/db/CMakeFiles/DataBase.dir/Field.cpp.i
.PHONY : Field.cpp.i

Field.s: Field.cpp.s
.PHONY : Field.s

# target to generate assembly for a file
Field.cpp.s:
	cd /home/<USER>/EnergyStorage/AI_Box && $(MAKE) $(MAKESILENT) -f apilib/db/CMakeFiles/DataBase.dir/build.make apilib/db/CMakeFiles/DataBase.dir/Field.cpp.s
.PHONY : Field.cpp.s

Implementation/LogonDatabase.o: Implementation/LogonDatabase.cpp.o
.PHONY : Implementation/LogonDatabase.o

# target to build an object file
Implementation/LogonDatabase.cpp.o:
	cd /home/<USER>/EnergyStorage/AI_Box && $(MAKE) $(MAKESILENT) -f apilib/db/CMakeFiles/DataBase.dir/build.make apilib/db/CMakeFiles/DataBase.dir/Implementation/LogonDatabase.cpp.o
.PHONY : Implementation/LogonDatabase.cpp.o

Implementation/LogonDatabase.i: Implementation/LogonDatabase.cpp.i
.PHONY : Implementation/LogonDatabase.i

# target to preprocess a source file
Implementation/LogonDatabase.cpp.i:
	cd /home/<USER>/EnergyStorage/AI_Box && $(MAKE) $(MAKESILENT) -f apilib/db/CMakeFiles/DataBase.dir/build.make apilib/db/CMakeFiles/DataBase.dir/Implementation/LogonDatabase.cpp.i
.PHONY : Implementation/LogonDatabase.cpp.i

Implementation/LogonDatabase.s: Implementation/LogonDatabase.cpp.s
.PHONY : Implementation/LogonDatabase.s

# target to generate assembly for a file
Implementation/LogonDatabase.cpp.s:
	cd /home/<USER>/EnergyStorage/AI_Box && $(MAKE) $(MAKESILENT) -f apilib/db/CMakeFiles/DataBase.dir/build.make apilib/db/CMakeFiles/DataBase.dir/Implementation/LogonDatabase.cpp.s
.PHONY : Implementation/LogonDatabase.cpp.s

Implementation/TreasureDatabase.o: Implementation/TreasureDatabase.cpp.o
.PHONY : Implementation/TreasureDatabase.o

# target to build an object file
Implementation/TreasureDatabase.cpp.o:
	cd /home/<USER>/EnergyStorage/AI_Box && $(MAKE) $(MAKESILENT) -f apilib/db/CMakeFiles/DataBase.dir/build.make apilib/db/CMakeFiles/DataBase.dir/Implementation/TreasureDatabase.cpp.o
.PHONY : Implementation/TreasureDatabase.cpp.o

Implementation/TreasureDatabase.i: Implementation/TreasureDatabase.cpp.i
.PHONY : Implementation/TreasureDatabase.i

# target to preprocess a source file
Implementation/TreasureDatabase.cpp.i:
	cd /home/<USER>/EnergyStorage/AI_Box && $(MAKE) $(MAKESILENT) -f apilib/db/CMakeFiles/DataBase.dir/build.make apilib/db/CMakeFiles/DataBase.dir/Implementation/TreasureDatabase.cpp.i
.PHONY : Implementation/TreasureDatabase.cpp.i

Implementation/TreasureDatabase.s: Implementation/TreasureDatabase.cpp.s
.PHONY : Implementation/TreasureDatabase.s

# target to generate assembly for a file
Implementation/TreasureDatabase.cpp.s:
	cd /home/<USER>/EnergyStorage/AI_Box && $(MAKE) $(MAKESILENT) -f apilib/db/CMakeFiles/DataBase.dir/build.make apilib/db/CMakeFiles/DataBase.dir/Implementation/TreasureDatabase.cpp.s
.PHONY : Implementation/TreasureDatabase.cpp.s

MySQLConnection.o: MySQLConnection.cpp.o
.PHONY : MySQLConnection.o

# target to build an object file
MySQLConnection.cpp.o:
	cd /home/<USER>/EnergyStorage/AI_Box && $(MAKE) $(MAKESILENT) -f apilib/db/CMakeFiles/DataBase.dir/build.make apilib/db/CMakeFiles/DataBase.dir/MySQLConnection.cpp.o
.PHONY : MySQLConnection.cpp.o

MySQLConnection.i: MySQLConnection.cpp.i
.PHONY : MySQLConnection.i

# target to preprocess a source file
MySQLConnection.cpp.i:
	cd /home/<USER>/EnergyStorage/AI_Box && $(MAKE) $(MAKESILENT) -f apilib/db/CMakeFiles/DataBase.dir/build.make apilib/db/CMakeFiles/DataBase.dir/MySQLConnection.cpp.i
.PHONY : MySQLConnection.cpp.i

MySQLConnection.s: MySQLConnection.cpp.s
.PHONY : MySQLConnection.s

# target to generate assembly for a file
MySQLConnection.cpp.s:
	cd /home/<USER>/EnergyStorage/AI_Box && $(MAKE) $(MAKESILENT) -f apilib/db/CMakeFiles/DataBase.dir/build.make apilib/db/CMakeFiles/DataBase.dir/MySQLConnection.cpp.s
.PHONY : MySQLConnection.cpp.s

PreparedStatement.o: PreparedStatement.cpp.o
.PHONY : PreparedStatement.o

# target to build an object file
PreparedStatement.cpp.o:
	cd /home/<USER>/EnergyStorage/AI_Box && $(MAKE) $(MAKESILENT) -f apilib/db/CMakeFiles/DataBase.dir/build.make apilib/db/CMakeFiles/DataBase.dir/PreparedStatement.cpp.o
.PHONY : PreparedStatement.cpp.o

PreparedStatement.i: PreparedStatement.cpp.i
.PHONY : PreparedStatement.i

# target to preprocess a source file
PreparedStatement.cpp.i:
	cd /home/<USER>/EnergyStorage/AI_Box && $(MAKE) $(MAKESILENT) -f apilib/db/CMakeFiles/DataBase.dir/build.make apilib/db/CMakeFiles/DataBase.dir/PreparedStatement.cpp.i
.PHONY : PreparedStatement.cpp.i

PreparedStatement.s: PreparedStatement.cpp.s
.PHONY : PreparedStatement.s

# target to generate assembly for a file
PreparedStatement.cpp.s:
	cd /home/<USER>/EnergyStorage/AI_Box && $(MAKE) $(MAKESILENT) -f apilib/db/CMakeFiles/DataBase.dir/build.make apilib/db/CMakeFiles/DataBase.dir/PreparedStatement.cpp.s
.PHONY : PreparedStatement.cpp.s

QueryCallback.o: QueryCallback.cpp.o
.PHONY : QueryCallback.o

# target to build an object file
QueryCallback.cpp.o:
	cd /home/<USER>/EnergyStorage/AI_Box && $(MAKE) $(MAKESILENT) -f apilib/db/CMakeFiles/DataBase.dir/build.make apilib/db/CMakeFiles/DataBase.dir/QueryCallback.cpp.o
.PHONY : QueryCallback.cpp.o

QueryCallback.i: QueryCallback.cpp.i
.PHONY : QueryCallback.i

# target to preprocess a source file
QueryCallback.cpp.i:
	cd /home/<USER>/EnergyStorage/AI_Box && $(MAKE) $(MAKESILENT) -f apilib/db/CMakeFiles/DataBase.dir/build.make apilib/db/CMakeFiles/DataBase.dir/QueryCallback.cpp.i
.PHONY : QueryCallback.cpp.i

QueryCallback.s: QueryCallback.cpp.s
.PHONY : QueryCallback.s

# target to generate assembly for a file
QueryCallback.cpp.s:
	cd /home/<USER>/EnergyStorage/AI_Box && $(MAKE) $(MAKESILENT) -f apilib/db/CMakeFiles/DataBase.dir/build.make apilib/db/CMakeFiles/DataBase.dir/QueryCallback.cpp.s
.PHONY : QueryCallback.cpp.s

QueryResult.o: QueryResult.cpp.o
.PHONY : QueryResult.o

# target to build an object file
QueryResult.cpp.o:
	cd /home/<USER>/EnergyStorage/AI_Box && $(MAKE) $(MAKESILENT) -f apilib/db/CMakeFiles/DataBase.dir/build.make apilib/db/CMakeFiles/DataBase.dir/QueryResult.cpp.o
.PHONY : QueryResult.cpp.o

QueryResult.i: QueryResult.cpp.i
.PHONY : QueryResult.i

# target to preprocess a source file
QueryResult.cpp.i:
	cd /home/<USER>/EnergyStorage/AI_Box && $(MAKE) $(MAKESILENT) -f apilib/db/CMakeFiles/DataBase.dir/build.make apilib/db/CMakeFiles/DataBase.dir/QueryResult.cpp.i
.PHONY : QueryResult.cpp.i

QueryResult.s: QueryResult.cpp.s
.PHONY : QueryResult.s

# target to generate assembly for a file
QueryResult.cpp.s:
	cd /home/<USER>/EnergyStorage/AI_Box && $(MAKE) $(MAKESILENT) -f apilib/db/CMakeFiles/DataBase.dir/build.make apilib/db/CMakeFiles/DataBase.dir/QueryResult.cpp.s
.PHONY : QueryResult.cpp.s

# Help Target
help:
	@echo "The following are some of the valid targets for this Makefile:"
	@echo "... all (the default if no target is provided)"
	@echo "... clean"
	@echo "... depend"
	@echo "... edit_cache"
	@echo "... install"
	@echo "... install/local"
	@echo "... install/strip"
	@echo "... list_install_components"
	@echo "... rebuild_cache"
	@echo "... DataBase"
	@echo "... DBExports.o"
	@echo "... DBExports.i"
	@echo "... DBExports.s"
	@echo "... DBUpdater.o"
	@echo "... DBUpdater.i"
	@echo "... DBUpdater.s"
	@echo "... DBWorker.o"
	@echo "... DBWorker.i"
	@echo "... DBWorker.s"
	@echo "... DBWorkerPool.o"
	@echo "... DBWorkerPool.i"
	@echo "... DBWorkerPool.s"
	@echo "... Field.o"
	@echo "... Field.i"
	@echo "... Field.s"
	@echo "... Implementation/LogonDatabase.o"
	@echo "... Implementation/LogonDatabase.i"
	@echo "... Implementation/LogonDatabase.s"
	@echo "... Implementation/TreasureDatabase.o"
	@echo "... Implementation/TreasureDatabase.i"
	@echo "... Implementation/TreasureDatabase.s"
	@echo "... MySQLConnection.o"
	@echo "... MySQLConnection.i"
	@echo "... MySQLConnection.s"
	@echo "... PreparedStatement.o"
	@echo "... PreparedStatement.i"
	@echo "... PreparedStatement.s"
	@echo "... QueryCallback.o"
	@echo "... QueryCallback.i"
	@echo "... QueryCallback.s"
	@echo "... QueryResult.o"
	@echo "... QueryResult.i"
	@echo "... QueryResult.s"
.PHONY : help



#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	cd /home/<USER>/EnergyStorage/AI_Box && $(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system

