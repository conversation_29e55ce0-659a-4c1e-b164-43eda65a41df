#include "LogonDatabase.h"

namespace DB {
LogonDatabaseConnection::LogonDatabaseConnection(MySQLConnectionInfo &connInfo) : MySQLConnection(connInfo) {}

LogonDatabaseConnection::LogonDatabaseConnection(ProducerConsumerQueue<SQLOperation *> *q,
                                                 MySQLConnectionInfo &connInfo)
    : MySQLConnection(q, connInfo) {}

LogonDatabaseConnection::~LogonDatabaseConnection() {}

void LogonDatabaseConnection::DoPrepareStatements() {
    if (!m_reconnecting) {
        m_stmtStorage.resize(LOGON_MAX_STATEMENTS);
    }

    // PrepareStatement(LOGON_SEL_LIMIT_ADDRESS, "SELECT
    // enjoin_logon,enjoin_reg,UNIX_TIMESTAMP(expire_date),restore FROM
    // limit_address WHERE addr = ? and machine = ?", CONNECTION_SYNCH);
    // PrepareStatement(LOGON_UPD_LIMIT_ADDRESS, "UPDATE limit_address SET
    // enjoin_logon=?,enjoin_reg=?,collect_date=NOW(),restore=? WHERE addr = ?
    // and machine = ?", CONNECTION_SYNCH);

    // PrepareStatement(LOGON_SEL_GAME_ID, "SELECT gameid FROM platform_id_mgr",
    // CONNECTION_SYNCH); PrepareStatement(LOGON_UPD_GAME_ID, "UPDATE
    // platform_id_mgr SET gameid=gameid+1", CONNECTION_SYNCH);

    // PrepareStatement(LOGON_SEL_VISITOR_ACCOUNT, "SELECT
    // id,account,username,sha_pass_hash,face_url,service_limit,score FROM
    // account_info WHERE register_machine = ?", CONNECTION_SYNCH);
    // PrepareStatement(LOGON_INS_VISITOR_ACCOUNT, "INSERT INTO
    // account_info(account,username,sha_pass_hash,face_url,score,register_ip,register_machine)
    // VALUES (?,?,?,?,?,?,?)", CONNECTION_SYNCH);
    // PrepareStatement(LOGON_UPD_VISITOR_ACCOUNT, "UPDATE account_info SET
    // last_logon_ip=?,last_logon_date=NOW() WHERE register_machine = ?",
    // CONNECTION_SYNCH);

    // PrepareStatement(LOGON_UPD_GAME_WRITE_SCORE, "UPDATE account_info SET
    // score=? WHERE id=?", CONNECTION_SYNCH);

    PrepareStatement(AI_BOX_CHECK_ACCOUNT, "SELECT `id` , `name` , `pwd` FROM user WHERE `name` = ? and `pwd` = ?",
                     CONNECTION_SYNCH);

    PrepareStatement(AI_BOX_MODIFY_PWD, " UPDATE `user` SET `pwd` = ? WHERE `name` = ?;", CONNECTION_SYNCH);

    PrepareStatement(AI_BOX_DELETE_ALGORITHM, " DELETE FROM algorithm_table WHERE `id` IN (?);", CONNECTION_SYNCH);

    PrepareStatement(AI_BOX_INSERT_ALGORITHM, " insert into algorithm_table values(?,?,?,?,?,?,?,?,?,?);",
                     CONNECTION_SYNCH);

    PrepareStatement(AI_BOX_CAMERA_LIST,
                     " select `id`,`name`,`push_url`,`start` from input_source_table "
                     "where enable=1;",
                     CONNECTION_SYNCH);

    PrepareStatement(AI_BOX_CAMERA_COUNT, " select count(id) from input_source_table where enable=1;",
                     CONNECTION_SYNCH);

    PrepareStatement(AI_BOX_CAMERA_INFO_LIST,
                     " select `id`,`sid`,`brand`,`name`,`ip`,`user`,`pwd`,`protocol`,`stream_url`,`format`,"
                     "`tcp`,`record`,`start`,`online`,`push_url` from input_source_table "
                     "where `enable`=1 and `type`=? ORDER BY `id` DESC LIMIT ? , ?;",
                     CONNECTION_SYNCH);

    PrepareStatement(AI_BOX_ALGORITHM_INFO,
                     "SELECT `alg_id`,`cycle`, `dp`,`dp_id`,`interval`,`hasTimes`,`level`, "
                     "`people_count`,`thrs`,`vehicletype`,`vehs`,`lines`,`polygons`, `width`, "
                     "`height`,`wb` FROM camera_alg_conf where camera_id = ?;",
                     CONNECTION_SYNCH);

    PrepareStatement(AI_BOX_CAMERA_COUNT_BY_TYPE, "select count(id) from input_source_table where type=? and enable=1;",
                     CONNECTION_SYNCH);

    PrepareStatement(AI_BOX_CAMERA_COUNT_BY_START,
                     "select count(id) from input_source_table where type=? and "
                     "enable=1 and start=1;",
                     CONNECTION_SYNCH);

    PrepareStatement(AI_BOX_CAMERA_START, "update input_source_table set `start`=? where `id`=?;", CONNECTION_SYNCH);

    PrepareStatement(AI_BOX_CAMERA_ADD,
                     "insert into input_source_table(`type`,`sid`,`brand`,`name`,`ip`,`user`,`pwd`,"
                     "`protocol`,`stream_url`,`push_url`,`format`,`tcp`,`record`,`start`,`online`) "
                     "values(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?);",
                     CONNECTION_SYNCH);

    PrepareStatement(AI_BOX_CAMERA_ALG_CONF_ADD, "INSERT INTO camera_alg_conf (\
                            `camera_id`,\
                            `alg_id`,\
                            `cycle`,\
                            `dp`,\
                            `dp_id`,\
                            `interval`,\
                            `hasTimes`,\
                            `level`,\
                            `people_count`,\
                            `thrs`,\
                            `vehicletype`,\
                            `vehs`,\
                            `lines`,\
                            `polygons`,\
                            `width`,\
                            `height`,\
                            `wb`\
                        )\
                        VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?);",
                     CONNECTION_SYNCH);

    PrepareStatement(AI_BOX_CAMERA_PUSH_URL, "update input_source_table set push_url=? where id=?;", CONNECTION_SYNCH);

    PrepareStatement(AI_BOX_CAMERA_REMOVE, "update input_source_table set `enable` = 0 where id = ? and `enable`=1;",
                     CONNECTION_SYNCH);

    PrepareStatement(AI_BOX_CAMERA_ALG_REMOVE, "delete from camera_alg_conf where `camera_id`=? ", CONNECTION_SYNCH);

    PrepareStatement(AI_BOX_CAMERA_BATCH_REMOVE, "update input_source_table set `enable`=0 where id \
                     in ( ? ) ",
                     CONNECTION_SYNCH);

    // PrepareStatement(AI_BOX_CAMERA_EDIT, "update input_source_table set ? where id = ?", CONNECTION_SYNCH);

    PrepareStatement(AI_BOX_CAMERA_ALG_ID, "select alg_id from  camera_alg_conf where camera_id = ? ",
                     CONNECTION_SYNCH);

    PrepareStatement(AI_BOX_CAMERA_ALG_CONF_UPDATE, "update camera_alg_conf set `cycle` = \
                      ?,`dp` = ?,`dp_id` = ?,`interval` = ?,`hasTimes` = ?,`level` = \
                      ?,`people_count` = ?,`thrs` = ?,`vehicletype` = ? ,`vehs` = \
                      ?,`lines` = ?,`polygons` = ?,`width` = ?, `height`=? , `wb`=? where\
                      camera_id = ? and alg_id = ?; ",
                     CONNECTION_SYNCH);

    PrepareStatement(AI_BOX_CAMERA_ALG_CONF_DELETE,
                     "delete from camera_alg_conf where "
                     "`camera_id`= ?  and alg_id in ( ? ) ",
                     CONNECTION_SYNCH);

    PrepareStatement(AI_BOX_CAMERA_LINES_DELETE, "delete from overline_log_table where camera_id = ? ",
                     CONNECTION_SYNCH);

    PrepareStatement(AI_BOX_CAMERA_LINES_ADD, "insert into overline_log_table "
                                              "(`camera_id`,`camera_name`,`line_name`,`start_time`,`end_time`,`type`,`"
                                              "direction`,`count`,`start_points_x`,`start_points_y`,`end_points_x`,`"
                                              "end_points_y`,`description`,`enable`) VALUES "
                     //  "(?,\"?\",\"?\",?,?,?,?,?,\"?\",\"?\",\"?\",\"?\",\"?\",?); ",
                     "(?,?,?,?,?,?,?,?,?,?,?,?,?,?); ",
                     CONNECTION_SYNCH);

    PrepareStatement(AI_BOX_BASE_BANK_SUB_GROUP, "select `id`, `type`, `name`,\
                   `param` from base_bank_sub_group_table where group_id=?",
                     CONNECTION_SYNCH);

    PrepareStatement(AI_BOX_BASE_BANK_SUB_GROUP_INSERT,
                     "insert into base_bank_sub_group_table"
                     "(`group_id`,`type`,`name`,`param`) values(?,?,?,?);",
                     CONNECTION_SYNCH);
    PrepareStatement(AI_BOX_BASE_BANK_SUB_GROUP_ID, "SELECT MAX(id) FROM base_bank_sub_group_table;", CONNECTION_SYNCH);

    PrepareStatement(AI_BOX_BASE_BANK_SUB_GROUP_DELETE, "delete from base_bank_sub_group_table where id=?;",
                     CONNECTION_SYNCH);

    PrepareStatement(AI_BOX_BASE_BANK_SUB_GROUP_COUNT,
                     "select count(id) from base_bank_table where `enable`=1 and "
                     "`group_id` = ? and `sub_group_id` = ?;",
                     CONNECTION_SYNCH);

    // PrepareStatement(AI_BOX_BASE_BANK,
    //                  "select count(id) from base_bank_table where `enable`=1 and "
    //                  "`group_id` = ? and `sub_group_id` = ? ? ?;",
                    //  CONNECTION_SYNCH);
    PrepareStatement(AI_BOX_BASE_BANK_DELETE, "update base_bank_table set `enable`=0 where `id`= ? and `enable`=1;",
                     CONNECTION_SYNCH);

    PrepareStatement(AI_BOX_BASE_BANK_BATCH_REMOVE, "update  base_bank_table set `enable`=0 where `id` in ( ? );",
                     CONNECTION_SYNCH);

    PrepareStatement(AI_BOX_ALGORITHM, "select count(id) from algorithm_table  where `enable`=1 and `group_id`=?;",
                     CONNECTION_SYNCH);

    // PrepareStatement(AI_BOX_ALGORITHM_BY_GROUP,
    //                  "select `id`,`name`,`describe`,`param`,`version` , `dlib` , `mask` "
    //                  "from algorithm_table where enable=1 and group_id=? ? ?;",
    //                  CONNECTION_SYNCH);

    PrepareStatement(AI_BOX_CAMERA_ALG_CONF_LIST,
                     "SELECT `camera_id`,`alg_id`,`cycle`, `dp`,`dp_id`,`interval`,`hasTimes`,`level`, "
                     "`people_count`,`thrs`,`vehicletype`,`vehs`,`lines`,`polygons`, `width`, `height`,`wb` FROM "
                     "camera_alg_conf where `camera_id` = ?;",
                     CONNECTION_SYNCH);

    // PrepareStatement(AI_BOX_ALGORITHM_EDIT, "update algorithm_table set ? where id = ? and enable = 1;",
    //                  CONNECTION_SYNCH);

    PrepareStatement(AI_BOX_ALGORITHM_PARAM, "select param from algorithm_table where `id`= ? and`enable`= 1; ",
                     CONNECTION_SYNCH);

    PrepareStatement(AI_BOX_ALARM,
                     "select a.id,b.name,ip,date,time,a.type,level,is_do,image,video "
                     " from alarm_log_table a join input_source_table b on a.camera_id=b.id "
                     " where a.enable=1 order by a.id desc LIMIT ?; ",
                     CONNECTION_SYNCH);

    PrepareStatement(AI_BOX_ALARM_COUNT,
                     "select count(a.id) "
                     "from alarm_log_table a "
                     "join input_source_table b on a.camera_id=b.id "
                     "where a.enable=1;",
                     CONNECTION_SYNCH);

    // PrepareStatement(AI_BOX_ALARM_SELECT,
    //                  "select a.id,b.name,b.ip,a.date,a.time,a.type,a.level,a.is_do,a.image,a.video "
    //                  " from alarm_log_table a "
    //                  " join input_source_table b on a.camera_id=b.id "
    //                  " where a.enable=1 ? ?",
    //                  CONNECTION_SYNCH);

    PrepareStatement(AI_BOX_ALARM_REMOVE, "update alarm_log_table set `enable`=0 where `id`=? and `enable`=1",
                     CONNECTION_SYNCH);

    PrepareStatement(AI_BOX_ALARM_SOLVE, "update alarm_log_table set `is_do`=1 where `id`=? and `enable`=1",
                     CONNECTION_SYNCH);

    // PrepareStatement(AI_BOX_ALARM_COUNT_DATE, "select count(id) from alarm_log_table where enable=1 and date=?",
    //                  CONNECTION_SYNCH);

    PrepareStatement(AI_BOX_ALARM_TOTAL_COUNT, "select count(id) from alarm_log_table where enable=1 ",
                     CONNECTION_SYNCH);

    PrepareStatement(AI_BOX_ALARM_GROUP_BY_DAY,
                     "select date,count(id) from alarm_log_table where enable=1 and date>=? group by date ",
                     CONNECTION_SYNCH);

    PrepareStatement(AI_BOX_ALARM_GROUP_BY_SOURCE,
                     "select b.name as name,count(a.id) from alarm_log_table as a "
                     "join input_source_table as b on b.id=a.camera_id "
                     "where a.enable=1 and date>=? group by name  ",
                     CONNECTION_SYNCH);

    PrepareStatement(AI_BOX_ALARM_GROUP_BY_ALGORITHM,
                     "select b.name as name,count(a.id) from alarm_log_table as a "
                     "join algorithm_table as b on b.id=a.type "
                     "where a.enable=1 and date>=? group by name",
                     CONNECTION_SYNCH);
    PrepareStatement(AI_BOX_CAMERA_ADD_ID, "SELECT MAX(id) FROM input_source_table;", CONNECTION_SYNCH);

    PrepareStatement(AI_BOX_ALARM_BATCH_DELETE, "update alarm_log_table set `enable`=0 where `id` in ( ? )",
                     CONNECTION_SYNCH);

    // PrepareStatement(AI_BOX_ALARM_CROSSLINE_COUNT,
    //                  "select count(a.id) "
    //                  "from alarm_log_table a "
    //                  "join input_source_table b on a.camera_id=b.id "
    //                  "where a.enable=1 ? ",
    //                  CONNECTION_SYNCH);

    // PrepareStatement(AI_BOX_ALARM_CROSSLINE_SELECT,
    //                  "select a.id,a.camera_id,b.name,b.ip,a.creat_time,a.agl_id,a.direction,a.count,a.is_do,a.image "
    //                  " from overline_details_log_table a "
    //                  " join input_source_table b on a.camera_id=b.id "
    //                  " where a.enable=1 ? ?",
    //                  CONNECTION_SYNCH);

    // PrepareStatement(AI_BOX_OVERLINE_LOG_COUNT, "select count(*) from ( ? ) as subquery ", CONNECTION_SYNCH);

    // PrepareStatement(AI_BOX_OVERLINE_LOG_INFO,
    //                  "select a.id,b.name,b.ip,a.creat_time,a.agl_id,c.direction,"
    //                  "a.image,sum(a.count) "
    //                  "from overline_details_log_table as a "
    //                  "left join overline_log_table as c on a.camera_id=c.camera_id and a.line_id = c.id and "
    //                  "a.agl_id = c.type  left join   input_source_table as b on a.camera_id=b.id "
    //                  "where b.enable=1 ? ? ? ",
    //                  CONNECTION_SYNCH);

    PrepareStatement(AI_BOX_OVERLINE_LOG_ZERO, "delete from overline_log_table  where start_time <= ? and camera_id=?",
                     CONNECTION_SYNCH);

    // PrepareStatement(AI_BOX_BASE_BANK_UPDATE,
    //                  "update base_bank_table set ? where `id`= ? and `enable`=1",
    //                  CONNECTION_SYNCH);

    

    PrepareStatement(AI_BOX_BASE_BANK_INSERT,
                     "insert into base_bank_table(`group_id`,`sub_group_id`,"
                     "`eid`,`name`,`image`,`describe`,`param`,`dtime`,`import`)"
                     "values(?,?,?,?,?,?,?,?,?);",
                     CONNECTION_SYNCH);

    PrepareStatement(AI_BOX_BASE_BANK_LOAD,
                     "update base_bank_table set import=1 where import=0 and enable=1 and group_id=? and sub_group_id=?;",
                     CONNECTION_SYNCH);

    PrepareStatement(AI_BOX_CAMERA_PUSH_STREAM,
                     " select `id`,`name`,`stream_url`,`start` from input_source_table where enable=1;",
                     CONNECTION_SYNCH);

    PrepareStatement(AI_BOX_CAMERA_ALG_CONF_LIST2,
                     "SELECT `camera_id`,`alg_id`,`cycle`, `dp`,`dp_id`,`interval`,`hasTimes`,`level`, "
                     "`people_count`,`thrs`,`vehicletype`,`vehs`,`lines`,`polygons`, `width`, `height`,`wb` FROM "
                     "camera_alg_conf where `camera_id` in ( ? ) ;",
                     CONNECTION_SYNCH);
    PrepareStatement(AI_BOX_CAMERA_ALARM_LOG_INSERT,
                     "insert into alarm_log_table(`camera_id`,`date`,`time`,"
                     "`type`,`level`,`image`,`temp`)  values(?,?,?,?,?,?,?);",
                     CONNECTION_SYNCH);

    // PrepareStatement(LOGON_MAX_STATEMENTS, "SELECT id FROM account_info WHERE
    // id = ?", CONNECTION_SYNCH);   
}
} // namespace DB