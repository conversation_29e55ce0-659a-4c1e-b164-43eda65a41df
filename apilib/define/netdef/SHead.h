##ifndef _SHEADH
#define _SHEADH

#include "Protocol.h"



#pragma pack(push, 1)

struct SHead {
  uint16_t m_nFlag;   // 标志 (2 bytes)
  uint16_t m_nSrcId;  // 源ID (2 bytes)
  uint16_t m_nDestId; // 目的ID (2 bytes)
  uint8_t m_uniqueId[36]; // 消息唯一ID，由消息发起端生成，处理端收原样返回
  uint16_t m_nMsgId;  // 消息类型 (2 bytes)
  uint32_t m_nLenght; // 数据长度 (4 bytes)

  SHead()
      : m_nFlag(MESSAGE_HEAD_FLAG), m_nSrcId(0), m_nDestId(0), m_nMsgId(0),
        m_nLenght(0) {
    memset(m_uniqueId, 0x0, sizeof(m_uniqueId));
  }
};

#pragma pack(pop)

#ifdef PROTOCOL_STREAM
inline nsStreamWriter &operator<<(nsStreamWriter &stream, const SHead &data) {
  stream << data.m_nFlag << data.m_nSrcId << data.m_nDestId;
  for (size_t i = 0; i < 36; i++) {
    stream << data.m_uniqueId[i];
  }
  stream << data.m_nMsgId << data.m_nLenght;

  return stream;
}

inline nsStreamReader &operator>>(nsStreamReader &stream, SHead &data) {
  stream >> data.m_nFlag >> data.m_nSrcId >> data.m_nDestId;
  for (size_t i = 0; i < 36; i++) {
    stream >> data.m_uniqueId[i];
  }
  stream >> data.m_nMsgId >> data.m_nLenght;

  return stream;
}
#endif

#endif /* _SHEADH */