# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.28

# Default target executed when no arguments are given to make.
default_target: all
.PHONY : default_target

# Allow only one "make -f Makefile2" at a time, but pass parallelism.
.NOTPARALLEL:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Produce verbose output by default.
VERBOSE = 1

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/EnergyStorage/AI_Box

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/EnergyStorage/AI_Box

#=============================================================================
# Targets provided globally by CMake.

# Special rule for the target edit_cache
edit_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "No interactive CMake dialog available..."
	/usr/bin/cmake -E echo No\ interactive\ CMake\ dialog\ available.
.PHONY : edit_cache

# Special rule for the target edit_cache
edit_cache/fast: edit_cache
.PHONY : edit_cache/fast

# Special rule for the target rebuild_cache
rebuild_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Running CMake to regenerate build system..."
	/usr/bin/cmake --regenerate-during-build -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : rebuild_cache

# Special rule for the target rebuild_cache
rebuild_cache/fast: rebuild_cache
.PHONY : rebuild_cache/fast

# Special rule for the target list_install_components
list_install_components:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Available install components are: \"Unspecified\""
.PHONY : list_install_components

# Special rule for the target list_install_components
list_install_components/fast: list_install_components
.PHONY : list_install_components/fast

# Special rule for the target install
install: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Install the project..."
	/usr/bin/cmake -P cmake_install.cmake
.PHONY : install

# Special rule for the target install
install/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Install the project..."
	/usr/bin/cmake -P cmake_install.cmake
.PHONY : install/fast

# Special rule for the target install/local
install/local: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Installing only the local directory..."
	/usr/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local

# Special rule for the target install/local
install/local/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Installing only the local directory..."
	/usr/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local/fast

# Special rule for the target install/strip
install/strip: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Installing the project stripped..."
	/usr/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip

# Special rule for the target install/strip
install/strip/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Installing the project stripped..."
	/usr/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip/fast

# The main all target
all: cmake_check_build_system
	cd /home/<USER>/EnergyStorage/AI_Box && $(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/EnergyStorage/AI_Box/CMakeFiles /home/<USER>/EnergyStorage/AI_Box/apilib/net//CMakeFiles/progress.marks
	cd /home/<USER>/EnergyStorage/AI_Box && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 apilib/net/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/EnergyStorage/AI_Box/CMakeFiles 0
.PHONY : all

# The main clean target
clean:
	cd /home/<USER>/EnergyStorage/AI_Box && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 apilib/net/clean
.PHONY : clean

# The main clean target
clean/fast: clean
.PHONY : clean/fast

# Prepare targets for installation.
preinstall: all
	cd /home/<USER>/EnergyStorage/AI_Box && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 apilib/net/preinstall
.PHONY : preinstall

# Prepare targets for installation.
preinstall/fast:
	cd /home/<USER>/EnergyStorage/AI_Box && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 apilib/net/preinstall
.PHONY : preinstall/fast

# clear depends
depend:
	cd /home/<USER>/EnergyStorage/AI_Box && $(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 1
.PHONY : depend

# Convenience name for target.
apilib/net/CMakeFiles/Net.dir/rule:
	cd /home/<USER>/EnergyStorage/AI_Box && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 apilib/net/CMakeFiles/Net.dir/rule
.PHONY : apilib/net/CMakeFiles/Net.dir/rule

# Convenience name for target.
Net: apilib/net/CMakeFiles/Net.dir/rule
.PHONY : Net

# fast build rule for target.
Net/fast:
	cd /home/<USER>/EnergyStorage/AI_Box && $(MAKE) $(MAKESILENT) -f apilib/net/CMakeFiles/Net.dir/build.make apilib/net/CMakeFiles/Net.dir/build
.PHONY : Net/fast

AsynchronismEngine.o: AsynchronismEngine.cpp.o
.PHONY : AsynchronismEngine.o

# target to build an object file
AsynchronismEngine.cpp.o:
	cd /home/<USER>/EnergyStorage/AI_Box && $(MAKE) $(MAKESILENT) -f apilib/net/CMakeFiles/Net.dir/build.make apilib/net/CMakeFiles/Net.dir/AsynchronismEngine.cpp.o
.PHONY : AsynchronismEngine.cpp.o

AsynchronismEngine.i: AsynchronismEngine.cpp.i
.PHONY : AsynchronismEngine.i

# target to preprocess a source file
AsynchronismEngine.cpp.i:
	cd /home/<USER>/EnergyStorage/AI_Box && $(MAKE) $(MAKESILENT) -f apilib/net/CMakeFiles/Net.dir/build.make apilib/net/CMakeFiles/Net.dir/AsynchronismEngine.cpp.i
.PHONY : AsynchronismEngine.cpp.i

AsynchronismEngine.s: AsynchronismEngine.cpp.s
.PHONY : AsynchronismEngine.s

# target to generate assembly for a file
AsynchronismEngine.cpp.s:
	cd /home/<USER>/EnergyStorage/AI_Box && $(MAKE) $(MAKESILENT) -f apilib/net/CMakeFiles/Net.dir/build.make apilib/net/CMakeFiles/Net.dir/AsynchronismEngine.cpp.s
.PHONY : AsynchronismEngine.cpp.s

AttemperEngine.o: AttemperEngine.cpp.o
.PHONY : AttemperEngine.o

# target to build an object file
AttemperEngine.cpp.o:
	cd /home/<USER>/EnergyStorage/AI_Box && $(MAKE) $(MAKESILENT) -f apilib/net/CMakeFiles/Net.dir/build.make apilib/net/CMakeFiles/Net.dir/AttemperEngine.cpp.o
.PHONY : AttemperEngine.cpp.o

AttemperEngine.i: AttemperEngine.cpp.i
.PHONY : AttemperEngine.i

# target to preprocess a source file
AttemperEngine.cpp.i:
	cd /home/<USER>/EnergyStorage/AI_Box && $(MAKE) $(MAKESILENT) -f apilib/net/CMakeFiles/Net.dir/build.make apilib/net/CMakeFiles/Net.dir/AttemperEngine.cpp.i
.PHONY : AttemperEngine.cpp.i

AttemperEngine.s: AttemperEngine.cpp.s
.PHONY : AttemperEngine.s

# target to generate assembly for a file
AttemperEngine.cpp.s:
	cd /home/<USER>/EnergyStorage/AI_Box && $(MAKE) $(MAKESILENT) -f apilib/net/CMakeFiles/Net.dir/build.make apilib/net/CMakeFiles/Net.dir/AttemperEngine.cpp.s
.PHONY : AttemperEngine.cpp.s

TCPNetworkEngine.o: TCPNetworkEngine.cpp.o
.PHONY : TCPNetworkEngine.o

# target to build an object file
TCPNetworkEngine.cpp.o:
	cd /home/<USER>/EnergyStorage/AI_Box && $(MAKE) $(MAKESILENT) -f apilib/net/CMakeFiles/Net.dir/build.make apilib/net/CMakeFiles/Net.dir/TCPNetworkEngine.cpp.o
.PHONY : TCPNetworkEngine.cpp.o

TCPNetworkEngine.i: TCPNetworkEngine.cpp.i
.PHONY : TCPNetworkEngine.i

# target to preprocess a source file
TCPNetworkEngine.cpp.i:
	cd /home/<USER>/EnergyStorage/AI_Box && $(MAKE) $(MAKESILENT) -f apilib/net/CMakeFiles/Net.dir/build.make apilib/net/CMakeFiles/Net.dir/TCPNetworkEngine.cpp.i
.PHONY : TCPNetworkEngine.cpp.i

TCPNetworkEngine.s: TCPNetworkEngine.cpp.s
.PHONY : TCPNetworkEngine.s

# target to generate assembly for a file
TCPNetworkEngine.cpp.s:
	cd /home/<USER>/EnergyStorage/AI_Box && $(MAKE) $(MAKESILENT) -f apilib/net/CMakeFiles/Net.dir/build.make apilib/net/CMakeFiles/Net.dir/TCPNetworkEngine.cpp.s
.PHONY : TCPNetworkEngine.cpp.s

TCPNetworkItem.o: TCPNetworkItem.cpp.o
.PHONY : TCPNetworkItem.o

# target to build an object file
TCPNetworkItem.cpp.o:
	cd /home/<USER>/EnergyStorage/AI_Box && $(MAKE) $(MAKESILENT) -f apilib/net/CMakeFiles/Net.dir/build.make apilib/net/CMakeFiles/Net.dir/TCPNetworkItem.cpp.o
.PHONY : TCPNetworkItem.cpp.o

TCPNetworkItem.i: TCPNetworkItem.cpp.i
.PHONY : TCPNetworkItem.i

# target to preprocess a source file
TCPNetworkItem.cpp.i:
	cd /home/<USER>/EnergyStorage/AI_Box && $(MAKE) $(MAKESILENT) -f apilib/net/CMakeFiles/Net.dir/build.make apilib/net/CMakeFiles/Net.dir/TCPNetworkItem.cpp.i
.PHONY : TCPNetworkItem.cpp.i

TCPNetworkItem.s: TCPNetworkItem.cpp.s
.PHONY : TCPNetworkItem.s

# target to generate assembly for a file
TCPNetworkItem.cpp.s:
	cd /home/<USER>/EnergyStorage/AI_Box && $(MAKE) $(MAKESILENT) -f apilib/net/CMakeFiles/Net.dir/build.make apilib/net/CMakeFiles/Net.dir/TCPNetworkItem.cpp.s
.PHONY : TCPNetworkItem.cpp.s

TCPSocketService.o: TCPSocketService.cpp.o
.PHONY : TCPSocketService.o

# target to build an object file
TCPSocketService.cpp.o:
	cd /home/<USER>/EnergyStorage/AI_Box && $(MAKE) $(MAKESILENT) -f apilib/net/CMakeFiles/Net.dir/build.make apilib/net/CMakeFiles/Net.dir/TCPSocketService.cpp.o
.PHONY : TCPSocketService.cpp.o

TCPSocketService.i: TCPSocketService.cpp.i
.PHONY : TCPSocketService.i

# target to preprocess a source file
TCPSocketService.cpp.i:
	cd /home/<USER>/EnergyStorage/AI_Box && $(MAKE) $(MAKESILENT) -f apilib/net/CMakeFiles/Net.dir/build.make apilib/net/CMakeFiles/Net.dir/TCPSocketService.cpp.i
.PHONY : TCPSocketService.cpp.i

TCPSocketService.s: TCPSocketService.cpp.s
.PHONY : TCPSocketService.s

# target to generate assembly for a file
TCPSocketService.cpp.s:
	cd /home/<USER>/EnergyStorage/AI_Box && $(MAKE) $(MAKESILENT) -f apilib/net/CMakeFiles/Net.dir/build.make apilib/net/CMakeFiles/Net.dir/TCPSocketService.cpp.s
.PHONY : TCPSocketService.cpp.s

TimerEngine.o: TimerEngine.cpp.o
.PHONY : TimerEngine.o

# target to build an object file
TimerEngine.cpp.o:
	cd /home/<USER>/EnergyStorage/AI_Box && $(MAKE) $(MAKESILENT) -f apilib/net/CMakeFiles/Net.dir/build.make apilib/net/CMakeFiles/Net.dir/TimerEngine.cpp.o
.PHONY : TimerEngine.cpp.o

TimerEngine.i: TimerEngine.cpp.i
.PHONY : TimerEngine.i

# target to preprocess a source file
TimerEngine.cpp.i:
	cd /home/<USER>/EnergyStorage/AI_Box && $(MAKE) $(MAKESILENT) -f apilib/net/CMakeFiles/Net.dir/build.make apilib/net/CMakeFiles/Net.dir/TimerEngine.cpp.i
.PHONY : TimerEngine.cpp.i

TimerEngine.s: TimerEngine.cpp.s
.PHONY : TimerEngine.s

# target to generate assembly for a file
TimerEngine.cpp.s:
	cd /home/<USER>/EnergyStorage/AI_Box && $(MAKE) $(MAKESILENT) -f apilib/net/CMakeFiles/Net.dir/build.make apilib/net/CMakeFiles/Net.dir/TimerEngine.cpp.s
.PHONY : TimerEngine.cpp.s

# Help Target
help:
	@echo "The following are some of the valid targets for this Makefile:"
	@echo "... all (the default if no target is provided)"
	@echo "... clean"
	@echo "... depend"
	@echo "... edit_cache"
	@echo "... install"
	@echo "... install/local"
	@echo "... install/strip"
	@echo "... list_install_components"
	@echo "... rebuild_cache"
	@echo "... Net"
	@echo "... AsynchronismEngine.o"
	@echo "... AsynchronismEngine.i"
	@echo "... AsynchronismEngine.s"
	@echo "... AttemperEngine.o"
	@echo "... AttemperEngine.i"
	@echo "... AttemperEngine.s"
	@echo "... TCPNetworkEngine.o"
	@echo "... TCPNetworkEngine.i"
	@echo "... TCPNetworkEngine.s"
	@echo "... TCPNetworkItem.o"
	@echo "... TCPNetworkItem.i"
	@echo "... TCPNetworkItem.s"
	@echo "... TCPSocketService.o"
	@echo "... TCPSocketService.i"
	@echo "... TCPSocketService.s"
	@echo "... TimerEngine.o"
	@echo "... TimerEngine.i"
	@echo "... TimerEngine.s"
.PHONY : help



#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	cd /home/<USER>/EnergyStorage/AI_Box && $(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system

