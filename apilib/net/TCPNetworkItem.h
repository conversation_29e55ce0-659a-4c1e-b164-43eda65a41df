#ifndef TCP_NETWORK_ITEM_H
#define TCP_NETWORK_ITEM_H

#include "Define.h"
#include "MessageBuffer.h"
#include "Packet.h"
#include <asio/ip/tcp.hpp>
#include <atomic>
#include <functional>
#include <memory>
#include <mutex>
#include <queue>

#define READ_BLOCK_SIZE 4096
#ifdef BOOST_ASIO_HAS_IOCP
#define LENDY_SOCKET_USE_IOCP
#endif

namespace Net {
using asio::ip::tcp;
using Util::MessageBuffer;

class CTCPNetworkItem;

// ���Ӷ���ص��ӿ�
struct ITCPNetworkItemSink {
    // ���¼�
    virtual bool OnEventSocketBind(std::shared_ptr<CTCPNetworkItem> pTCPNetworkItem) = 0;
    // �ر��¼�
    virtual bool OnEventSocketShut(std::shared_ptr<CTCPNetworkItem> pTCPNetworkItem) = 0;
    // ��ȡ�¼�
    virtual bool OnEventSocketRead(TCP_Command Command, void *pData, uint16 wDataSize,
                                   std::shared_ptr<CTCPNetworkItem> pTCPNetworkItem) = 0;
};

class CTCPNetworkItem : public std::enable_shared_from_this<CTCPNetworkItem> {
  public:
    explicit CTCPNetworkItem(uint16 index, tcp::socket &&socket, ITCPNetworkItemSink *);
    // explicit CTCPNetworkItem(uint16 index, tcp::socket&& socket, ITCPNetworkItemSink*,
    //	std::function<bool(CTCPNetworkItem *)>,
    //	std::function<bool(CTCPNetworkItem *)>,
    //	std::function<bool(TCP_Command, void*, uint16, CTCPNetworkItem*)>);

    virtual ~CTCPNetworkItem();

    virtual void Start();

    virtual bool Update();

    void Attach(tcp::socket &&socket);

    asio::ip::address GetRemoteIPAddress() const;

    uint16 GetRemotePort() const;

    void AsyncRead();

    bool SendData(uint16 wMainCmdID, uint16 wSubCmdID, void *pData, uint16 wDataSize);

    void QueuePacket(MessageBuffer &&buffer);

    bool IsOpen() const;
    
    void CloseSocket();

    void DelayedCloseSocket();

    // ����Ⱥ��
    bool AllowBatchSend(bool cbAllowBatch);

    uint32 GetIndex() { return m_index; }
    uint32 GetClientIP() { return m_remoteAddress.to_v4().to_uint(); }

    MessageBuffer &GetReadBuffer();

    // ��������
    inline std::mutex &GetMutex() { return m_mutex; }

    // �ص�����
  public:
    void SocketBindCallBack();

    void SocketShutCallBack();

  protected:
    virtual void OnClose();

    virtual void ReadHandler();

    bool AsyncProcessQueue();

    void SetNoDelay(bool enable);

    // ���ܺ���
  private:
    // ��������
    uint16 EncryptBuffer(uint8 pcbDataBuffer[], uint16 wDataSize, uint16 wBufferSize);
    // ��������
    uint16 CrevasseBuffer(uint8 pcbDataBuffer[], uint16 wDataSize);

  private:
    void ReadHandlerInternal(asio::error_code error, size_t transferredBytes);

#ifdef LENDY_SOCKET_USE_IOCP
    void WriteHandler(asio::error_code error, std::size_t transferedBytes);
#endif
    void WriteHandlerWrapper(asio::error_code /*error*/, std::size_t /*transferedBytes*/);

    bool HandleQueue();

  private:
    uint32 m_index;
    bool m_bAllowBatch;
    std::mutex m_mutex;

    tcp::socket m_socket;
    asio::ip::address m_remoteAddress;
    uint16 m_remotePort;

    MessageBuffer m_readBuffer;
    std::queue<MessageBuffer> m_writeQueue;

    std::atomic<bool> m_closed;
    std::atomic<bool> m_closing;
    bool m_bWritingAsync;

    ITCPNetworkItemSink *m_pITCPNetworkItemSink; // �ص��ӿ�

    // private:
    //	std::function<bool(CTCPNetworkItem *)> m_SocketBindCallBack;
    //	std::function<bool(CTCPNetworkItem *)> m_SocketShutCallBack;
    //	std::function<bool(TCP_Command, void*, uint16, CTCPNetworkItem*)> m_SocketReadCallBack;
};
} // namespace Net

#endif