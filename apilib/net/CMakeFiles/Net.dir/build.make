# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.28

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Produce verbose output by default.
VERBOSE = 1

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/EnergyStorage/AI_Box

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/EnergyStorage/AI_Box

# Include any dependencies generated for this target.
include apilib/net/CMakeFiles/Net.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include apilib/net/CMakeFiles/Net.dir/compiler_depend.make

# Include the progress variables for this target.
include apilib/net/CMakeFiles/Net.dir/progress.make

# Include the compile flags for this target's objects.
include apilib/net/CMakeFiles/Net.dir/flags.make

apilib/net/CMakeFiles/Net.dir/AsynchronismEngine.cpp.o: apilib/net/CMakeFiles/Net.dir/flags.make
apilib/net/CMakeFiles/Net.dir/AsynchronismEngine.cpp.o: apilib/net/AsynchronismEngine.cpp
apilib/net/CMakeFiles/Net.dir/AsynchronismEngine.cpp.o: apilib/net/CMakeFiles/Net.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/EnergyStorage/AI_Box/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building CXX object apilib/net/CMakeFiles/Net.dir/AsynchronismEngine.cpp.o"
	cd /home/<USER>/EnergyStorage/AI_Box/apilib/net && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT apilib/net/CMakeFiles/Net.dir/AsynchronismEngine.cpp.o -MF CMakeFiles/Net.dir/AsynchronismEngine.cpp.o.d -o CMakeFiles/Net.dir/AsynchronismEngine.cpp.o -c /home/<USER>/EnergyStorage/AI_Box/apilib/net/AsynchronismEngine.cpp

apilib/net/CMakeFiles/Net.dir/AsynchronismEngine.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/Net.dir/AsynchronismEngine.cpp.i"
	cd /home/<USER>/EnergyStorage/AI_Box/apilib/net && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/EnergyStorage/AI_Box/apilib/net/AsynchronismEngine.cpp > CMakeFiles/Net.dir/AsynchronismEngine.cpp.i

apilib/net/CMakeFiles/Net.dir/AsynchronismEngine.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/Net.dir/AsynchronismEngine.cpp.s"
	cd /home/<USER>/EnergyStorage/AI_Box/apilib/net && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/EnergyStorage/AI_Box/apilib/net/AsynchronismEngine.cpp -o CMakeFiles/Net.dir/AsynchronismEngine.cpp.s

apilib/net/CMakeFiles/Net.dir/AttemperEngine.cpp.o: apilib/net/CMakeFiles/Net.dir/flags.make
apilib/net/CMakeFiles/Net.dir/AttemperEngine.cpp.o: apilib/net/AttemperEngine.cpp
apilib/net/CMakeFiles/Net.dir/AttemperEngine.cpp.o: apilib/net/CMakeFiles/Net.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/EnergyStorage/AI_Box/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Building CXX object apilib/net/CMakeFiles/Net.dir/AttemperEngine.cpp.o"
	cd /home/<USER>/EnergyStorage/AI_Box/apilib/net && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT apilib/net/CMakeFiles/Net.dir/AttemperEngine.cpp.o -MF CMakeFiles/Net.dir/AttemperEngine.cpp.o.d -o CMakeFiles/Net.dir/AttemperEngine.cpp.o -c /home/<USER>/EnergyStorage/AI_Box/apilib/net/AttemperEngine.cpp

apilib/net/CMakeFiles/Net.dir/AttemperEngine.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/Net.dir/AttemperEngine.cpp.i"
	cd /home/<USER>/EnergyStorage/AI_Box/apilib/net && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/EnergyStorage/AI_Box/apilib/net/AttemperEngine.cpp > CMakeFiles/Net.dir/AttemperEngine.cpp.i

apilib/net/CMakeFiles/Net.dir/AttemperEngine.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/Net.dir/AttemperEngine.cpp.s"
	cd /home/<USER>/EnergyStorage/AI_Box/apilib/net && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/EnergyStorage/AI_Box/apilib/net/AttemperEngine.cpp -o CMakeFiles/Net.dir/AttemperEngine.cpp.s

apilib/net/CMakeFiles/Net.dir/TCPNetworkEngine.cpp.o: apilib/net/CMakeFiles/Net.dir/flags.make
apilib/net/CMakeFiles/Net.dir/TCPNetworkEngine.cpp.o: apilib/net/TCPNetworkEngine.cpp
apilib/net/CMakeFiles/Net.dir/TCPNetworkEngine.cpp.o: apilib/net/CMakeFiles/Net.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/EnergyStorage/AI_Box/CMakeFiles --progress-num=$(CMAKE_PROGRESS_3) "Building CXX object apilib/net/CMakeFiles/Net.dir/TCPNetworkEngine.cpp.o"
	cd /home/<USER>/EnergyStorage/AI_Box/apilib/net && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT apilib/net/CMakeFiles/Net.dir/TCPNetworkEngine.cpp.o -MF CMakeFiles/Net.dir/TCPNetworkEngine.cpp.o.d -o CMakeFiles/Net.dir/TCPNetworkEngine.cpp.o -c /home/<USER>/EnergyStorage/AI_Box/apilib/net/TCPNetworkEngine.cpp

apilib/net/CMakeFiles/Net.dir/TCPNetworkEngine.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/Net.dir/TCPNetworkEngine.cpp.i"
	cd /home/<USER>/EnergyStorage/AI_Box/apilib/net && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/EnergyStorage/AI_Box/apilib/net/TCPNetworkEngine.cpp > CMakeFiles/Net.dir/TCPNetworkEngine.cpp.i

apilib/net/CMakeFiles/Net.dir/TCPNetworkEngine.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/Net.dir/TCPNetworkEngine.cpp.s"
	cd /home/<USER>/EnergyStorage/AI_Box/apilib/net && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/EnergyStorage/AI_Box/apilib/net/TCPNetworkEngine.cpp -o CMakeFiles/Net.dir/TCPNetworkEngine.cpp.s

apilib/net/CMakeFiles/Net.dir/TCPNetworkItem.cpp.o: apilib/net/CMakeFiles/Net.dir/flags.make
apilib/net/CMakeFiles/Net.dir/TCPNetworkItem.cpp.o: apilib/net/TCPNetworkItem.cpp
apilib/net/CMakeFiles/Net.dir/TCPNetworkItem.cpp.o: apilib/net/CMakeFiles/Net.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/EnergyStorage/AI_Box/CMakeFiles --progress-num=$(CMAKE_PROGRESS_4) "Building CXX object apilib/net/CMakeFiles/Net.dir/TCPNetworkItem.cpp.o"
	cd /home/<USER>/EnergyStorage/AI_Box/apilib/net && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT apilib/net/CMakeFiles/Net.dir/TCPNetworkItem.cpp.o -MF CMakeFiles/Net.dir/TCPNetworkItem.cpp.o.d -o CMakeFiles/Net.dir/TCPNetworkItem.cpp.o -c /home/<USER>/EnergyStorage/AI_Box/apilib/net/TCPNetworkItem.cpp

apilib/net/CMakeFiles/Net.dir/TCPNetworkItem.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/Net.dir/TCPNetworkItem.cpp.i"
	cd /home/<USER>/EnergyStorage/AI_Box/apilib/net && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/EnergyStorage/AI_Box/apilib/net/TCPNetworkItem.cpp > CMakeFiles/Net.dir/TCPNetworkItem.cpp.i

apilib/net/CMakeFiles/Net.dir/TCPNetworkItem.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/Net.dir/TCPNetworkItem.cpp.s"
	cd /home/<USER>/EnergyStorage/AI_Box/apilib/net && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/EnergyStorage/AI_Box/apilib/net/TCPNetworkItem.cpp -o CMakeFiles/Net.dir/TCPNetworkItem.cpp.s

apilib/net/CMakeFiles/Net.dir/TCPSocketService.cpp.o: apilib/net/CMakeFiles/Net.dir/flags.make
apilib/net/CMakeFiles/Net.dir/TCPSocketService.cpp.o: apilib/net/TCPSocketService.cpp
apilib/net/CMakeFiles/Net.dir/TCPSocketService.cpp.o: apilib/net/CMakeFiles/Net.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/EnergyStorage/AI_Box/CMakeFiles --progress-num=$(CMAKE_PROGRESS_5) "Building CXX object apilib/net/CMakeFiles/Net.dir/TCPSocketService.cpp.o"
	cd /home/<USER>/EnergyStorage/AI_Box/apilib/net && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT apilib/net/CMakeFiles/Net.dir/TCPSocketService.cpp.o -MF CMakeFiles/Net.dir/TCPSocketService.cpp.o.d -o CMakeFiles/Net.dir/TCPSocketService.cpp.o -c /home/<USER>/EnergyStorage/AI_Box/apilib/net/TCPSocketService.cpp

apilib/net/CMakeFiles/Net.dir/TCPSocketService.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/Net.dir/TCPSocketService.cpp.i"
	cd /home/<USER>/EnergyStorage/AI_Box/apilib/net && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/EnergyStorage/AI_Box/apilib/net/TCPSocketService.cpp > CMakeFiles/Net.dir/TCPSocketService.cpp.i

apilib/net/CMakeFiles/Net.dir/TCPSocketService.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/Net.dir/TCPSocketService.cpp.s"
	cd /home/<USER>/EnergyStorage/AI_Box/apilib/net && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/EnergyStorage/AI_Box/apilib/net/TCPSocketService.cpp -o CMakeFiles/Net.dir/TCPSocketService.cpp.s

apilib/net/CMakeFiles/Net.dir/TimerEngine.cpp.o: apilib/net/CMakeFiles/Net.dir/flags.make
apilib/net/CMakeFiles/Net.dir/TimerEngine.cpp.o: apilib/net/TimerEngine.cpp
apilib/net/CMakeFiles/Net.dir/TimerEngine.cpp.o: apilib/net/CMakeFiles/Net.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/EnergyStorage/AI_Box/CMakeFiles --progress-num=$(CMAKE_PROGRESS_6) "Building CXX object apilib/net/CMakeFiles/Net.dir/TimerEngine.cpp.o"
	cd /home/<USER>/EnergyStorage/AI_Box/apilib/net && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT apilib/net/CMakeFiles/Net.dir/TimerEngine.cpp.o -MF CMakeFiles/Net.dir/TimerEngine.cpp.o.d -o CMakeFiles/Net.dir/TimerEngine.cpp.o -c /home/<USER>/EnergyStorage/AI_Box/apilib/net/TimerEngine.cpp

apilib/net/CMakeFiles/Net.dir/TimerEngine.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/Net.dir/TimerEngine.cpp.i"
	cd /home/<USER>/EnergyStorage/AI_Box/apilib/net && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/EnergyStorage/AI_Box/apilib/net/TimerEngine.cpp > CMakeFiles/Net.dir/TimerEngine.cpp.i

apilib/net/CMakeFiles/Net.dir/TimerEngine.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/Net.dir/TimerEngine.cpp.s"
	cd /home/<USER>/EnergyStorage/AI_Box/apilib/net && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/EnergyStorage/AI_Box/apilib/net/TimerEngine.cpp -o CMakeFiles/Net.dir/TimerEngine.cpp.s

# Object files for target Net
Net_OBJECTS = \
"CMakeFiles/Net.dir/AsynchronismEngine.cpp.o" \
"CMakeFiles/Net.dir/AttemperEngine.cpp.o" \
"CMakeFiles/Net.dir/TCPNetworkEngine.cpp.o" \
"CMakeFiles/Net.dir/TCPNetworkItem.cpp.o" \
"CMakeFiles/Net.dir/TCPSocketService.cpp.o" \
"CMakeFiles/Net.dir/TimerEngine.cpp.o"

# External object files for target Net
Net_EXTERNAL_OBJECTS =

apilib/net/Net.so: apilib/net/CMakeFiles/Net.dir/AsynchronismEngine.cpp.o
apilib/net/Net.so: apilib/net/CMakeFiles/Net.dir/AttemperEngine.cpp.o
apilib/net/Net.so: apilib/net/CMakeFiles/Net.dir/TCPNetworkEngine.cpp.o
apilib/net/Net.so: apilib/net/CMakeFiles/Net.dir/TCPNetworkItem.cpp.o
apilib/net/Net.so: apilib/net/CMakeFiles/Net.dir/TCPSocketService.cpp.o
apilib/net/Net.so: apilib/net/CMakeFiles/Net.dir/TimerEngine.cpp.o
apilib/net/Net.so: apilib/net/CMakeFiles/Net.dir/build.make
apilib/net/Net.so: apilib/log/Log.so
apilib/net/Net.so: apilib/util/Util.so
apilib/net/Net.so: apilib/dep/fmt/fmt.a
apilib/net/Net.so: /usr/lib/x86_64-linux-gnu/libssl.so
apilib/net/Net.so: /usr/lib/x86_64-linux-gnu/libcrypto.so
apilib/net/Net.so: apilib/net/CMakeFiles/Net.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --bold --progress-dir=/home/<USER>/EnergyStorage/AI_Box/CMakeFiles --progress-num=$(CMAKE_PROGRESS_7) "Linking CXX shared library Net.so"
	cd /home/<USER>/EnergyStorage/AI_Box/apilib/net && $(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/Net.dir/link.txt --verbose=$(VERBOSE)
	cd /home/<USER>/EnergyStorage/AI_Box/apilib/net && /usr/bin/cmake -E copy /home/<USER>/EnergyStorage/AI_Box/apilib/net/Net.so /home/<USER>/EnergyStorage/AI_Box/bin/

# Rule to build all files generated by this target.
apilib/net/CMakeFiles/Net.dir/build: apilib/net/Net.so
.PHONY : apilib/net/CMakeFiles/Net.dir/build

apilib/net/CMakeFiles/Net.dir/clean:
	cd /home/<USER>/EnergyStorage/AI_Box/apilib/net && $(CMAKE_COMMAND) -P CMakeFiles/Net.dir/cmake_clean.cmake
.PHONY : apilib/net/CMakeFiles/Net.dir/clean

apilib/net/CMakeFiles/Net.dir/depend:
	cd /home/<USER>/EnergyStorage/AI_Box && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /home/<USER>/EnergyStorage/AI_Box /home/<USER>/EnergyStorage/AI_Box/apilib/net /home/<USER>/EnergyStorage/AI_Box /home/<USER>/EnergyStorage/AI_Box/apilib/net /home/<USER>/EnergyStorage/AI_Box/apilib/net/CMakeFiles/Net.dir/DependInfo.cmake "--color=$(COLOR)"
.PHONY : apilib/net/CMakeFiles/Net.dir/depend

