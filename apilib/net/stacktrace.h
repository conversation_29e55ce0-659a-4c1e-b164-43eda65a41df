
#pragma once
#include <iostream>
#include <sstream>
#include <string>
#include <vector>

#ifdef __linux__
#include <execinfo.h>
#include <unistd.h>
#elif _WIN32
#include <dbghelp.h>
#include <windows.h>
#pragma comment(lib, "dbghelp.lib")
#endif

class StackTrace {
  public:
    static std::vector<std::string> Capture(int skip = 0) {
        std::vector<std::string> stack;
#ifdef __linux__
        void *buffer[100];
        int size = backtrace(buffer, 100);
        char **symbols = backtrace_symbols(buffer, size);
        for (int i = skip; i < size; ++i) {
            stack.emplace_back(symbols[i]);
        }
        free(symbols);
#elif _WIN32
        void *buffer[100];
        HANDLE process = GetCurrentProcess();
        SymInitialize(process, NULL, TRUE);
        unsigned short frames = CaptureStackBackTrace(0, 100, buffer, NULL);
        SYMBOL_INFO *symbol = (SYMBOL_INFO *)malloc(sizeof(SYMBOL_INFO) + 256);
        symbol->MaxNameLen = 255;
        symbol->SizeOfStruct = sizeof(SYMBOL_INFO);
        for (unsigned int i = skip; i < frames; i++) {
            SymFromAddr(process, (DWORD64)(buffer[i]), 0, symbol);
            stack.emplace_back(symbol->Name);
        }
        free(symbol);
#endif
        return stack;
    }
};
