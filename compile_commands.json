[{"directory": "/home/<USER>/EnergyStorage/AI_Box/apilib/dep/fmt", "command": "/usr/bin/c++ -DASIO_STANDALONE -DBOOST_ASIO_STANDALONE -DFMT_USE_DELETED_FUNCTIONS -DFMT_USE_EXTERN_TEMPLATES -DFMT_USE_OVERRIDE -DFMT_USE_RVALUE_REFERENCES -DFMT_USE_VARIADIC_TEMPLATES -DLENDY_API_USE_DYNAMIC_LIBS -DPROTOCOL_STREAM -I/usr/local/openssl-1.0.2/include -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/fmt -std=c++14 -O3 -fPIC -O2 -g -DNDEBUG -std=gnu++14 -w -o CMakeFiles/fmt.dir/fmt/format.cc.o -c /home/<USER>/EnergyStorage/AI_Box/apilib/dep/fmt/fmt/format.cc", "file": "/home/<USER>/EnergyStorage/AI_Box/apilib/dep/fmt/fmt/format.cc", "output": "apilib/dep/fmt/CMakeFiles/fmt.dir/fmt/format.cc.o"}, {"directory": "/home/<USER>/EnergyStorage/AI_Box/apilib/dep/fmt", "command": "/usr/bin/c++ -DASIO_STANDALONE -DBOOST_ASIO_STANDALONE -DFMT_USE_DELETED_FUNCTIONS -DFMT_USE_EXTERN_TEMPLATES -DFMT_USE_OVERRIDE -DFMT_USE_RVALUE_REFERENCES -DFMT_USE_VARIADIC_TEMPLATES -DLENDY_API_USE_DYNAMIC_LIBS -DPROTOCOL_STREAM -I/usr/local/openssl-1.0.2/include -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/fmt -std=c++14 -O3 -fPIC -O2 -g -DNDEBUG -std=gnu++14 -w -o CMakeFiles/fmt.dir/fmt/ostream.cc.o -c /home/<USER>/EnergyStorage/AI_Box/apilib/dep/fmt/fmt/ostream.cc", "file": "/home/<USER>/EnergyStorage/AI_Box/apilib/dep/fmt/fmt/ostream.cc", "output": "apilib/dep/fmt/CMakeFiles/fmt.dir/fmt/ostream.cc.o"}, {"directory": "/home/<USER>/EnergyStorage/AI_Box/apilib/dep/fmt", "command": "/usr/bin/c++ -DASIO_STANDALONE -DBOOST_ASIO_STANDALONE -DFMT_USE_DELETED_FUNCTIONS -DFMT_USE_EXTERN_TEMPLATES -DFMT_USE_OVERRIDE -DFMT_USE_RVALUE_REFERENCES -DFMT_USE_VARIADIC_TEMPLATES -DLENDY_API_USE_DYNAMIC_LIBS -DPROTOCOL_STREAM -I/usr/local/openssl-1.0.2/include -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/fmt -std=c++14 -O3 -fPIC -O2 -g -DNDEBUG -std=gnu++14 -w -o CMakeFiles/fmt.dir/fmt/printf.cc.o -c /home/<USER>/EnergyStorage/AI_Box/apilib/dep/fmt/fmt/printf.cc", "file": "/home/<USER>/EnergyStorage/AI_Box/apilib/dep/fmt/fmt/printf.cc", "output": "apilib/dep/fmt/CMakeFiles/fmt.dir/fmt/printf.cc.o"}, {"directory": "/home/<USER>/EnergyStorage/AI_Box/apilib/dep/fmt", "command": "/usr/bin/c++ -DASIO_STANDALONE -DBOOST_ASIO_STANDALONE -DFMT_USE_DELETED_FUNCTIONS -DFMT_USE_EXTERN_TEMPLATES -DFMT_USE_OVERRIDE -DFMT_USE_RVALUE_REFERENCES -DFMT_USE_VARIADIC_TEMPLATES -DLENDY_API_USE_DYNAMIC_LIBS -DPROTOCOL_STREAM -I/usr/local/openssl-1.0.2/include -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/fmt -std=c++14 -O3 -fPIC -O2 -g -DNDEBUG -std=gnu++14 -w -o CMakeFiles/fmt.dir/fmt/posix.cc.o -c /home/<USER>/EnergyStorage/AI_Box/apilib/dep/fmt/fmt/posix.cc", "file": "/home/<USER>/EnergyStorage/AI_Box/apilib/dep/fmt/fmt/posix.cc", "output": "apilib/dep/fmt/CMakeFiles/fmt.dir/fmt/posix.cc.o"}, {"directory": "/home/<USER>/EnergyStorage/AI_Box/apilib/util", "command": "/usr/bin/c++ -<PERSON><PERSON><PERSON>_STANDALONE -DBOOST_ASIO_STANDALONE -DFMT_USE_DELETED_FUNCTIONS -DFMT_USE_EXTERN_TEMPLATES -DFMT_USE_OVERRIDE -DFMT_USE_RVALUE_REFERENCES -DFMT_USE_VARIADIC_TEMPLATES -DLENDY_API_EXPORT_COMMON -DLENDY_API_USE_DYNAMIC_LIBS -DPROTOCOL_STREAM -DUtil_EXPORTS -I/usr/local/openssl-1.0.2/include -I/home/<USER>/EnergyStorage/AI_Box/apilib -I/home/<USER>/EnergyStorage/AI_Box/apilib/define -I/home/<USER>/EnergyStorage/AI_Box/apilib/define/netdef -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/detail -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/detail/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/experimental -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/experimental/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/generic -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/generic/detail -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/generic/detail/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ip -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ip/detail -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ip/detail/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ip/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/local -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/local/detail -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/local/detail/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/posix -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ssl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ssl/detail -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ssl/detail/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ssl/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ts -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/windows -I/home/<USER>/EnergyStorage/AI_Box/apilib/net -I/home/<USER>/EnergyStorage/AI_Box/apilib/net/CMakeFiles -I/home/<USER>/EnergyStorage/AI_Box/apilib/net/CMakeFiles/Net.dir -I/home/<USER>/EnergyStorage/AI_Box/apilib/db -I/home/<USER>/EnergyStorage/AI_Box/apilib/db/CMakeFiles -I/home/<USER>/EnergyStorage/AI_Box/apilib/db/CMakeFiles/DataBase.dir -I/home/<USER>/EnergyStorage/AI_Box/apilib/db/CMakeFiles/DataBase.dir/Implementation -I/home/<USER>/EnergyStorage/AI_Box/apilib/db/Implementation -I/home/<USER>/EnergyStorage/AI_Box/apilib/util -I/home/<USER>/EnergyStorage/AI_Box/apilib/util/CMakeFiles -I/home/<USER>/EnergyStorage/AI_Box/apilib/util/CMakeFiles/Util.dir -I/home/<USER>/EnergyStorage/AI_Box/vision_data.h -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/fmt -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/utf8cpp -std=c++14 -O3 -fPIC -O2 -g -DNDEBUG -std=gnu++14 -fPIC -o CMakeFiles/Util.dir/BigNumber.cpp.o -c /home/<USER>/EnergyStorage/AI_Box/apilib/util/BigNumber.cpp", "file": "/home/<USER>/EnergyStorage/AI_Box/apilib/util/BigNumber.cpp", "output": "apilib/util/CMakeFiles/Util.dir/BigNumber.cpp.o"}, {"directory": "/home/<USER>/EnergyStorage/AI_Box/apilib/util", "command": "/usr/bin/c++ -<PERSON><PERSON><PERSON>_STANDALONE -DBOOST_ASIO_STANDALONE -DFMT_USE_DELETED_FUNCTIONS -DFMT_USE_EXTERN_TEMPLATES -DFMT_USE_OVERRIDE -DFMT_USE_RVALUE_REFERENCES -DFMT_USE_VARIADIC_TEMPLATES -DLENDY_API_EXPORT_COMMON -DLENDY_API_USE_DYNAMIC_LIBS -DPROTOCOL_STREAM -DUtil_EXPORTS -I/usr/local/openssl-1.0.2/include -I/home/<USER>/EnergyStorage/AI_Box/apilib -I/home/<USER>/EnergyStorage/AI_Box/apilib/define -I/home/<USER>/EnergyStorage/AI_Box/apilib/define/netdef -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/detail -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/detail/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/experimental -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/experimental/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/generic -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/generic/detail -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/generic/detail/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ip -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ip/detail -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ip/detail/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ip/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/local -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/local/detail -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/local/detail/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/posix -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ssl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ssl/detail -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ssl/detail/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ssl/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ts -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/windows -I/home/<USER>/EnergyStorage/AI_Box/apilib/net -I/home/<USER>/EnergyStorage/AI_Box/apilib/net/CMakeFiles -I/home/<USER>/EnergyStorage/AI_Box/apilib/net/CMakeFiles/Net.dir -I/home/<USER>/EnergyStorage/AI_Box/apilib/db -I/home/<USER>/EnergyStorage/AI_Box/apilib/db/CMakeFiles -I/home/<USER>/EnergyStorage/AI_Box/apilib/db/CMakeFiles/DataBase.dir -I/home/<USER>/EnergyStorage/AI_Box/apilib/db/CMakeFiles/DataBase.dir/Implementation -I/home/<USER>/EnergyStorage/AI_Box/apilib/db/Implementation -I/home/<USER>/EnergyStorage/AI_Box/apilib/util -I/home/<USER>/EnergyStorage/AI_Box/apilib/util/CMakeFiles -I/home/<USER>/EnergyStorage/AI_Box/apilib/util/CMakeFiles/Util.dir -I/home/<USER>/EnergyStorage/AI_Box/vision_data.h -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/fmt -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/utf8cpp -std=c++14 -O3 -fPIC -O2 -g -DNDEBUG -std=gnu++14 -fPIC -o CMakeFiles/Util.dir/DataQueue.cpp.o -c /home/<USER>/EnergyStorage/AI_Box/apilib/util/DataQueue.cpp", "file": "/home/<USER>/EnergyStorage/AI_Box/apilib/util/DataQueue.cpp", "output": "apilib/util/CMakeFiles/Util.dir/DataQueue.cpp.o"}, {"directory": "/home/<USER>/EnergyStorage/AI_Box/apilib/util", "command": "/usr/bin/c++ -<PERSON><PERSON><PERSON>_STANDALONE -DBOOST_ASIO_STANDALONE -DFMT_USE_DELETED_FUNCTIONS -DFMT_USE_EXTERN_TEMPLATES -DFMT_USE_OVERRIDE -DFMT_USE_RVALUE_REFERENCES -DFMT_USE_VARIADIC_TEMPLATES -DLENDY_API_EXPORT_COMMON -DLENDY_API_USE_DYNAMIC_LIBS -DPROTOCOL_STREAM -DUtil_EXPORTS -I/usr/local/openssl-1.0.2/include -I/home/<USER>/EnergyStorage/AI_Box/apilib -I/home/<USER>/EnergyStorage/AI_Box/apilib/define -I/home/<USER>/EnergyStorage/AI_Box/apilib/define/netdef -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/detail -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/detail/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/experimental -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/experimental/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/generic -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/generic/detail -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/generic/detail/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ip -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ip/detail -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ip/detail/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ip/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/local -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/local/detail -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/local/detail/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/posix -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ssl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ssl/detail -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ssl/detail/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ssl/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ts -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/windows -I/home/<USER>/EnergyStorage/AI_Box/apilib/net -I/home/<USER>/EnergyStorage/AI_Box/apilib/net/CMakeFiles -I/home/<USER>/EnergyStorage/AI_Box/apilib/net/CMakeFiles/Net.dir -I/home/<USER>/EnergyStorage/AI_Box/apilib/db -I/home/<USER>/EnergyStorage/AI_Box/apilib/db/CMakeFiles -I/home/<USER>/EnergyStorage/AI_Box/apilib/db/CMakeFiles/DataBase.dir -I/home/<USER>/EnergyStorage/AI_Box/apilib/db/CMakeFiles/DataBase.dir/Implementation -I/home/<USER>/EnergyStorage/AI_Box/apilib/db/Implementation -I/home/<USER>/EnergyStorage/AI_Box/apilib/util -I/home/<USER>/EnergyStorage/AI_Box/apilib/util/CMakeFiles -I/home/<USER>/EnergyStorage/AI_Box/apilib/util/CMakeFiles/Util.dir -I/home/<USER>/EnergyStorage/AI_Box/vision_data.h -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/fmt -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/utf8cpp -std=c++14 -O3 -fPIC -O2 -g -DNDEBUG -std=gnu++14 -fPIC -o CMakeFiles/Util.dir/GitVision.cpp.o -c /home/<USER>/EnergyStorage/AI_Box/apilib/util/GitVision.cpp", "file": "/home/<USER>/EnergyStorage/AI_Box/apilib/util/GitVision.cpp", "output": "apilib/util/CMakeFiles/Util.dir/GitVision.cpp.o"}, {"directory": "/home/<USER>/EnergyStorage/AI_Box/apilib/util", "command": "/usr/bin/c++ -<PERSON><PERSON><PERSON>_STANDALONE -DBOOST_ASIO_STANDALONE -DFMT_USE_DELETED_FUNCTIONS -DFMT_USE_EXTERN_TEMPLATES -DFMT_USE_OVERRIDE -DFMT_USE_RVALUE_REFERENCES -DFMT_USE_VARIADIC_TEMPLATES -DLENDY_API_EXPORT_COMMON -DLENDY_API_USE_DYNAMIC_LIBS -DPROTOCOL_STREAM -DUtil_EXPORTS -I/usr/local/openssl-1.0.2/include -I/home/<USER>/EnergyStorage/AI_Box/apilib -I/home/<USER>/EnergyStorage/AI_Box/apilib/define -I/home/<USER>/EnergyStorage/AI_Box/apilib/define/netdef -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/detail -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/detail/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/experimental -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/experimental/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/generic -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/generic/detail -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/generic/detail/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ip -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ip/detail -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ip/detail/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ip/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/local -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/local/detail -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/local/detail/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/posix -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ssl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ssl/detail -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ssl/detail/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ssl/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ts -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/windows -I/home/<USER>/EnergyStorage/AI_Box/apilib/net -I/home/<USER>/EnergyStorage/AI_Box/apilib/net/CMakeFiles -I/home/<USER>/EnergyStorage/AI_Box/apilib/net/CMakeFiles/Net.dir -I/home/<USER>/EnergyStorage/AI_Box/apilib/db -I/home/<USER>/EnergyStorage/AI_Box/apilib/db/CMakeFiles -I/home/<USER>/EnergyStorage/AI_Box/apilib/db/CMakeFiles/DataBase.dir -I/home/<USER>/EnergyStorage/AI_Box/apilib/db/CMakeFiles/DataBase.dir/Implementation -I/home/<USER>/EnergyStorage/AI_Box/apilib/db/Implementation -I/home/<USER>/EnergyStorage/AI_Box/apilib/util -I/home/<USER>/EnergyStorage/AI_Box/apilib/util/CMakeFiles -I/home/<USER>/EnergyStorage/AI_Box/apilib/util/CMakeFiles/Util.dir -I/home/<USER>/EnergyStorage/AI_Box/vision_data.h -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/fmt -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/utf8cpp -std=c++14 -O3 -fPIC -O2 -g -DNDEBUG -std=gnu++14 -fPIC -o CMakeFiles/Util.dir/INIReader.cpp.o -c /home/<USER>/EnergyStorage/AI_Box/apilib/util/INIReader.cpp", "file": "/home/<USER>/EnergyStorage/AI_Box/apilib/util/INIReader.cpp", "output": "apilib/util/CMakeFiles/Util.dir/INIReader.cpp.o"}, {"directory": "/home/<USER>/EnergyStorage/AI_Box/apilib/util", "command": "/usr/bin/c++ -<PERSON><PERSON><PERSON>_STANDALONE -DBOOST_ASIO_STANDALONE -DFMT_USE_DELETED_FUNCTIONS -DFMT_USE_EXTERN_TEMPLATES -DFMT_USE_OVERRIDE -DFMT_USE_RVALUE_REFERENCES -DFMT_USE_VARIADIC_TEMPLATES -DLENDY_API_EXPORT_COMMON -DLENDY_API_USE_DYNAMIC_LIBS -DPROTOCOL_STREAM -DUtil_EXPORTS -I/usr/local/openssl-1.0.2/include -I/home/<USER>/EnergyStorage/AI_Box/apilib -I/home/<USER>/EnergyStorage/AI_Box/apilib/define -I/home/<USER>/EnergyStorage/AI_Box/apilib/define/netdef -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/detail -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/detail/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/experimental -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/experimental/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/generic -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/generic/detail -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/generic/detail/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ip -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ip/detail -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ip/detail/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ip/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/local -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/local/detail -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/local/detail/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/posix -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ssl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ssl/detail -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ssl/detail/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ssl/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ts -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/windows -I/home/<USER>/EnergyStorage/AI_Box/apilib/net -I/home/<USER>/EnergyStorage/AI_Box/apilib/net/CMakeFiles -I/home/<USER>/EnergyStorage/AI_Box/apilib/net/CMakeFiles/Net.dir -I/home/<USER>/EnergyStorage/AI_Box/apilib/db -I/home/<USER>/EnergyStorage/AI_Box/apilib/db/CMakeFiles -I/home/<USER>/EnergyStorage/AI_Box/apilib/db/CMakeFiles/DataBase.dir -I/home/<USER>/EnergyStorage/AI_Box/apilib/db/CMakeFiles/DataBase.dir/Implementation -I/home/<USER>/EnergyStorage/AI_Box/apilib/db/Implementation -I/home/<USER>/EnergyStorage/AI_Box/apilib/util -I/home/<USER>/EnergyStorage/AI_Box/apilib/util/CMakeFiles -I/home/<USER>/EnergyStorage/AI_Box/apilib/util/CMakeFiles/Util.dir -I/home/<USER>/EnergyStorage/AI_Box/vision_data.h -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/fmt -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/utf8cpp -std=c++14 -O3 -fPIC -O2 -g -DNDEBUG -std=gnu++14 -fPIC -o CMakeFiles/Util.dir/MessageBuffer.cpp.o -c /home/<USER>/EnergyStorage/AI_Box/apilib/util/MessageBuffer.cpp", "file": "/home/<USER>/EnergyStorage/AI_Box/apilib/util/MessageBuffer.cpp", "output": "apilib/util/CMakeFiles/Util.dir/MessageBuffer.cpp.o"}, {"directory": "/home/<USER>/EnergyStorage/AI_Box/apilib/util", "command": "/usr/bin/c++ -<PERSON><PERSON><PERSON>_STANDALONE -DBOOST_ASIO_STANDALONE -DFMT_USE_DELETED_FUNCTIONS -DFMT_USE_EXTERN_TEMPLATES -DFMT_USE_OVERRIDE -DFMT_USE_RVALUE_REFERENCES -DFMT_USE_VARIADIC_TEMPLATES -DLENDY_API_EXPORT_COMMON -DLENDY_API_USE_DYNAMIC_LIBS -DPROTOCOL_STREAM -DUtil_EXPORTS -I/usr/local/openssl-1.0.2/include -I/home/<USER>/EnergyStorage/AI_Box/apilib -I/home/<USER>/EnergyStorage/AI_Box/apilib/define -I/home/<USER>/EnergyStorage/AI_Box/apilib/define/netdef -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/detail -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/detail/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/experimental -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/experimental/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/generic -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/generic/detail -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/generic/detail/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ip -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ip/detail -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ip/detail/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ip/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/local -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/local/detail -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/local/detail/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/posix -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ssl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ssl/detail -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ssl/detail/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ssl/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ts -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/windows -I/home/<USER>/EnergyStorage/AI_Box/apilib/net -I/home/<USER>/EnergyStorage/AI_Box/apilib/net/CMakeFiles -I/home/<USER>/EnergyStorage/AI_Box/apilib/net/CMakeFiles/Net.dir -I/home/<USER>/EnergyStorage/AI_Box/apilib/db -I/home/<USER>/EnergyStorage/AI_Box/apilib/db/CMakeFiles -I/home/<USER>/EnergyStorage/AI_Box/apilib/db/CMakeFiles/DataBase.dir -I/home/<USER>/EnergyStorage/AI_Box/apilib/db/CMakeFiles/DataBase.dir/Implementation -I/home/<USER>/EnergyStorage/AI_Box/apilib/db/Implementation -I/home/<USER>/EnergyStorage/AI_Box/apilib/util -I/home/<USER>/EnergyStorage/AI_Box/apilib/util/CMakeFiles -I/home/<USER>/EnergyStorage/AI_Box/apilib/util/CMakeFiles/Util.dir -I/home/<USER>/EnergyStorage/AI_Box/vision_data.h -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/fmt -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/utf8cpp -std=c++14 -O3 -fPIC -O2 -g -DNDEBUG -std=gnu++14 -fPIC -o CMakeFiles/Util.dir/SHA1.cpp.o -c /home/<USER>/EnergyStorage/AI_Box/apilib/util/SHA1.cpp", "file": "/home/<USER>/EnergyStorage/AI_Box/apilib/util/SHA1.cpp", "output": "apilib/util/CMakeFiles/Util.dir/SHA1.cpp.o"}, {"directory": "/home/<USER>/EnergyStorage/AI_Box/apilib/util", "command": "/usr/bin/c++ -<PERSON><PERSON><PERSON>_STANDALONE -DBOOST_ASIO_STANDALONE -DFMT_USE_DELETED_FUNCTIONS -DFMT_USE_EXTERN_TEMPLATES -DFMT_USE_OVERRIDE -DFMT_USE_RVALUE_REFERENCES -DFMT_USE_VARIADIC_TEMPLATES -DLENDY_API_EXPORT_COMMON -DLENDY_API_USE_DYNAMIC_LIBS -DPROTOCOL_STREAM -DUtil_EXPORTS -I/usr/local/openssl-1.0.2/include -I/home/<USER>/EnergyStorage/AI_Box/apilib -I/home/<USER>/EnergyStorage/AI_Box/apilib/define -I/home/<USER>/EnergyStorage/AI_Box/apilib/define/netdef -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/detail -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/detail/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/experimental -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/experimental/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/generic -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/generic/detail -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/generic/detail/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ip -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ip/detail -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ip/detail/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ip/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/local -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/local/detail -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/local/detail/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/posix -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ssl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ssl/detail -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ssl/detail/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ssl/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ts -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/windows -I/home/<USER>/EnergyStorage/AI_Box/apilib/net -I/home/<USER>/EnergyStorage/AI_Box/apilib/net/CMakeFiles -I/home/<USER>/EnergyStorage/AI_Box/apilib/net/CMakeFiles/Net.dir -I/home/<USER>/EnergyStorage/AI_Box/apilib/db -I/home/<USER>/EnergyStorage/AI_Box/apilib/db/CMakeFiles -I/home/<USER>/EnergyStorage/AI_Box/apilib/db/CMakeFiles/DataBase.dir -I/home/<USER>/EnergyStorage/AI_Box/apilib/db/CMakeFiles/DataBase.dir/Implementation -I/home/<USER>/EnergyStorage/AI_Box/apilib/db/Implementation -I/home/<USER>/EnergyStorage/AI_Box/apilib/util -I/home/<USER>/EnergyStorage/AI_Box/apilib/util/CMakeFiles -I/home/<USER>/EnergyStorage/AI_Box/apilib/util/CMakeFiles/Util.dir -I/home/<USER>/EnergyStorage/AI_Box/vision_data.h -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/fmt -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/utf8cpp -std=c++14 -O3 -fPIC -O2 -g -DNDEBUG -std=gnu++14 -fPIC -o CMakeFiles/Util.dir/StringUtility.cpp.o -c /home/<USER>/EnergyStorage/AI_Box/apilib/util/StringUtility.cpp", "file": "/home/<USER>/EnergyStorage/AI_Box/apilib/util/StringUtility.cpp", "output": "apilib/util/CMakeFiles/Util.dir/StringUtility.cpp.o"}, {"directory": "/home/<USER>/EnergyStorage/AI_Box/apilib/log", "command": "/usr/bin/c++ -<PERSON><PERSON><PERSON>_STANDALONE -DBOOST_ASIO_STANDALONE -DFMT_USE_DELETED_FUNCTIONS -DFMT_USE_EXTERN_TEMPLATES -DFMT_USE_OVERRIDE -DFMT_USE_RVALUE_REFERENCES -DFMT_USE_VARIADIC_TEMPLATES -DLENDY_API_EXPORT_COMMON -DLENDY_API_USE_DYNAMIC_LIBS -DLog_EXPORTS -DPROTOCOL_STREAM -I/usr/local/openssl-1.0.2/include -I/home/<USER>/EnergyStorage/AI_Box/apilib -I/home/<USER>/EnergyStorage/AI_Box/apilib/define -I/home/<USER>/EnergyStorage/AI_Box/apilib/define/netdef -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/detail -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/detail/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/experimental -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/experimental/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/generic -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/generic/detail -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/generic/detail/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ip -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ip/detail -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ip/detail/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ip/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/local -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/local/detail -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/local/detail/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/posix -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ssl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ssl/detail -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ssl/detail/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ssl/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ts -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/windows -I/home/<USER>/EnergyStorage/AI_Box/apilib/net -I/home/<USER>/EnergyStorage/AI_Box/apilib/net/CMakeFiles -I/home/<USER>/EnergyStorage/AI_Box/apilib/net/CMakeFiles/Net.dir -I/home/<USER>/EnergyStorage/AI_Box/apilib/db -I/home/<USER>/EnergyStorage/AI_Box/apilib/db/CMakeFiles -I/home/<USER>/EnergyStorage/AI_Box/apilib/db/CMakeFiles/DataBase.dir -I/home/<USER>/EnergyStorage/AI_Box/apilib/db/CMakeFiles/DataBase.dir/Implementation -I/home/<USER>/EnergyStorage/AI_Box/apilib/db/Implementation -I/home/<USER>/EnergyStorage/AI_Box/apilib/log -I/home/<USER>/EnergyStorage/AI_Box/apilib/log/CMakeFiles -I/home/<USER>/EnergyStorage/AI_Box/apilib/log/CMakeFiles/Log.dir -I/home/<USER>/EnergyStorage/AI_Box/apilib/util -I/home/<USER>/EnergyStorage/AI_Box/apilib/util/CMakeFiles -I/home/<USER>/EnergyStorage/AI_Box/apilib/util/CMakeFiles/Util.dir -I/home/<USER>/EnergyStorage/AI_Box/vision_data.h -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/fmt -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/utf8cpp -std=c++14 -O3 -fPIC -O2 -g -DNDEBUG -std=gnu++14 -fPIC -o CMakeFiles/Log.dir/Log.cpp.o -c /home/<USER>/EnergyStorage/AI_Box/apilib/log/Log.cpp", "file": "/home/<USER>/EnergyStorage/AI_Box/apilib/log/Log.cpp", "output": "apilib/log/CMakeFiles/Log.dir/Log.cpp.o"}, {"directory": "/home/<USER>/EnergyStorage/AI_Box/apilib/log", "command": "/usr/bin/c++ -<PERSON><PERSON><PERSON>_STANDALONE -DBOOST_ASIO_STANDALONE -DFMT_USE_DELETED_FUNCTIONS -DFMT_USE_EXTERN_TEMPLATES -DFMT_USE_OVERRIDE -DFMT_USE_RVALUE_REFERENCES -DFMT_USE_VARIADIC_TEMPLATES -DLENDY_API_EXPORT_COMMON -DLENDY_API_USE_DYNAMIC_LIBS -DLog_EXPORTS -DPROTOCOL_STREAM -I/usr/local/openssl-1.0.2/include -I/home/<USER>/EnergyStorage/AI_Box/apilib -I/home/<USER>/EnergyStorage/AI_Box/apilib/define -I/home/<USER>/EnergyStorage/AI_Box/apilib/define/netdef -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/detail -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/detail/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/experimental -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/experimental/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/generic -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/generic/detail -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/generic/detail/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ip -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ip/detail -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ip/detail/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ip/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/local -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/local/detail -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/local/detail/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/posix -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ssl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ssl/detail -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ssl/detail/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ssl/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ts -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/windows -I/home/<USER>/EnergyStorage/AI_Box/apilib/net -I/home/<USER>/EnergyStorage/AI_Box/apilib/net/CMakeFiles -I/home/<USER>/EnergyStorage/AI_Box/apilib/net/CMakeFiles/Net.dir -I/home/<USER>/EnergyStorage/AI_Box/apilib/db -I/home/<USER>/EnergyStorage/AI_Box/apilib/db/CMakeFiles -I/home/<USER>/EnergyStorage/AI_Box/apilib/db/CMakeFiles/DataBase.dir -I/home/<USER>/EnergyStorage/AI_Box/apilib/db/CMakeFiles/DataBase.dir/Implementation -I/home/<USER>/EnergyStorage/AI_Box/apilib/db/Implementation -I/home/<USER>/EnergyStorage/AI_Box/apilib/log -I/home/<USER>/EnergyStorage/AI_Box/apilib/log/CMakeFiles -I/home/<USER>/EnergyStorage/AI_Box/apilib/log/CMakeFiles/Log.dir -I/home/<USER>/EnergyStorage/AI_Box/apilib/util -I/home/<USER>/EnergyStorage/AI_Box/apilib/util/CMakeFiles -I/home/<USER>/EnergyStorage/AI_Box/apilib/util/CMakeFiles/Util.dir -I/home/<USER>/EnergyStorage/AI_Box/vision_data.h -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/fmt -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/utf8cpp -std=c++14 -O3 -fPIC -O2 -g -DNDEBUG -std=gnu++14 -fPIC -o CMakeFiles/Log.dir/LogConsole.cpp.o -c /home/<USER>/EnergyStorage/AI_Box/apilib/log/LogConsole.cpp", "file": "/home/<USER>/EnergyStorage/AI_Box/apilib/log/LogConsole.cpp", "output": "apilib/log/CMakeFiles/Log.dir/LogConsole.cpp.o"}, {"directory": "/home/<USER>/EnergyStorage/AI_Box/apilib/log", "command": "/usr/bin/c++ -<PERSON><PERSON><PERSON>_STANDALONE -DBOOST_ASIO_STANDALONE -DFMT_USE_DELETED_FUNCTIONS -DFMT_USE_EXTERN_TEMPLATES -DFMT_USE_OVERRIDE -DFMT_USE_RVALUE_REFERENCES -DFMT_USE_VARIADIC_TEMPLATES -DLENDY_API_EXPORT_COMMON -DLENDY_API_USE_DYNAMIC_LIBS -DLog_EXPORTS -DPROTOCOL_STREAM -I/usr/local/openssl-1.0.2/include -I/home/<USER>/EnergyStorage/AI_Box/apilib -I/home/<USER>/EnergyStorage/AI_Box/apilib/define -I/home/<USER>/EnergyStorage/AI_Box/apilib/define/netdef -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/detail -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/detail/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/experimental -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/experimental/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/generic -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/generic/detail -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/generic/detail/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ip -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ip/detail -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ip/detail/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ip/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/local -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/local/detail -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/local/detail/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/posix -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ssl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ssl/detail -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ssl/detail/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ssl/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ts -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/windows -I/home/<USER>/EnergyStorage/AI_Box/apilib/net -I/home/<USER>/EnergyStorage/AI_Box/apilib/net/CMakeFiles -I/home/<USER>/EnergyStorage/AI_Box/apilib/net/CMakeFiles/Net.dir -I/home/<USER>/EnergyStorage/AI_Box/apilib/db -I/home/<USER>/EnergyStorage/AI_Box/apilib/db/CMakeFiles -I/home/<USER>/EnergyStorage/AI_Box/apilib/db/CMakeFiles/DataBase.dir -I/home/<USER>/EnergyStorage/AI_Box/apilib/db/CMakeFiles/DataBase.dir/Implementation -I/home/<USER>/EnergyStorage/AI_Box/apilib/db/Implementation -I/home/<USER>/EnergyStorage/AI_Box/apilib/log -I/home/<USER>/EnergyStorage/AI_Box/apilib/log/CMakeFiles -I/home/<USER>/EnergyStorage/AI_Box/apilib/log/CMakeFiles/Log.dir -I/home/<USER>/EnergyStorage/AI_Box/apilib/util -I/home/<USER>/EnergyStorage/AI_Box/apilib/util/CMakeFiles -I/home/<USER>/EnergyStorage/AI_Box/apilib/util/CMakeFiles/Util.dir -I/home/<USER>/EnergyStorage/AI_Box/vision_data.h -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/fmt -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/utf8cpp -std=c++14 -O3 -fPIC -O2 -g -DNDEBUG -std=gnu++14 -fPIC -o CMakeFiles/Log.dir/LogFile.cpp.o -c /home/<USER>/EnergyStorage/AI_Box/apilib/log/LogFile.cpp", "file": "/home/<USER>/EnergyStorage/AI_Box/apilib/log/LogFile.cpp", "output": "apilib/log/CMakeFiles/Log.dir/LogFile.cpp.o"}, {"directory": "/home/<USER>/EnergyStorage/AI_Box/apilib/log", "command": "/usr/bin/c++ -<PERSON><PERSON><PERSON>_STANDALONE -DBOOST_ASIO_STANDALONE -DFMT_USE_DELETED_FUNCTIONS -DFMT_USE_EXTERN_TEMPLATES -DFMT_USE_OVERRIDE -DFMT_USE_RVALUE_REFERENCES -DFMT_USE_VARIADIC_TEMPLATES -DLENDY_API_EXPORT_COMMON -DLENDY_API_USE_DYNAMIC_LIBS -DLog_EXPORTS -DPROTOCOL_STREAM -I/usr/local/openssl-1.0.2/include -I/home/<USER>/EnergyStorage/AI_Box/apilib -I/home/<USER>/EnergyStorage/AI_Box/apilib/define -I/home/<USER>/EnergyStorage/AI_Box/apilib/define/netdef -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/detail -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/detail/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/experimental -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/experimental/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/generic -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/generic/detail -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/generic/detail/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ip -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ip/detail -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ip/detail/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ip/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/local -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/local/detail -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/local/detail/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/posix -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ssl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ssl/detail -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ssl/detail/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ssl/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ts -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/windows -I/home/<USER>/EnergyStorage/AI_Box/apilib/net -I/home/<USER>/EnergyStorage/AI_Box/apilib/net/CMakeFiles -I/home/<USER>/EnergyStorage/AI_Box/apilib/net/CMakeFiles/Net.dir -I/home/<USER>/EnergyStorage/AI_Box/apilib/db -I/home/<USER>/EnergyStorage/AI_Box/apilib/db/CMakeFiles -I/home/<USER>/EnergyStorage/AI_Box/apilib/db/CMakeFiles/DataBase.dir -I/home/<USER>/EnergyStorage/AI_Box/apilib/db/CMakeFiles/DataBase.dir/Implementation -I/home/<USER>/EnergyStorage/AI_Box/apilib/db/Implementation -I/home/<USER>/EnergyStorage/AI_Box/apilib/log -I/home/<USER>/EnergyStorage/AI_Box/apilib/log/CMakeFiles -I/home/<USER>/EnergyStorage/AI_Box/apilib/log/CMakeFiles/Log.dir -I/home/<USER>/EnergyStorage/AI_Box/apilib/util -I/home/<USER>/EnergyStorage/AI_Box/apilib/util/CMakeFiles -I/home/<USER>/EnergyStorage/AI_Box/apilib/util/CMakeFiles/Util.dir -I/home/<USER>/EnergyStorage/AI_Box/vision_data.h -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/fmt -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/utf8cpp -std=c++14 -O3 -fPIC -O2 -g -DNDEBUG -std=gnu++14 -fPIC -o CMakeFiles/Log.dir/LogMessage.cpp.o -c /home/<USER>/EnergyStorage/AI_Box/apilib/log/LogMessage.cpp", "file": "/home/<USER>/EnergyStorage/AI_Box/apilib/log/LogMessage.cpp", "output": "apilib/log/CMakeFiles/Log.dir/LogMessage.cpp.o"}, {"directory": "/home/<USER>/EnergyStorage/AI_Box/apilib/net", "command": "/usr/bin/c++ -<PERSON><PERSON><PERSON>_STANDALONE -DBOOST_ASIO_STANDALONE -DFMT_USE_DELETED_FUNCTIONS -DFMT_USE_EXTERN_TEMPLATES -DFMT_USE_OVERRIDE -DFMT_USE_RVALUE_REFERENCES -DFMT_USE_VARIADIC_TEMPLATES -DLENDY_API_EXPORT_COMMON -DLENDY_API_USE_DYNAMIC_LIBS -DNet_EXPORTS -DPROTOCOL_STREAM -I/usr/local/openssl-1.0.2/include -I/home/<USER>/EnergyStorage/AI_Box/apilib -I/home/<USER>/EnergyStorage/AI_Box/apilib/define -I/home/<USER>/EnergyStorage/AI_Box/apilib/define/netdef -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/detail -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/detail/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/experimental -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/experimental/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/generic -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/generic/detail -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/generic/detail/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ip -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ip/detail -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ip/detail/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ip/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/local -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/local/detail -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/local/detail/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/posix -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ssl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ssl/detail -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ssl/detail/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ssl/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ts -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/windows -I/home/<USER>/EnergyStorage/AI_Box/apilib/net -I/home/<USER>/EnergyStorage/AI_Box/apilib/net/CMakeFiles -I/home/<USER>/EnergyStorage/AI_Box/apilib/net/CMakeFiles/Net.dir -I/home/<USER>/EnergyStorage/AI_Box/apilib/db -I/home/<USER>/EnergyStorage/AI_Box/apilib/db/CMakeFiles -I/home/<USER>/EnergyStorage/AI_Box/apilib/db/CMakeFiles/DataBase.dir -I/home/<USER>/EnergyStorage/AI_Box/apilib/db/CMakeFiles/DataBase.dir/Implementation -I/home/<USER>/EnergyStorage/AI_Box/apilib/db/Implementation -I/home/<USER>/EnergyStorage/AI_Box/apilib/log -I/home/<USER>/EnergyStorage/AI_Box/apilib/log/CMakeFiles -I/home/<USER>/EnergyStorage/AI_Box/apilib/log/CMakeFiles/Log.dir -I/home/<USER>/EnergyStorage/AI_Box/apilib/util -I/home/<USER>/EnergyStorage/AI_Box/apilib/util/CMakeFiles -I/home/<USER>/EnergyStorage/AI_Box/apilib/util/CMakeFiles/Util.dir -I/home/<USER>/EnergyStorage/AI_Box/vision_data.h -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/fmt -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/utf8cpp -std=c++14 -O3 -fPIC -O2 -g -DNDEBUG -std=gnu++14 -fPIC -o CMakeFiles/Net.dir/AsynchronismEngine.cpp.o -c /home/<USER>/EnergyStorage/AI_Box/apilib/net/AsynchronismEngine.cpp", "file": "/home/<USER>/EnergyStorage/AI_Box/apilib/net/AsynchronismEngine.cpp", "output": "apilib/net/CMakeFiles/Net.dir/AsynchronismEngine.cpp.o"}, {"directory": "/home/<USER>/EnergyStorage/AI_Box/apilib/net", "command": "/usr/bin/c++ -<PERSON><PERSON><PERSON>_STANDALONE -DBOOST_ASIO_STANDALONE -DFMT_USE_DELETED_FUNCTIONS -DFMT_USE_EXTERN_TEMPLATES -DFMT_USE_OVERRIDE -DFMT_USE_RVALUE_REFERENCES -DFMT_USE_VARIADIC_TEMPLATES -DLENDY_API_EXPORT_COMMON -DLENDY_API_USE_DYNAMIC_LIBS -DNet_EXPORTS -DPROTOCOL_STREAM -I/usr/local/openssl-1.0.2/include -I/home/<USER>/EnergyStorage/AI_Box/apilib -I/home/<USER>/EnergyStorage/AI_Box/apilib/define -I/home/<USER>/EnergyStorage/AI_Box/apilib/define/netdef -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/detail -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/detail/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/experimental -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/experimental/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/generic -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/generic/detail -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/generic/detail/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ip -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ip/detail -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ip/detail/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ip/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/local -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/local/detail -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/local/detail/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/posix -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ssl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ssl/detail -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ssl/detail/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ssl/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ts -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/windows -I/home/<USER>/EnergyStorage/AI_Box/apilib/net -I/home/<USER>/EnergyStorage/AI_Box/apilib/net/CMakeFiles -I/home/<USER>/EnergyStorage/AI_Box/apilib/net/CMakeFiles/Net.dir -I/home/<USER>/EnergyStorage/AI_Box/apilib/db -I/home/<USER>/EnergyStorage/AI_Box/apilib/db/CMakeFiles -I/home/<USER>/EnergyStorage/AI_Box/apilib/db/CMakeFiles/DataBase.dir -I/home/<USER>/EnergyStorage/AI_Box/apilib/db/CMakeFiles/DataBase.dir/Implementation -I/home/<USER>/EnergyStorage/AI_Box/apilib/db/Implementation -I/home/<USER>/EnergyStorage/AI_Box/apilib/log -I/home/<USER>/EnergyStorage/AI_Box/apilib/log/CMakeFiles -I/home/<USER>/EnergyStorage/AI_Box/apilib/log/CMakeFiles/Log.dir -I/home/<USER>/EnergyStorage/AI_Box/apilib/util -I/home/<USER>/EnergyStorage/AI_Box/apilib/util/CMakeFiles -I/home/<USER>/EnergyStorage/AI_Box/apilib/util/CMakeFiles/Util.dir -I/home/<USER>/EnergyStorage/AI_Box/vision_data.h -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/fmt -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/utf8cpp -std=c++14 -O3 -fPIC -O2 -g -DNDEBUG -std=gnu++14 -fPIC -o CMakeFiles/Net.dir/AttemperEngine.cpp.o -c /home/<USER>/EnergyStorage/AI_Box/apilib/net/AttemperEngine.cpp", "file": "/home/<USER>/EnergyStorage/AI_Box/apilib/net/AttemperEngine.cpp", "output": "apilib/net/CMakeFiles/Net.dir/AttemperEngine.cpp.o"}, {"directory": "/home/<USER>/EnergyStorage/AI_Box/apilib/net", "command": "/usr/bin/c++ -<PERSON><PERSON><PERSON>_STANDALONE -DBOOST_ASIO_STANDALONE -DFMT_USE_DELETED_FUNCTIONS -DFMT_USE_EXTERN_TEMPLATES -DFMT_USE_OVERRIDE -DFMT_USE_RVALUE_REFERENCES -DFMT_USE_VARIADIC_TEMPLATES -DLENDY_API_EXPORT_COMMON -DLENDY_API_USE_DYNAMIC_LIBS -DNet_EXPORTS -DPROTOCOL_STREAM -I/usr/local/openssl-1.0.2/include -I/home/<USER>/EnergyStorage/AI_Box/apilib -I/home/<USER>/EnergyStorage/AI_Box/apilib/define -I/home/<USER>/EnergyStorage/AI_Box/apilib/define/netdef -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/detail -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/detail/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/experimental -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/experimental/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/generic -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/generic/detail -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/generic/detail/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ip -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ip/detail -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ip/detail/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ip/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/local -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/local/detail -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/local/detail/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/posix -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ssl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ssl/detail -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ssl/detail/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ssl/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ts -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/windows -I/home/<USER>/EnergyStorage/AI_Box/apilib/net -I/home/<USER>/EnergyStorage/AI_Box/apilib/net/CMakeFiles -I/home/<USER>/EnergyStorage/AI_Box/apilib/net/CMakeFiles/Net.dir -I/home/<USER>/EnergyStorage/AI_Box/apilib/db -I/home/<USER>/EnergyStorage/AI_Box/apilib/db/CMakeFiles -I/home/<USER>/EnergyStorage/AI_Box/apilib/db/CMakeFiles/DataBase.dir -I/home/<USER>/EnergyStorage/AI_Box/apilib/db/CMakeFiles/DataBase.dir/Implementation -I/home/<USER>/EnergyStorage/AI_Box/apilib/db/Implementation -I/home/<USER>/EnergyStorage/AI_Box/apilib/log -I/home/<USER>/EnergyStorage/AI_Box/apilib/log/CMakeFiles -I/home/<USER>/EnergyStorage/AI_Box/apilib/log/CMakeFiles/Log.dir -I/home/<USER>/EnergyStorage/AI_Box/apilib/util -I/home/<USER>/EnergyStorage/AI_Box/apilib/util/CMakeFiles -I/home/<USER>/EnergyStorage/AI_Box/apilib/util/CMakeFiles/Util.dir -I/home/<USER>/EnergyStorage/AI_Box/vision_data.h -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/fmt -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/utf8cpp -std=c++14 -O3 -fPIC -O2 -g -DNDEBUG -std=gnu++14 -fPIC -o CMakeFiles/Net.dir/TCPNetworkEngine.cpp.o -c /home/<USER>/EnergyStorage/AI_Box/apilib/net/TCPNetworkEngine.cpp", "file": "/home/<USER>/EnergyStorage/AI_Box/apilib/net/TCPNetworkEngine.cpp", "output": "apilib/net/CMakeFiles/Net.dir/TCPNetworkEngine.cpp.o"}, {"directory": "/home/<USER>/EnergyStorage/AI_Box/apilib/net", "command": "/usr/bin/c++ -<PERSON><PERSON><PERSON>_STANDALONE -DBOOST_ASIO_STANDALONE -DFMT_USE_DELETED_FUNCTIONS -DFMT_USE_EXTERN_TEMPLATES -DFMT_USE_OVERRIDE -DFMT_USE_RVALUE_REFERENCES -DFMT_USE_VARIADIC_TEMPLATES -DLENDY_API_EXPORT_COMMON -DLENDY_API_USE_DYNAMIC_LIBS -DNet_EXPORTS -DPROTOCOL_STREAM -I/usr/local/openssl-1.0.2/include -I/home/<USER>/EnergyStorage/AI_Box/apilib -I/home/<USER>/EnergyStorage/AI_Box/apilib/define -I/home/<USER>/EnergyStorage/AI_Box/apilib/define/netdef -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/detail -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/detail/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/experimental -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/experimental/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/generic -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/generic/detail -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/generic/detail/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ip -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ip/detail -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ip/detail/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ip/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/local -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/local/detail -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/local/detail/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/posix -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ssl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ssl/detail -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ssl/detail/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ssl/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ts -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/windows -I/home/<USER>/EnergyStorage/AI_Box/apilib/net -I/home/<USER>/EnergyStorage/AI_Box/apilib/net/CMakeFiles -I/home/<USER>/EnergyStorage/AI_Box/apilib/net/CMakeFiles/Net.dir -I/home/<USER>/EnergyStorage/AI_Box/apilib/db -I/home/<USER>/EnergyStorage/AI_Box/apilib/db/CMakeFiles -I/home/<USER>/EnergyStorage/AI_Box/apilib/db/CMakeFiles/DataBase.dir -I/home/<USER>/EnergyStorage/AI_Box/apilib/db/CMakeFiles/DataBase.dir/Implementation -I/home/<USER>/EnergyStorage/AI_Box/apilib/db/Implementation -I/home/<USER>/EnergyStorage/AI_Box/apilib/log -I/home/<USER>/EnergyStorage/AI_Box/apilib/log/CMakeFiles -I/home/<USER>/EnergyStorage/AI_Box/apilib/log/CMakeFiles/Log.dir -I/home/<USER>/EnergyStorage/AI_Box/apilib/util -I/home/<USER>/EnergyStorage/AI_Box/apilib/util/CMakeFiles -I/home/<USER>/EnergyStorage/AI_Box/apilib/util/CMakeFiles/Util.dir -I/home/<USER>/EnergyStorage/AI_Box/vision_data.h -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/fmt -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/utf8cpp -std=c++14 -O3 -fPIC -O2 -g -DNDEBUG -std=gnu++14 -fPIC -o CMakeFiles/Net.dir/TCPNetworkItem.cpp.o -c /home/<USER>/EnergyStorage/AI_Box/apilib/net/TCPNetworkItem.cpp", "file": "/home/<USER>/EnergyStorage/AI_Box/apilib/net/TCPNetworkItem.cpp", "output": "apilib/net/CMakeFiles/Net.dir/TCPNetworkItem.cpp.o"}, {"directory": "/home/<USER>/EnergyStorage/AI_Box/apilib/net", "command": "/usr/bin/c++ -<PERSON><PERSON><PERSON>_STANDALONE -DBOOST_ASIO_STANDALONE -DFMT_USE_DELETED_FUNCTIONS -DFMT_USE_EXTERN_TEMPLATES -DFMT_USE_OVERRIDE -DFMT_USE_RVALUE_REFERENCES -DFMT_USE_VARIADIC_TEMPLATES -DLENDY_API_EXPORT_COMMON -DLENDY_API_USE_DYNAMIC_LIBS -DNet_EXPORTS -DPROTOCOL_STREAM -I/usr/local/openssl-1.0.2/include -I/home/<USER>/EnergyStorage/AI_Box/apilib -I/home/<USER>/EnergyStorage/AI_Box/apilib/define -I/home/<USER>/EnergyStorage/AI_Box/apilib/define/netdef -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/detail -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/detail/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/experimental -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/experimental/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/generic -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/generic/detail -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/generic/detail/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ip -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ip/detail -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ip/detail/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ip/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/local -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/local/detail -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/local/detail/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/posix -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ssl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ssl/detail -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ssl/detail/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ssl/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ts -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/windows -I/home/<USER>/EnergyStorage/AI_Box/apilib/net -I/home/<USER>/EnergyStorage/AI_Box/apilib/net/CMakeFiles -I/home/<USER>/EnergyStorage/AI_Box/apilib/net/CMakeFiles/Net.dir -I/home/<USER>/EnergyStorage/AI_Box/apilib/db -I/home/<USER>/EnergyStorage/AI_Box/apilib/db/CMakeFiles -I/home/<USER>/EnergyStorage/AI_Box/apilib/db/CMakeFiles/DataBase.dir -I/home/<USER>/EnergyStorage/AI_Box/apilib/db/CMakeFiles/DataBase.dir/Implementation -I/home/<USER>/EnergyStorage/AI_Box/apilib/db/Implementation -I/home/<USER>/EnergyStorage/AI_Box/apilib/log -I/home/<USER>/EnergyStorage/AI_Box/apilib/log/CMakeFiles -I/home/<USER>/EnergyStorage/AI_Box/apilib/log/CMakeFiles/Log.dir -I/home/<USER>/EnergyStorage/AI_Box/apilib/util -I/home/<USER>/EnergyStorage/AI_Box/apilib/util/CMakeFiles -I/home/<USER>/EnergyStorage/AI_Box/apilib/util/CMakeFiles/Util.dir -I/home/<USER>/EnergyStorage/AI_Box/vision_data.h -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/fmt -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/utf8cpp -std=c++14 -O3 -fPIC -O2 -g -DNDEBUG -std=gnu++14 -fPIC -o CMakeFiles/Net.dir/TCPSocketService.cpp.o -c /home/<USER>/EnergyStorage/AI_Box/apilib/net/TCPSocketService.cpp", "file": "/home/<USER>/EnergyStorage/AI_Box/apilib/net/TCPSocketService.cpp", "output": "apilib/net/CMakeFiles/Net.dir/TCPSocketService.cpp.o"}, {"directory": "/home/<USER>/EnergyStorage/AI_Box/apilib/net", "command": "/usr/bin/c++ -<PERSON><PERSON><PERSON>_STANDALONE -DBOOST_ASIO_STANDALONE -DFMT_USE_DELETED_FUNCTIONS -DFMT_USE_EXTERN_TEMPLATES -DFMT_USE_OVERRIDE -DFMT_USE_RVALUE_REFERENCES -DFMT_USE_VARIADIC_TEMPLATES -DLENDY_API_EXPORT_COMMON -DLENDY_API_USE_DYNAMIC_LIBS -DNet_EXPORTS -DPROTOCOL_STREAM -I/usr/local/openssl-1.0.2/include -I/home/<USER>/EnergyStorage/AI_Box/apilib -I/home/<USER>/EnergyStorage/AI_Box/apilib/define -I/home/<USER>/EnergyStorage/AI_Box/apilib/define/netdef -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/detail -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/detail/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/experimental -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/experimental/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/generic -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/generic/detail -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/generic/detail/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ip -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ip/detail -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ip/detail/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ip/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/local -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/local/detail -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/local/detail/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/posix -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ssl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ssl/detail -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ssl/detail/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ssl/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ts -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/windows -I/home/<USER>/EnergyStorage/AI_Box/apilib/net -I/home/<USER>/EnergyStorage/AI_Box/apilib/net/CMakeFiles -I/home/<USER>/EnergyStorage/AI_Box/apilib/net/CMakeFiles/Net.dir -I/home/<USER>/EnergyStorage/AI_Box/apilib/db -I/home/<USER>/EnergyStorage/AI_Box/apilib/db/CMakeFiles -I/home/<USER>/EnergyStorage/AI_Box/apilib/db/CMakeFiles/DataBase.dir -I/home/<USER>/EnergyStorage/AI_Box/apilib/db/CMakeFiles/DataBase.dir/Implementation -I/home/<USER>/EnergyStorage/AI_Box/apilib/db/Implementation -I/home/<USER>/EnergyStorage/AI_Box/apilib/log -I/home/<USER>/EnergyStorage/AI_Box/apilib/log/CMakeFiles -I/home/<USER>/EnergyStorage/AI_Box/apilib/log/CMakeFiles/Log.dir -I/home/<USER>/EnergyStorage/AI_Box/apilib/util -I/home/<USER>/EnergyStorage/AI_Box/apilib/util/CMakeFiles -I/home/<USER>/EnergyStorage/AI_Box/apilib/util/CMakeFiles/Util.dir -I/home/<USER>/EnergyStorage/AI_Box/vision_data.h -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/fmt -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/utf8cpp -std=c++14 -O3 -fPIC -O2 -g -DNDEBUG -std=gnu++14 -fPIC -o CMakeFiles/Net.dir/TimerEngine.cpp.o -c /home/<USER>/EnergyStorage/AI_Box/apilib/net/TimerEngine.cpp", "file": "/home/<USER>/EnergyStorage/AI_Box/apilib/net/TimerEngine.cpp", "output": "apilib/net/CMakeFiles/Net.dir/TimerEngine.cpp.o"}, {"directory": "/home/<USER>/EnergyStorage/AI_Box/apilib/db", "command": "/usr/bin/c++ -<PERSON><PERSON><PERSON>_STANDALONE -DBOOST_ASIO_STANDALONE -DDataBase_EXPORTS -DFMT_USE_DELETED_FUNCTIONS -DFMT_USE_EXTERN_TEMPLATES -DFMT_USE_OVERRIDE -DFMT_USE_RVALUE_REFERENCES -DFMT_USE_VARIADIC_TEMPLATES -DLENDY_API_EXPORT_COMMON -DLENDY_API_USE_DYNAMIC_LIBS -DPROTOCOL_STREAM -I/usr/local/openssl-1.0.2/include -I/home/<USER>/EnergyStorage/AI_Box/apilib -I/home/<USER>/EnergyStorage/AI_Box/apilib/define -I/home/<USER>/EnergyStorage/AI_Box/apilib/define/netdef -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/detail -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/detail/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/experimental -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/experimental/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/generic -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/generic/detail -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/generic/detail/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ip -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ip/detail -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ip/detail/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ip/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/local -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/local/detail -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/local/detail/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/posix -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ssl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ssl/detail -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ssl/detail/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ssl/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ts -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/windows -I/home/<USER>/EnergyStorage/AI_Box/apilib/net -I/home/<USER>/EnergyStorage/AI_Box/apilib/net/CMakeFiles -I/home/<USER>/EnergyStorage/AI_Box/apilib/net/CMakeFiles/Net.dir -I/home/<USER>/EnergyStorage/AI_Box/apilib/db -I/home/<USER>/EnergyStorage/AI_Box/apilib/db/CMakeFiles -I/home/<USER>/EnergyStorage/AI_Box/apilib/db/CMakeFiles/DataBase.dir -I/home/<USER>/EnergyStorage/AI_Box/apilib/db/CMakeFiles/DataBase.dir/Implementation -I/home/<USER>/EnergyStorage/AI_Box/apilib/db/Implementation -I/home/<USER>/EnergyStorage/AI_Box/apilib/log -I/home/<USER>/EnergyStorage/AI_Box/apilib/log/CMakeFiles -I/home/<USER>/EnergyStorage/AI_Box/apilib/log/CMakeFiles/Log.dir -I/home/<USER>/EnergyStorage/AI_Box/apilib/util -I/home/<USER>/EnergyStorage/AI_Box/apilib/util/CMakeFiles -I/home/<USER>/EnergyStorage/AI_Box/apilib/util/CMakeFiles/Util.dir -I/home/<USER>/EnergyStorage/AI_Box/vision_data.h -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/fmt -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/utf8cpp -isystem /usr/include/mysql -std=c++14 -O3 -fPIC -O2 -g -DNDEBUG -std=gnu++14 -fPIC -o CMakeFiles/DataBase.dir/DBExports.cpp.o -c /home/<USER>/EnergyStorage/AI_Box/apilib/db/DBExports.cpp", "file": "/home/<USER>/EnergyStorage/AI_Box/apilib/db/DBExports.cpp", "output": "apilib/db/CMakeFiles/DataBase.dir/DBExports.cpp.o"}, {"directory": "/home/<USER>/EnergyStorage/AI_Box/apilib/db", "command": "/usr/bin/c++ -<PERSON><PERSON><PERSON>_STANDALONE -DBOOST_ASIO_STANDALONE -DDataBase_EXPORTS -DFMT_USE_DELETED_FUNCTIONS -DFMT_USE_EXTERN_TEMPLATES -DFMT_USE_OVERRIDE -DFMT_USE_RVALUE_REFERENCES -DFMT_USE_VARIADIC_TEMPLATES -DLENDY_API_EXPORT_COMMON -DLENDY_API_USE_DYNAMIC_LIBS -DPROTOCOL_STREAM -I/usr/local/openssl-1.0.2/include -I/home/<USER>/EnergyStorage/AI_Box/apilib -I/home/<USER>/EnergyStorage/AI_Box/apilib/define -I/home/<USER>/EnergyStorage/AI_Box/apilib/define/netdef -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/detail -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/detail/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/experimental -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/experimental/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/generic -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/generic/detail -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/generic/detail/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ip -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ip/detail -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ip/detail/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ip/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/local -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/local/detail -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/local/detail/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/posix -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ssl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ssl/detail -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ssl/detail/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ssl/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ts -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/windows -I/home/<USER>/EnergyStorage/AI_Box/apilib/net -I/home/<USER>/EnergyStorage/AI_Box/apilib/net/CMakeFiles -I/home/<USER>/EnergyStorage/AI_Box/apilib/net/CMakeFiles/Net.dir -I/home/<USER>/EnergyStorage/AI_Box/apilib/db -I/home/<USER>/EnergyStorage/AI_Box/apilib/db/CMakeFiles -I/home/<USER>/EnergyStorage/AI_Box/apilib/db/CMakeFiles/DataBase.dir -I/home/<USER>/EnergyStorage/AI_Box/apilib/db/CMakeFiles/DataBase.dir/Implementation -I/home/<USER>/EnergyStorage/AI_Box/apilib/db/Implementation -I/home/<USER>/EnergyStorage/AI_Box/apilib/log -I/home/<USER>/EnergyStorage/AI_Box/apilib/log/CMakeFiles -I/home/<USER>/EnergyStorage/AI_Box/apilib/log/CMakeFiles/Log.dir -I/home/<USER>/EnergyStorage/AI_Box/apilib/util -I/home/<USER>/EnergyStorage/AI_Box/apilib/util/CMakeFiles -I/home/<USER>/EnergyStorage/AI_Box/apilib/util/CMakeFiles/Util.dir -I/home/<USER>/EnergyStorage/AI_Box/vision_data.h -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/fmt -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/utf8cpp -isystem /usr/include/mysql -std=c++14 -O3 -fPIC -O2 -g -DNDEBUG -std=gnu++14 -fPIC -o CMakeFiles/DataBase.dir/DBUpdater.cpp.o -c /home/<USER>/EnergyStorage/AI_Box/apilib/db/DBUpdater.cpp", "file": "/home/<USER>/EnergyStorage/AI_Box/apilib/db/DBUpdater.cpp", "output": "apilib/db/CMakeFiles/DataBase.dir/DBUpdater.cpp.o"}, {"directory": "/home/<USER>/EnergyStorage/AI_Box/apilib/db", "command": "/usr/bin/c++ -<PERSON><PERSON><PERSON>_STANDALONE -DBOOST_ASIO_STANDALONE -DDataBase_EXPORTS -DFMT_USE_DELETED_FUNCTIONS -DFMT_USE_EXTERN_TEMPLATES -DFMT_USE_OVERRIDE -DFMT_USE_RVALUE_REFERENCES -DFMT_USE_VARIADIC_TEMPLATES -DLENDY_API_EXPORT_COMMON -DLENDY_API_USE_DYNAMIC_LIBS -DPROTOCOL_STREAM -I/usr/local/openssl-1.0.2/include -I/home/<USER>/EnergyStorage/AI_Box/apilib -I/home/<USER>/EnergyStorage/AI_Box/apilib/define -I/home/<USER>/EnergyStorage/AI_Box/apilib/define/netdef -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/detail -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/detail/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/experimental -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/experimental/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/generic -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/generic/detail -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/generic/detail/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ip -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ip/detail -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ip/detail/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ip/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/local -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/local/detail -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/local/detail/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/posix -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ssl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ssl/detail -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ssl/detail/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ssl/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ts -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/windows -I/home/<USER>/EnergyStorage/AI_Box/apilib/net -I/home/<USER>/EnergyStorage/AI_Box/apilib/net/CMakeFiles -I/home/<USER>/EnergyStorage/AI_Box/apilib/net/CMakeFiles/Net.dir -I/home/<USER>/EnergyStorage/AI_Box/apilib/db -I/home/<USER>/EnergyStorage/AI_Box/apilib/db/CMakeFiles -I/home/<USER>/EnergyStorage/AI_Box/apilib/db/CMakeFiles/DataBase.dir -I/home/<USER>/EnergyStorage/AI_Box/apilib/db/CMakeFiles/DataBase.dir/Implementation -I/home/<USER>/EnergyStorage/AI_Box/apilib/db/Implementation -I/home/<USER>/EnergyStorage/AI_Box/apilib/log -I/home/<USER>/EnergyStorage/AI_Box/apilib/log/CMakeFiles -I/home/<USER>/EnergyStorage/AI_Box/apilib/log/CMakeFiles/Log.dir -I/home/<USER>/EnergyStorage/AI_Box/apilib/util -I/home/<USER>/EnergyStorage/AI_Box/apilib/util/CMakeFiles -I/home/<USER>/EnergyStorage/AI_Box/apilib/util/CMakeFiles/Util.dir -I/home/<USER>/EnergyStorage/AI_Box/vision_data.h -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/fmt -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/utf8cpp -isystem /usr/include/mysql -std=c++14 -O3 -fPIC -O2 -g -DNDEBUG -std=gnu++14 -fPIC -o CMakeFiles/DataBase.dir/DBWorker.cpp.o -c /home/<USER>/EnergyStorage/AI_Box/apilib/db/DBWorker.cpp", "file": "/home/<USER>/EnergyStorage/AI_Box/apilib/db/DBWorker.cpp", "output": "apilib/db/CMakeFiles/DataBase.dir/DBWorker.cpp.o"}, {"directory": "/home/<USER>/EnergyStorage/AI_Box/apilib/db", "command": "/usr/bin/c++ -<PERSON><PERSON><PERSON>_STANDALONE -DBOOST_ASIO_STANDALONE -DDataBase_EXPORTS -DFMT_USE_DELETED_FUNCTIONS -DFMT_USE_EXTERN_TEMPLATES -DFMT_USE_OVERRIDE -DFMT_USE_RVALUE_REFERENCES -DFMT_USE_VARIADIC_TEMPLATES -DLENDY_API_EXPORT_COMMON -DLENDY_API_USE_DYNAMIC_LIBS -DPROTOCOL_STREAM -I/usr/local/openssl-1.0.2/include -I/home/<USER>/EnergyStorage/AI_Box/apilib -I/home/<USER>/EnergyStorage/AI_Box/apilib/define -I/home/<USER>/EnergyStorage/AI_Box/apilib/define/netdef -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/detail -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/detail/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/experimental -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/experimental/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/generic -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/generic/detail -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/generic/detail/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ip -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ip/detail -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ip/detail/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ip/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/local -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/local/detail -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/local/detail/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/posix -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ssl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ssl/detail -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ssl/detail/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ssl/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ts -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/windows -I/home/<USER>/EnergyStorage/AI_Box/apilib/net -I/home/<USER>/EnergyStorage/AI_Box/apilib/net/CMakeFiles -I/home/<USER>/EnergyStorage/AI_Box/apilib/net/CMakeFiles/Net.dir -I/home/<USER>/EnergyStorage/AI_Box/apilib/db -I/home/<USER>/EnergyStorage/AI_Box/apilib/db/CMakeFiles -I/home/<USER>/EnergyStorage/AI_Box/apilib/db/CMakeFiles/DataBase.dir -I/home/<USER>/EnergyStorage/AI_Box/apilib/db/CMakeFiles/DataBase.dir/Implementation -I/home/<USER>/EnergyStorage/AI_Box/apilib/db/Implementation -I/home/<USER>/EnergyStorage/AI_Box/apilib/log -I/home/<USER>/EnergyStorage/AI_Box/apilib/log/CMakeFiles -I/home/<USER>/EnergyStorage/AI_Box/apilib/log/CMakeFiles/Log.dir -I/home/<USER>/EnergyStorage/AI_Box/apilib/util -I/home/<USER>/EnergyStorage/AI_Box/apilib/util/CMakeFiles -I/home/<USER>/EnergyStorage/AI_Box/apilib/util/CMakeFiles/Util.dir -I/home/<USER>/EnergyStorage/AI_Box/vision_data.h -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/fmt -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/utf8cpp -isystem /usr/include/mysql -std=c++14 -O3 -fPIC -O2 -g -DNDEBUG -std=gnu++14 -fPIC -o CMakeFiles/DataBase.dir/DBWorkerPool.cpp.o -c /home/<USER>/EnergyStorage/AI_Box/apilib/db/DBWorkerPool.cpp", "file": "/home/<USER>/EnergyStorage/AI_Box/apilib/db/DBWorkerPool.cpp", "output": "apilib/db/CMakeFiles/DataBase.dir/DBWorkerPool.cpp.o"}, {"directory": "/home/<USER>/EnergyStorage/AI_Box/apilib/db", "command": "/usr/bin/c++ -<PERSON><PERSON><PERSON>_STANDALONE -DBOOST_ASIO_STANDALONE -DDataBase_EXPORTS -DFMT_USE_DELETED_FUNCTIONS -DFMT_USE_EXTERN_TEMPLATES -DFMT_USE_OVERRIDE -DFMT_USE_RVALUE_REFERENCES -DFMT_USE_VARIADIC_TEMPLATES -DLENDY_API_EXPORT_COMMON -DLENDY_API_USE_DYNAMIC_LIBS -DPROTOCOL_STREAM -I/usr/local/openssl-1.0.2/include -I/home/<USER>/EnergyStorage/AI_Box/apilib -I/home/<USER>/EnergyStorage/AI_Box/apilib/define -I/home/<USER>/EnergyStorage/AI_Box/apilib/define/netdef -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/detail -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/detail/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/experimental -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/experimental/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/generic -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/generic/detail -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/generic/detail/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ip -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ip/detail -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ip/detail/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ip/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/local -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/local/detail -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/local/detail/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/posix -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ssl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ssl/detail -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ssl/detail/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ssl/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ts -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/windows -I/home/<USER>/EnergyStorage/AI_Box/apilib/net -I/home/<USER>/EnergyStorage/AI_Box/apilib/net/CMakeFiles -I/home/<USER>/EnergyStorage/AI_Box/apilib/net/CMakeFiles/Net.dir -I/home/<USER>/EnergyStorage/AI_Box/apilib/db -I/home/<USER>/EnergyStorage/AI_Box/apilib/db/CMakeFiles -I/home/<USER>/EnergyStorage/AI_Box/apilib/db/CMakeFiles/DataBase.dir -I/home/<USER>/EnergyStorage/AI_Box/apilib/db/CMakeFiles/DataBase.dir/Implementation -I/home/<USER>/EnergyStorage/AI_Box/apilib/db/Implementation -I/home/<USER>/EnergyStorage/AI_Box/apilib/log -I/home/<USER>/EnergyStorage/AI_Box/apilib/log/CMakeFiles -I/home/<USER>/EnergyStorage/AI_Box/apilib/log/CMakeFiles/Log.dir -I/home/<USER>/EnergyStorage/AI_Box/apilib/util -I/home/<USER>/EnergyStorage/AI_Box/apilib/util/CMakeFiles -I/home/<USER>/EnergyStorage/AI_Box/apilib/util/CMakeFiles/Util.dir -I/home/<USER>/EnergyStorage/AI_Box/vision_data.h -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/fmt -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/utf8cpp -isystem /usr/include/mysql -std=c++14 -O3 -fPIC -O2 -g -DNDEBUG -std=gnu++14 -fPIC -o CMakeFiles/DataBase.dir/Field.cpp.o -c /home/<USER>/EnergyStorage/AI_Box/apilib/db/Field.cpp", "file": "/home/<USER>/EnergyStorage/AI_Box/apilib/db/Field.cpp", "output": "apilib/db/CMakeFiles/DataBase.dir/Field.cpp.o"}, {"directory": "/home/<USER>/EnergyStorage/AI_Box/apilib/db", "command": "/usr/bin/c++ -<PERSON><PERSON><PERSON>_STANDALONE -DBOOST_ASIO_STANDALONE -DDataBase_EXPORTS -DFMT_USE_DELETED_FUNCTIONS -DFMT_USE_EXTERN_TEMPLATES -DFMT_USE_OVERRIDE -DFMT_USE_RVALUE_REFERENCES -DFMT_USE_VARIADIC_TEMPLATES -DLENDY_API_EXPORT_COMMON -DLENDY_API_USE_DYNAMIC_LIBS -DPROTOCOL_STREAM -I/usr/local/openssl-1.0.2/include -I/home/<USER>/EnergyStorage/AI_Box/apilib -I/home/<USER>/EnergyStorage/AI_Box/apilib/define -I/home/<USER>/EnergyStorage/AI_Box/apilib/define/netdef -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/detail -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/detail/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/experimental -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/experimental/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/generic -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/generic/detail -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/generic/detail/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ip -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ip/detail -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ip/detail/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ip/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/local -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/local/detail -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/local/detail/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/posix -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ssl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ssl/detail -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ssl/detail/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ssl/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ts -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/windows -I/home/<USER>/EnergyStorage/AI_Box/apilib/net -I/home/<USER>/EnergyStorage/AI_Box/apilib/net/CMakeFiles -I/home/<USER>/EnergyStorage/AI_Box/apilib/net/CMakeFiles/Net.dir -I/home/<USER>/EnergyStorage/AI_Box/apilib/db -I/home/<USER>/EnergyStorage/AI_Box/apilib/db/CMakeFiles -I/home/<USER>/EnergyStorage/AI_Box/apilib/db/CMakeFiles/DataBase.dir -I/home/<USER>/EnergyStorage/AI_Box/apilib/db/CMakeFiles/DataBase.dir/Implementation -I/home/<USER>/EnergyStorage/AI_Box/apilib/db/Implementation -I/home/<USER>/EnergyStorage/AI_Box/apilib/log -I/home/<USER>/EnergyStorage/AI_Box/apilib/log/CMakeFiles -I/home/<USER>/EnergyStorage/AI_Box/apilib/log/CMakeFiles/Log.dir -I/home/<USER>/EnergyStorage/AI_Box/apilib/util -I/home/<USER>/EnergyStorage/AI_Box/apilib/util/CMakeFiles -I/home/<USER>/EnergyStorage/AI_Box/apilib/util/CMakeFiles/Util.dir -I/home/<USER>/EnergyStorage/AI_Box/vision_data.h -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/fmt -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/utf8cpp -isystem /usr/include/mysql -std=c++14 -O3 -fPIC -O2 -g -DNDEBUG -std=gnu++14 -fPIC -o CMakeFiles/DataBase.dir/MySQLConnection.cpp.o -c /home/<USER>/EnergyStorage/AI_Box/apilib/db/MySQLConnection.cpp", "file": "/home/<USER>/EnergyStorage/AI_Box/apilib/db/MySQLConnection.cpp", "output": "apilib/db/CMakeFiles/DataBase.dir/MySQLConnection.cpp.o"}, {"directory": "/home/<USER>/EnergyStorage/AI_Box/apilib/db", "command": "/usr/bin/c++ -<PERSON><PERSON><PERSON>_STANDALONE -DBOOST_ASIO_STANDALONE -DDataBase_EXPORTS -DFMT_USE_DELETED_FUNCTIONS -DFMT_USE_EXTERN_TEMPLATES -DFMT_USE_OVERRIDE -DFMT_USE_RVALUE_REFERENCES -DFMT_USE_VARIADIC_TEMPLATES -DLENDY_API_EXPORT_COMMON -DLENDY_API_USE_DYNAMIC_LIBS -DPROTOCOL_STREAM -I/usr/local/openssl-1.0.2/include -I/home/<USER>/EnergyStorage/AI_Box/apilib -I/home/<USER>/EnergyStorage/AI_Box/apilib/define -I/home/<USER>/EnergyStorage/AI_Box/apilib/define/netdef -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/detail -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/detail/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/experimental -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/experimental/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/generic -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/generic/detail -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/generic/detail/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ip -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ip/detail -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ip/detail/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ip/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/local -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/local/detail -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/local/detail/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/posix -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ssl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ssl/detail -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ssl/detail/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ssl/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ts -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/windows -I/home/<USER>/EnergyStorage/AI_Box/apilib/net -I/home/<USER>/EnergyStorage/AI_Box/apilib/net/CMakeFiles -I/home/<USER>/EnergyStorage/AI_Box/apilib/net/CMakeFiles/Net.dir -I/home/<USER>/EnergyStorage/AI_Box/apilib/db -I/home/<USER>/EnergyStorage/AI_Box/apilib/db/CMakeFiles -I/home/<USER>/EnergyStorage/AI_Box/apilib/db/CMakeFiles/DataBase.dir -I/home/<USER>/EnergyStorage/AI_Box/apilib/db/CMakeFiles/DataBase.dir/Implementation -I/home/<USER>/EnergyStorage/AI_Box/apilib/db/Implementation -I/home/<USER>/EnergyStorage/AI_Box/apilib/log -I/home/<USER>/EnergyStorage/AI_Box/apilib/log/CMakeFiles -I/home/<USER>/EnergyStorage/AI_Box/apilib/log/CMakeFiles/Log.dir -I/home/<USER>/EnergyStorage/AI_Box/apilib/util -I/home/<USER>/EnergyStorage/AI_Box/apilib/util/CMakeFiles -I/home/<USER>/EnergyStorage/AI_Box/apilib/util/CMakeFiles/Util.dir -I/home/<USER>/EnergyStorage/AI_Box/vision_data.h -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/fmt -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/utf8cpp -isystem /usr/include/mysql -std=c++14 -O3 -fPIC -O2 -g -DNDEBUG -std=gnu++14 -fPIC -o CMakeFiles/DataBase.dir/PreparedStatement.cpp.o -c /home/<USER>/EnergyStorage/AI_Box/apilib/db/PreparedStatement.cpp", "file": "/home/<USER>/EnergyStorage/AI_Box/apilib/db/PreparedStatement.cpp", "output": "apilib/db/CMakeFiles/DataBase.dir/PreparedStatement.cpp.o"}, {"directory": "/home/<USER>/EnergyStorage/AI_Box/apilib/db", "command": "/usr/bin/c++ -<PERSON><PERSON><PERSON>_STANDALONE -DBOOST_ASIO_STANDALONE -DDataBase_EXPORTS -DFMT_USE_DELETED_FUNCTIONS -DFMT_USE_EXTERN_TEMPLATES -DFMT_USE_OVERRIDE -DFMT_USE_RVALUE_REFERENCES -DFMT_USE_VARIADIC_TEMPLATES -DLENDY_API_EXPORT_COMMON -DLENDY_API_USE_DYNAMIC_LIBS -DPROTOCOL_STREAM -I/usr/local/openssl-1.0.2/include -I/home/<USER>/EnergyStorage/AI_Box/apilib -I/home/<USER>/EnergyStorage/AI_Box/apilib/define -I/home/<USER>/EnergyStorage/AI_Box/apilib/define/netdef -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/detail -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/detail/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/experimental -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/experimental/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/generic -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/generic/detail -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/generic/detail/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ip -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ip/detail -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ip/detail/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ip/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/local -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/local/detail -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/local/detail/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/posix -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ssl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ssl/detail -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ssl/detail/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ssl/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ts -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/windows -I/home/<USER>/EnergyStorage/AI_Box/apilib/net -I/home/<USER>/EnergyStorage/AI_Box/apilib/net/CMakeFiles -I/home/<USER>/EnergyStorage/AI_Box/apilib/net/CMakeFiles/Net.dir -I/home/<USER>/EnergyStorage/AI_Box/apilib/db -I/home/<USER>/EnergyStorage/AI_Box/apilib/db/CMakeFiles -I/home/<USER>/EnergyStorage/AI_Box/apilib/db/CMakeFiles/DataBase.dir -I/home/<USER>/EnergyStorage/AI_Box/apilib/db/CMakeFiles/DataBase.dir/Implementation -I/home/<USER>/EnergyStorage/AI_Box/apilib/db/Implementation -I/home/<USER>/EnergyStorage/AI_Box/apilib/log -I/home/<USER>/EnergyStorage/AI_Box/apilib/log/CMakeFiles -I/home/<USER>/EnergyStorage/AI_Box/apilib/log/CMakeFiles/Log.dir -I/home/<USER>/EnergyStorage/AI_Box/apilib/util -I/home/<USER>/EnergyStorage/AI_Box/apilib/util/CMakeFiles -I/home/<USER>/EnergyStorage/AI_Box/apilib/util/CMakeFiles/Util.dir -I/home/<USER>/EnergyStorage/AI_Box/vision_data.h -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/fmt -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/utf8cpp -isystem /usr/include/mysql -std=c++14 -O3 -fPIC -O2 -g -DNDEBUG -std=gnu++14 -fPIC -o CMakeFiles/DataBase.dir/QueryCallback.cpp.o -c /home/<USER>/EnergyStorage/AI_Box/apilib/db/QueryCallback.cpp", "file": "/home/<USER>/EnergyStorage/AI_Box/apilib/db/QueryCallback.cpp", "output": "apilib/db/CMakeFiles/DataBase.dir/QueryCallback.cpp.o"}, {"directory": "/home/<USER>/EnergyStorage/AI_Box/apilib/db", "command": "/usr/bin/c++ -<PERSON><PERSON><PERSON>_STANDALONE -DBOOST_ASIO_STANDALONE -DDataBase_EXPORTS -DFMT_USE_DELETED_FUNCTIONS -DFMT_USE_EXTERN_TEMPLATES -DFMT_USE_OVERRIDE -DFMT_USE_RVALUE_REFERENCES -DFMT_USE_VARIADIC_TEMPLATES -DLENDY_API_EXPORT_COMMON -DLENDY_API_USE_DYNAMIC_LIBS -DPROTOCOL_STREAM -I/usr/local/openssl-1.0.2/include -I/home/<USER>/EnergyStorage/AI_Box/apilib -I/home/<USER>/EnergyStorage/AI_Box/apilib/define -I/home/<USER>/EnergyStorage/AI_Box/apilib/define/netdef -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/detail -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/detail/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/experimental -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/experimental/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/generic -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/generic/detail -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/generic/detail/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ip -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ip/detail -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ip/detail/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ip/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/local -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/local/detail -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/local/detail/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/posix -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ssl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ssl/detail -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ssl/detail/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ssl/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ts -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/windows -I/home/<USER>/EnergyStorage/AI_Box/apilib/net -I/home/<USER>/EnergyStorage/AI_Box/apilib/net/CMakeFiles -I/home/<USER>/EnergyStorage/AI_Box/apilib/net/CMakeFiles/Net.dir -I/home/<USER>/EnergyStorage/AI_Box/apilib/db -I/home/<USER>/EnergyStorage/AI_Box/apilib/db/CMakeFiles -I/home/<USER>/EnergyStorage/AI_Box/apilib/db/CMakeFiles/DataBase.dir -I/home/<USER>/EnergyStorage/AI_Box/apilib/db/CMakeFiles/DataBase.dir/Implementation -I/home/<USER>/EnergyStorage/AI_Box/apilib/db/Implementation -I/home/<USER>/EnergyStorage/AI_Box/apilib/log -I/home/<USER>/EnergyStorage/AI_Box/apilib/log/CMakeFiles -I/home/<USER>/EnergyStorage/AI_Box/apilib/log/CMakeFiles/Log.dir -I/home/<USER>/EnergyStorage/AI_Box/apilib/util -I/home/<USER>/EnergyStorage/AI_Box/apilib/util/CMakeFiles -I/home/<USER>/EnergyStorage/AI_Box/apilib/util/CMakeFiles/Util.dir -I/home/<USER>/EnergyStorage/AI_Box/vision_data.h -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/fmt -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/utf8cpp -isystem /usr/include/mysql -std=c++14 -O3 -fPIC -O2 -g -DNDEBUG -std=gnu++14 -fPIC -o CMakeFiles/DataBase.dir/QueryResult.cpp.o -c /home/<USER>/EnergyStorage/AI_Box/apilib/db/QueryResult.cpp", "file": "/home/<USER>/EnergyStorage/AI_Box/apilib/db/QueryResult.cpp", "output": "apilib/db/CMakeFiles/DataBase.dir/QueryResult.cpp.o"}, {"directory": "/home/<USER>/EnergyStorage/AI_Box/apilib/db", "command": "/usr/bin/c++ -<PERSON><PERSON><PERSON>_STANDALONE -DBOOST_ASIO_STANDALONE -DDataBase_EXPORTS -DFMT_USE_DELETED_FUNCTIONS -DFMT_USE_EXTERN_TEMPLATES -DFMT_USE_OVERRIDE -DFMT_USE_RVALUE_REFERENCES -DFMT_USE_VARIADIC_TEMPLATES -DLENDY_API_EXPORT_COMMON -DLENDY_API_USE_DYNAMIC_LIBS -DPROTOCOL_STREAM -I/usr/local/openssl-1.0.2/include -I/home/<USER>/EnergyStorage/AI_Box/apilib -I/home/<USER>/EnergyStorage/AI_Box/apilib/define -I/home/<USER>/EnergyStorage/AI_Box/apilib/define/netdef -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/detail -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/detail/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/experimental -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/experimental/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/generic -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/generic/detail -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/generic/detail/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ip -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ip/detail -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ip/detail/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ip/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/local -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/local/detail -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/local/detail/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/posix -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ssl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ssl/detail -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ssl/detail/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ssl/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ts -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/windows -I/home/<USER>/EnergyStorage/AI_Box/apilib/net -I/home/<USER>/EnergyStorage/AI_Box/apilib/net/CMakeFiles -I/home/<USER>/EnergyStorage/AI_Box/apilib/net/CMakeFiles/Net.dir -I/home/<USER>/EnergyStorage/AI_Box/apilib/db -I/home/<USER>/EnergyStorage/AI_Box/apilib/db/CMakeFiles -I/home/<USER>/EnergyStorage/AI_Box/apilib/db/CMakeFiles/DataBase.dir -I/home/<USER>/EnergyStorage/AI_Box/apilib/db/CMakeFiles/DataBase.dir/Implementation -I/home/<USER>/EnergyStorage/AI_Box/apilib/db/Implementation -I/home/<USER>/EnergyStorage/AI_Box/apilib/log -I/home/<USER>/EnergyStorage/AI_Box/apilib/log/CMakeFiles -I/home/<USER>/EnergyStorage/AI_Box/apilib/log/CMakeFiles/Log.dir -I/home/<USER>/EnergyStorage/AI_Box/apilib/util -I/home/<USER>/EnergyStorage/AI_Box/apilib/util/CMakeFiles -I/home/<USER>/EnergyStorage/AI_Box/apilib/util/CMakeFiles/Util.dir -I/home/<USER>/EnergyStorage/AI_Box/vision_data.h -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/fmt -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/utf8cpp -isystem /usr/include/mysql -std=c++14 -O3 -fPIC -O2 -g -DNDEBUG -std=gnu++14 -fPIC -o CMakeFiles/DataBase.dir/Implementation/LogonDatabase.cpp.o -c /home/<USER>/EnergyStorage/AI_Box/apilib/db/Implementation/LogonDatabase.cpp", "file": "/home/<USER>/EnergyStorage/AI_Box/apilib/db/Implementation/LogonDatabase.cpp", "output": "apilib/db/CMakeFiles/DataBase.dir/Implementation/LogonDatabase.cpp.o"}, {"directory": "/home/<USER>/EnergyStorage/AI_Box/apilib/db", "command": "/usr/bin/c++ -<PERSON><PERSON><PERSON>_STANDALONE -DBOOST_ASIO_STANDALONE -DDataBase_EXPORTS -DFMT_USE_DELETED_FUNCTIONS -DFMT_USE_EXTERN_TEMPLATES -DFMT_USE_OVERRIDE -DFMT_USE_RVALUE_REFERENCES -DFMT_USE_VARIADIC_TEMPLATES -DLENDY_API_EXPORT_COMMON -DLENDY_API_USE_DYNAMIC_LIBS -DPROTOCOL_STREAM -I/usr/local/openssl-1.0.2/include -I/home/<USER>/EnergyStorage/AI_Box/apilib -I/home/<USER>/EnergyStorage/AI_Box/apilib/define -I/home/<USER>/EnergyStorage/AI_Box/apilib/define/netdef -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/detail -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/detail/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/experimental -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/experimental/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/generic -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/generic/detail -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/generic/detail/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ip -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ip/detail -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ip/detail/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ip/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/local -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/local/detail -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/local/detail/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/posix -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ssl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ssl/detail -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ssl/detail/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ssl/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ts -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/windows -I/home/<USER>/EnergyStorage/AI_Box/apilib/net -I/home/<USER>/EnergyStorage/AI_Box/apilib/net/CMakeFiles -I/home/<USER>/EnergyStorage/AI_Box/apilib/net/CMakeFiles/Net.dir -I/home/<USER>/EnergyStorage/AI_Box/apilib/db -I/home/<USER>/EnergyStorage/AI_Box/apilib/db/CMakeFiles -I/home/<USER>/EnergyStorage/AI_Box/apilib/db/CMakeFiles/DataBase.dir -I/home/<USER>/EnergyStorage/AI_Box/apilib/db/CMakeFiles/DataBase.dir/Implementation -I/home/<USER>/EnergyStorage/AI_Box/apilib/db/Implementation -I/home/<USER>/EnergyStorage/AI_Box/apilib/log -I/home/<USER>/EnergyStorage/AI_Box/apilib/log/CMakeFiles -I/home/<USER>/EnergyStorage/AI_Box/apilib/log/CMakeFiles/Log.dir -I/home/<USER>/EnergyStorage/AI_Box/apilib/util -I/home/<USER>/EnergyStorage/AI_Box/apilib/util/CMakeFiles -I/home/<USER>/EnergyStorage/AI_Box/apilib/util/CMakeFiles/Util.dir -I/home/<USER>/EnergyStorage/AI_Box/vision_data.h -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/fmt -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/utf8cpp -isystem /usr/include/mysql -std=c++14 -O3 -fPIC -O2 -g -DNDEBUG -std=gnu++14 -fPIC -o CMakeFiles/DataBase.dir/Implementation/TreasureDatabase.cpp.o -c /home/<USER>/EnergyStorage/AI_Box/apilib/db/Implementation/TreasureDatabase.cpp", "file": "/home/<USER>/EnergyStorage/AI_Box/apilib/db/Implementation/TreasureDatabase.cpp", "output": "apilib/db/CMakeFiles/DataBase.dir/Implementation/TreasureDatabase.cpp.o"}, {"directory": "/home/<USER>/EnergyStorage/AI_Box/src/aibox", "command": "/usr/bin/c++ -<PERSON><PERSON><PERSON>_STANDALONE -DBOOST_ATOMIC_DYN_LINK -DBOOST_ATOMIC_NO_LIB -DBOOST_FILESYSTEM_DYN_LINK -DBOOST_FILESYSTEM_NO_LIB -DBOOST_SYSTEM_DYN_LINK -DBOOST_SYSTEM_NO_LIB -DFMT_USE_DELETED_FUNCTIONS -DFMT_USE_EXTERN_TEMPLATES -DFMT_USE_OVERRIDE -DFMT_USE_RVALUE_REFERENCES -DFMT_USE_VARIADIC_TEMPLATES -DLENDY_API_EXPORT_GAME -DLENDY_API_USE_DYNAMIC_LIBS -DPROTOCOL_STREAM -I/usr/local/openssl-1.0.2/include -I/home/<USER>/EnergyStorage/AI_Box/src/msgdef -I/home/<USER>/EnergyStorage/AI_Box/src/aibox -I/home/<USER>/EnergyStorage/AI_Box/src/aibox/CMakeFiles -I/home/<USER>/EnergyStorage/AI_Box/src/aibox/CMakeFiles/AI_Box.dir -I/home/<USER>/EnergyStorage/AI_Box/src/aibox/CMakeFiles/AI_Box.dir/data -I/home/<USER>/EnergyStorage/AI_Box/src/aibox/CMakeFiles/AI_Box.dir/http -I/home/<USER>/EnergyStorage/AI_Box/src/aibox/CMakeFiles/AI_Box.dir/http/handler -I/home/<USER>/EnergyStorage/AI_Box/src/aibox/CMakeFiles/valgrind_test.dir -I/home/<USER>/EnergyStorage/AI_Box/src/aibox/data -I/home/<USER>/EnergyStorage/AI_Box/src/aibox/data/NotifyItem -I/home/<USER>/EnergyStorage/AI_Box/src/aibox/http -I/home/<USER>/EnergyStorage/AI_Box/src/aibox/http/handler -I/home/<USER>/EnergyStorage/AI_Box/apilib -I/home/<USER>/EnergyStorage/AI_Box/apilib/define -I/home/<USER>/EnergyStorage/AI_Box/apilib/define/netdef -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/detail -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/detail/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/experimental -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/experimental/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/generic -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/generic/detail -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/generic/detail/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ip -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ip/detail -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ip/detail/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ip/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/local -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/local/detail -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/local/detail/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/posix -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ssl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ssl/detail -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ssl/detail/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ssl/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ts -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/windows -I/home/<USER>/EnergyStorage/AI_Box/apilib/net -I/home/<USER>/EnergyStorage/AI_Box/apilib/net/CMakeFiles -I/home/<USER>/EnergyStorage/AI_Box/apilib/net/CMakeFiles/Net.dir -I/home/<USER>/EnergyStorage/AI_Box/apilib/db -I/home/<USER>/EnergyStorage/AI_Box/apilib/db/CMakeFiles -I/home/<USER>/EnergyStorage/AI_Box/apilib/db/CMakeFiles/DataBase.dir -I/home/<USER>/EnergyStorage/AI_Box/apilib/db/CMakeFiles/DataBase.dir/Implementation -I/home/<USER>/EnergyStorage/AI_Box/apilib/db/Implementation -I/home/<USER>/EnergyStorage/AI_Box/apilib/log -I/home/<USER>/EnergyStorage/AI_Box/apilib/log/CMakeFiles -I/home/<USER>/EnergyStorage/AI_Box/apilib/log/CMakeFiles/Log.dir -I/home/<USER>/EnergyStorage/AI_Box/apilib/util -I/home/<USER>/EnergyStorage/AI_Box/apilib/util/CMakeFiles -I/home/<USER>/EnergyStorage/AI_Box/apilib/util/CMakeFiles/Util.dir -I/home/<USER>/EnergyStorage/AI_Box/vision_data.h -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/fmt -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/utf8cpp -isystem /usr/include/mysql -std=c++14 -O3 -fPIC -g -std=gnu++14 -o CMakeFiles/AI_Box.dir/AIBoxListManager.cpp.o -c /home/<USER>/EnergyStorage/AI_Box/src/aibox/AIBoxListManager.cpp", "file": "/home/<USER>/EnergyStorage/AI_Box/src/aibox/AIBoxListManager.cpp", "output": "src/aibox/CMakeFiles/AI_Box.dir/AIBoxListManager.cpp.o"}, {"directory": "/home/<USER>/EnergyStorage/AI_Box/src/aibox", "command": "/usr/bin/c++ -<PERSON><PERSON><PERSON>_STANDALONE -DBOOST_ATOMIC_DYN_LINK -DBOOST_ATOMIC_NO_LIB -DBOOST_FILESYSTEM_DYN_LINK -DBOOST_FILESYSTEM_NO_LIB -DBOOST_SYSTEM_DYN_LINK -DBOOST_SYSTEM_NO_LIB -DFMT_USE_DELETED_FUNCTIONS -DFMT_USE_EXTERN_TEMPLATES -DFMT_USE_OVERRIDE -DFMT_USE_RVALUE_REFERENCES -DFMT_USE_VARIADIC_TEMPLATES -DLENDY_API_EXPORT_GAME -DLENDY_API_USE_DYNAMIC_LIBS -DPROTOCOL_STREAM -I/usr/local/openssl-1.0.2/include -I/home/<USER>/EnergyStorage/AI_Box/src/msgdef -I/home/<USER>/EnergyStorage/AI_Box/src/aibox -I/home/<USER>/EnergyStorage/AI_Box/src/aibox/CMakeFiles -I/home/<USER>/EnergyStorage/AI_Box/src/aibox/CMakeFiles/AI_Box.dir -I/home/<USER>/EnergyStorage/AI_Box/src/aibox/CMakeFiles/AI_Box.dir/data -I/home/<USER>/EnergyStorage/AI_Box/src/aibox/CMakeFiles/AI_Box.dir/http -I/home/<USER>/EnergyStorage/AI_Box/src/aibox/CMakeFiles/AI_Box.dir/http/handler -I/home/<USER>/EnergyStorage/AI_Box/src/aibox/CMakeFiles/valgrind_test.dir -I/home/<USER>/EnergyStorage/AI_Box/src/aibox/data -I/home/<USER>/EnergyStorage/AI_Box/src/aibox/data/NotifyItem -I/home/<USER>/EnergyStorage/AI_Box/src/aibox/http -I/home/<USER>/EnergyStorage/AI_Box/src/aibox/http/handler -I/home/<USER>/EnergyStorage/AI_Box/apilib -I/home/<USER>/EnergyStorage/AI_Box/apilib/define -I/home/<USER>/EnergyStorage/AI_Box/apilib/define/netdef -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/detail -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/detail/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/experimental -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/experimental/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/generic -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/generic/detail -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/generic/detail/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ip -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ip/detail -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ip/detail/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ip/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/local -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/local/detail -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/local/detail/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/posix -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ssl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ssl/detail -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ssl/detail/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ssl/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ts -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/windows -I/home/<USER>/EnergyStorage/AI_Box/apilib/net -I/home/<USER>/EnergyStorage/AI_Box/apilib/net/CMakeFiles -I/home/<USER>/EnergyStorage/AI_Box/apilib/net/CMakeFiles/Net.dir -I/home/<USER>/EnergyStorage/AI_Box/apilib/db -I/home/<USER>/EnergyStorage/AI_Box/apilib/db/CMakeFiles -I/home/<USER>/EnergyStorage/AI_Box/apilib/db/CMakeFiles/DataBase.dir -I/home/<USER>/EnergyStorage/AI_Box/apilib/db/CMakeFiles/DataBase.dir/Implementation -I/home/<USER>/EnergyStorage/AI_Box/apilib/db/Implementation -I/home/<USER>/EnergyStorage/AI_Box/apilib/log -I/home/<USER>/EnergyStorage/AI_Box/apilib/log/CMakeFiles -I/home/<USER>/EnergyStorage/AI_Box/apilib/log/CMakeFiles/Log.dir -I/home/<USER>/EnergyStorage/AI_Box/apilib/util -I/home/<USER>/EnergyStorage/AI_Box/apilib/util/CMakeFiles -I/home/<USER>/EnergyStorage/AI_Box/apilib/util/CMakeFiles/Util.dir -I/home/<USER>/EnergyStorage/AI_Box/vision_data.h -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/fmt -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/utf8cpp -isystem /usr/include/mysql -std=c++14 -O3 -fPIC -g -std=gnu++14 -o CMakeFiles/AI_Box.dir/AttemperEngineSink.cpp.o -c /home/<USER>/EnergyStorage/AI_Box/src/aibox/AttemperEngineSink.cpp", "file": "/home/<USER>/EnergyStorage/AI_Box/src/aibox/AttemperEngineSink.cpp", "output": "src/aibox/CMakeFiles/AI_Box.dir/AttemperEngineSink.cpp.o"}, {"directory": "/home/<USER>/EnergyStorage/AI_Box/src/aibox", "command": "/usr/bin/c++ -<PERSON><PERSON><PERSON>_STANDALONE -DBOOST_ATOMIC_DYN_LINK -DBOOST_ATOMIC_NO_LIB -DBOOST_FILESYSTEM_DYN_LINK -DBOOST_FILESYSTEM_NO_LIB -DBOOST_SYSTEM_DYN_LINK -DBOOST_SYSTEM_NO_LIB -DFMT_USE_DELETED_FUNCTIONS -DFMT_USE_EXTERN_TEMPLATES -DFMT_USE_OVERRIDE -DFMT_USE_RVALUE_REFERENCES -DFMT_USE_VARIADIC_TEMPLATES -DLENDY_API_EXPORT_GAME -DLENDY_API_USE_DYNAMIC_LIBS -DPROTOCOL_STREAM -I/usr/local/openssl-1.0.2/include -I/home/<USER>/EnergyStorage/AI_Box/src/msgdef -I/home/<USER>/EnergyStorage/AI_Box/src/aibox -I/home/<USER>/EnergyStorage/AI_Box/src/aibox/CMakeFiles -I/home/<USER>/EnergyStorage/AI_Box/src/aibox/CMakeFiles/AI_Box.dir -I/home/<USER>/EnergyStorage/AI_Box/src/aibox/CMakeFiles/AI_Box.dir/data -I/home/<USER>/EnergyStorage/AI_Box/src/aibox/CMakeFiles/AI_Box.dir/http -I/home/<USER>/EnergyStorage/AI_Box/src/aibox/CMakeFiles/AI_Box.dir/http/handler -I/home/<USER>/EnergyStorage/AI_Box/src/aibox/CMakeFiles/valgrind_test.dir -I/home/<USER>/EnergyStorage/AI_Box/src/aibox/data -I/home/<USER>/EnergyStorage/AI_Box/src/aibox/data/NotifyItem -I/home/<USER>/EnergyStorage/AI_Box/src/aibox/http -I/home/<USER>/EnergyStorage/AI_Box/src/aibox/http/handler -I/home/<USER>/EnergyStorage/AI_Box/apilib -I/home/<USER>/EnergyStorage/AI_Box/apilib/define -I/home/<USER>/EnergyStorage/AI_Box/apilib/define/netdef -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/detail -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/detail/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/experimental -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/experimental/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/generic -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/generic/detail -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/generic/detail/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ip -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ip/detail -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ip/detail/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ip/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/local -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/local/detail -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/local/detail/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/posix -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ssl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ssl/detail -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ssl/detail/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ssl/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ts -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/windows -I/home/<USER>/EnergyStorage/AI_Box/apilib/net -I/home/<USER>/EnergyStorage/AI_Box/apilib/net/CMakeFiles -I/home/<USER>/EnergyStorage/AI_Box/apilib/net/CMakeFiles/Net.dir -I/home/<USER>/EnergyStorage/AI_Box/apilib/db -I/home/<USER>/EnergyStorage/AI_Box/apilib/db/CMakeFiles -I/home/<USER>/EnergyStorage/AI_Box/apilib/db/CMakeFiles/DataBase.dir -I/home/<USER>/EnergyStorage/AI_Box/apilib/db/CMakeFiles/DataBase.dir/Implementation -I/home/<USER>/EnergyStorage/AI_Box/apilib/db/Implementation -I/home/<USER>/EnergyStorage/AI_Box/apilib/log -I/home/<USER>/EnergyStorage/AI_Box/apilib/log/CMakeFiles -I/home/<USER>/EnergyStorage/AI_Box/apilib/log/CMakeFiles/Log.dir -I/home/<USER>/EnergyStorage/AI_Box/apilib/util -I/home/<USER>/EnergyStorage/AI_Box/apilib/util/CMakeFiles -I/home/<USER>/EnergyStorage/AI_Box/apilib/util/CMakeFiles/Util.dir -I/home/<USER>/EnergyStorage/AI_Box/vision_data.h -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/fmt -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/utf8cpp -isystem /usr/include/mysql -std=c++14 -O3 -fPIC -g -std=gnu++14 -o CMakeFiles/AI_Box.dir/EnergyStorageCabinManager.cpp.o -c /home/<USER>/EnergyStorage/AI_Box/src/aibox/EnergyStorageCabinManager.cpp", "file": "/home/<USER>/EnergyStorage/AI_Box/src/aibox/EnergyStorageCabinManager.cpp", "output": "src/aibox/CMakeFiles/AI_Box.dir/EnergyStorageCabinManager.cpp.o"}, {"directory": "/home/<USER>/EnergyStorage/AI_Box/src/aibox", "command": "/usr/bin/c++ -<PERSON><PERSON><PERSON>_STANDALONE -DBOOST_ATOMIC_DYN_LINK -DBOOST_ATOMIC_NO_LIB -DBOOST_FILESYSTEM_DYN_LINK -DBOOST_FILESYSTEM_NO_LIB -DBOOST_SYSTEM_DYN_LINK -DBOOST_SYSTEM_NO_LIB -DFMT_USE_DELETED_FUNCTIONS -DFMT_USE_EXTERN_TEMPLATES -DFMT_USE_OVERRIDE -DFMT_USE_RVALUE_REFERENCES -DFMT_USE_VARIADIC_TEMPLATES -DLENDY_API_EXPORT_GAME -DLENDY_API_USE_DYNAMIC_LIBS -DPROTOCOL_STREAM -I/usr/local/openssl-1.0.2/include -I/home/<USER>/EnergyStorage/AI_Box/src/msgdef -I/home/<USER>/EnergyStorage/AI_Box/src/aibox -I/home/<USER>/EnergyStorage/AI_Box/src/aibox/CMakeFiles -I/home/<USER>/EnergyStorage/AI_Box/src/aibox/CMakeFiles/AI_Box.dir -I/home/<USER>/EnergyStorage/AI_Box/src/aibox/CMakeFiles/AI_Box.dir/data -I/home/<USER>/EnergyStorage/AI_Box/src/aibox/CMakeFiles/AI_Box.dir/http -I/home/<USER>/EnergyStorage/AI_Box/src/aibox/CMakeFiles/AI_Box.dir/http/handler -I/home/<USER>/EnergyStorage/AI_Box/src/aibox/CMakeFiles/valgrind_test.dir -I/home/<USER>/EnergyStorage/AI_Box/src/aibox/data -I/home/<USER>/EnergyStorage/AI_Box/src/aibox/data/NotifyItem -I/home/<USER>/EnergyStorage/AI_Box/src/aibox/http -I/home/<USER>/EnergyStorage/AI_Box/src/aibox/http/handler -I/home/<USER>/EnergyStorage/AI_Box/apilib -I/home/<USER>/EnergyStorage/AI_Box/apilib/define -I/home/<USER>/EnergyStorage/AI_Box/apilib/define/netdef -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/detail -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/detail/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/experimental -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/experimental/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/generic -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/generic/detail -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/generic/detail/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ip -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ip/detail -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ip/detail/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ip/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/local -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/local/detail -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/local/detail/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/posix -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ssl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ssl/detail -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ssl/detail/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ssl/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ts -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/windows -I/home/<USER>/EnergyStorage/AI_Box/apilib/net -I/home/<USER>/EnergyStorage/AI_Box/apilib/net/CMakeFiles -I/home/<USER>/EnergyStorage/AI_Box/apilib/net/CMakeFiles/Net.dir -I/home/<USER>/EnergyStorage/AI_Box/apilib/db -I/home/<USER>/EnergyStorage/AI_Box/apilib/db/CMakeFiles -I/home/<USER>/EnergyStorage/AI_Box/apilib/db/CMakeFiles/DataBase.dir -I/home/<USER>/EnergyStorage/AI_Box/apilib/db/CMakeFiles/DataBase.dir/Implementation -I/home/<USER>/EnergyStorage/AI_Box/apilib/db/Implementation -I/home/<USER>/EnergyStorage/AI_Box/apilib/log -I/home/<USER>/EnergyStorage/AI_Box/apilib/log/CMakeFiles -I/home/<USER>/EnergyStorage/AI_Box/apilib/log/CMakeFiles/Log.dir -I/home/<USER>/EnergyStorage/AI_Box/apilib/util -I/home/<USER>/EnergyStorage/AI_Box/apilib/util/CMakeFiles -I/home/<USER>/EnergyStorage/AI_Box/apilib/util/CMakeFiles/Util.dir -I/home/<USER>/EnergyStorage/AI_Box/vision_data.h -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/fmt -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/utf8cpp -isystem /usr/include/mysql -std=c++14 -O3 -fPIC -g -std=gnu++14 -o CMakeFiles/AI_Box.dir/RoomListManager.cpp.o -c /home/<USER>/EnergyStorage/AI_Box/src/aibox/RoomListManager.cpp", "file": "/home/<USER>/EnergyStorage/AI_Box/src/aibox/RoomListManager.cpp", "output": "src/aibox/CMakeFiles/AI_Box.dir/RoomListManager.cpp.o"}, {"directory": "/home/<USER>/EnergyStorage/AI_Box/src/aibox", "command": "/usr/bin/c++ -<PERSON><PERSON><PERSON>_STANDALONE -DBOOST_ATOMIC_DYN_LINK -DBOOST_ATOMIC_NO_LIB -DBOOST_FILESYSTEM_DYN_LINK -DBOOST_FILESYSTEM_NO_LIB -DBOOST_SYSTEM_DYN_LINK -DBOOST_SYSTEM_NO_LIB -DFMT_USE_DELETED_FUNCTIONS -DFMT_USE_EXTERN_TEMPLATES -DFMT_USE_OVERRIDE -DFMT_USE_RVALUE_REFERENCES -DFMT_USE_VARIADIC_TEMPLATES -DLENDY_API_EXPORT_GAME -DLENDY_API_USE_DYNAMIC_LIBS -DPROTOCOL_STREAM -I/usr/local/openssl-1.0.2/include -I/home/<USER>/EnergyStorage/AI_Box/src/msgdef -I/home/<USER>/EnergyStorage/AI_Box/src/aibox -I/home/<USER>/EnergyStorage/AI_Box/src/aibox/CMakeFiles -I/home/<USER>/EnergyStorage/AI_Box/src/aibox/CMakeFiles/AI_Box.dir -I/home/<USER>/EnergyStorage/AI_Box/src/aibox/CMakeFiles/AI_Box.dir/data -I/home/<USER>/EnergyStorage/AI_Box/src/aibox/CMakeFiles/AI_Box.dir/http -I/home/<USER>/EnergyStorage/AI_Box/src/aibox/CMakeFiles/AI_Box.dir/http/handler -I/home/<USER>/EnergyStorage/AI_Box/src/aibox/CMakeFiles/valgrind_test.dir -I/home/<USER>/EnergyStorage/AI_Box/src/aibox/data -I/home/<USER>/EnergyStorage/AI_Box/src/aibox/data/NotifyItem -I/home/<USER>/EnergyStorage/AI_Box/src/aibox/http -I/home/<USER>/EnergyStorage/AI_Box/src/aibox/http/handler -I/home/<USER>/EnergyStorage/AI_Box/apilib -I/home/<USER>/EnergyStorage/AI_Box/apilib/define -I/home/<USER>/EnergyStorage/AI_Box/apilib/define/netdef -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/detail -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/detail/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/experimental -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/experimental/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/generic -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/generic/detail -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/generic/detail/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ip -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ip/detail -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ip/detail/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ip/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/local -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/local/detail -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/local/detail/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/posix -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ssl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ssl/detail -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ssl/detail/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ssl/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ts -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/windows -I/home/<USER>/EnergyStorage/AI_Box/apilib/net -I/home/<USER>/EnergyStorage/AI_Box/apilib/net/CMakeFiles -I/home/<USER>/EnergyStorage/AI_Box/apilib/net/CMakeFiles/Net.dir -I/home/<USER>/EnergyStorage/AI_Box/apilib/db -I/home/<USER>/EnergyStorage/AI_Box/apilib/db/CMakeFiles -I/home/<USER>/EnergyStorage/AI_Box/apilib/db/CMakeFiles/DataBase.dir -I/home/<USER>/EnergyStorage/AI_Box/apilib/db/CMakeFiles/DataBase.dir/Implementation -I/home/<USER>/EnergyStorage/AI_Box/apilib/db/Implementation -I/home/<USER>/EnergyStorage/AI_Box/apilib/log -I/home/<USER>/EnergyStorage/AI_Box/apilib/log/CMakeFiles -I/home/<USER>/EnergyStorage/AI_Box/apilib/log/CMakeFiles/Log.dir -I/home/<USER>/EnergyStorage/AI_Box/apilib/util -I/home/<USER>/EnergyStorage/AI_Box/apilib/util/CMakeFiles -I/home/<USER>/EnergyStorage/AI_Box/apilib/util/CMakeFiles/Util.dir -I/home/<USER>/EnergyStorage/AI_Box/vision_data.h -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/fmt -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/utf8cpp -isystem /usr/include/mysql -std=c++14 -O3 -fPIC -g -std=gnu++14 -o CMakeFiles/AI_Box.dir/ServiceUnits.cpp.o -c /home/<USER>/EnergyStorage/AI_Box/src/aibox/ServiceUnits.cpp", "file": "/home/<USER>/EnergyStorage/AI_Box/src/aibox/ServiceUnits.cpp", "output": "src/aibox/CMakeFiles/AI_Box.dir/ServiceUnits.cpp.o"}, {"directory": "/home/<USER>/EnergyStorage/AI_Box/src/aibox", "command": "/usr/bin/c++ -<PERSON><PERSON><PERSON>_STANDALONE -DBOOST_ATOMIC_DYN_LINK -DBOOST_ATOMIC_NO_LIB -DBOOST_FILESYSTEM_DYN_LINK -DBOOST_FILESYSTEM_NO_LIB -DBOOST_SYSTEM_DYN_LINK -DBOOST_SYSTEM_NO_LIB -DFMT_USE_DELETED_FUNCTIONS -DFMT_USE_EXTERN_TEMPLATES -DFMT_USE_OVERRIDE -DFMT_USE_RVALUE_REFERENCES -DFMT_USE_VARIADIC_TEMPLATES -DLENDY_API_EXPORT_GAME -DLENDY_API_USE_DYNAMIC_LIBS -DPROTOCOL_STREAM -I/usr/local/openssl-1.0.2/include -I/home/<USER>/EnergyStorage/AI_Box/src/msgdef -I/home/<USER>/EnergyStorage/AI_Box/src/aibox -I/home/<USER>/EnergyStorage/AI_Box/src/aibox/CMakeFiles -I/home/<USER>/EnergyStorage/AI_Box/src/aibox/CMakeFiles/AI_Box.dir -I/home/<USER>/EnergyStorage/AI_Box/src/aibox/CMakeFiles/AI_Box.dir/data -I/home/<USER>/EnergyStorage/AI_Box/src/aibox/CMakeFiles/AI_Box.dir/http -I/home/<USER>/EnergyStorage/AI_Box/src/aibox/CMakeFiles/AI_Box.dir/http/handler -I/home/<USER>/EnergyStorage/AI_Box/src/aibox/CMakeFiles/valgrind_test.dir -I/home/<USER>/EnergyStorage/AI_Box/src/aibox/data -I/home/<USER>/EnergyStorage/AI_Box/src/aibox/data/NotifyItem -I/home/<USER>/EnergyStorage/AI_Box/src/aibox/http -I/home/<USER>/EnergyStorage/AI_Box/src/aibox/http/handler -I/home/<USER>/EnergyStorage/AI_Box/apilib -I/home/<USER>/EnergyStorage/AI_Box/apilib/define -I/home/<USER>/EnergyStorage/AI_Box/apilib/define/netdef -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/detail -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/detail/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/experimental -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/experimental/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/generic -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/generic/detail -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/generic/detail/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ip -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ip/detail -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ip/detail/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ip/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/local -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/local/detail -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/local/detail/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/posix -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ssl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ssl/detail -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ssl/detail/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ssl/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ts -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/windows -I/home/<USER>/EnergyStorage/AI_Box/apilib/net -I/home/<USER>/EnergyStorage/AI_Box/apilib/net/CMakeFiles -I/home/<USER>/EnergyStorage/AI_Box/apilib/net/CMakeFiles/Net.dir -I/home/<USER>/EnergyStorage/AI_Box/apilib/db -I/home/<USER>/EnergyStorage/AI_Box/apilib/db/CMakeFiles -I/home/<USER>/EnergyStorage/AI_Box/apilib/db/CMakeFiles/DataBase.dir -I/home/<USER>/EnergyStorage/AI_Box/apilib/db/CMakeFiles/DataBase.dir/Implementation -I/home/<USER>/EnergyStorage/AI_Box/apilib/db/Implementation -I/home/<USER>/EnergyStorage/AI_Box/apilib/log -I/home/<USER>/EnergyStorage/AI_Box/apilib/log/CMakeFiles -I/home/<USER>/EnergyStorage/AI_Box/apilib/log/CMakeFiles/Log.dir -I/home/<USER>/EnergyStorage/AI_Box/apilib/util -I/home/<USER>/EnergyStorage/AI_Box/apilib/util/CMakeFiles -I/home/<USER>/EnergyStorage/AI_Box/apilib/util/CMakeFiles/Util.dir -I/home/<USER>/EnergyStorage/AI_Box/vision_data.h -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/fmt -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/utf8cpp -isystem /usr/include/mysql -std=c++14 -O3 -fPIC -g -std=gnu++14 -o CMakeFiles/AI_Box.dir/main.cpp.o -c /home/<USER>/EnergyStorage/AI_Box/src/aibox/main.cpp", "file": "/home/<USER>/EnergyStorage/AI_Box/src/aibox/main.cpp", "output": "src/aibox/CMakeFiles/AI_Box.dir/main.cpp.o"}, {"directory": "/home/<USER>/EnergyStorage/AI_Box/src/aibox", "command": "/usr/bin/c++ -<PERSON><PERSON><PERSON>_STANDALONE -DBOOST_ATOMIC_DYN_LINK -DBOOST_ATOMIC_NO_LIB -DBOOST_FILESYSTEM_DYN_LINK -DBOOST_FILESYSTEM_NO_LIB -DBOOST_SYSTEM_DYN_LINK -DBOOST_SYSTEM_NO_LIB -DFMT_USE_DELETED_FUNCTIONS -DFMT_USE_EXTERN_TEMPLATES -DFMT_USE_OVERRIDE -DFMT_USE_RVALUE_REFERENCES -DFMT_USE_VARIADIC_TEMPLATES -DLENDY_API_EXPORT_GAME -DLENDY_API_USE_DYNAMIC_LIBS -DPROTOCOL_STREAM -I/usr/local/openssl-1.0.2/include -I/home/<USER>/EnergyStorage/AI_Box/src/msgdef -I/home/<USER>/EnergyStorage/AI_Box/src/aibox -I/home/<USER>/EnergyStorage/AI_Box/src/aibox/CMakeFiles -I/home/<USER>/EnergyStorage/AI_Box/src/aibox/CMakeFiles/AI_Box.dir -I/home/<USER>/EnergyStorage/AI_Box/src/aibox/CMakeFiles/AI_Box.dir/data -I/home/<USER>/EnergyStorage/AI_Box/src/aibox/CMakeFiles/AI_Box.dir/http -I/home/<USER>/EnergyStorage/AI_Box/src/aibox/CMakeFiles/AI_Box.dir/http/handler -I/home/<USER>/EnergyStorage/AI_Box/src/aibox/CMakeFiles/valgrind_test.dir -I/home/<USER>/EnergyStorage/AI_Box/src/aibox/data -I/home/<USER>/EnergyStorage/AI_Box/src/aibox/data/NotifyItem -I/home/<USER>/EnergyStorage/AI_Box/src/aibox/http -I/home/<USER>/EnergyStorage/AI_Box/src/aibox/http/handler -I/home/<USER>/EnergyStorage/AI_Box/apilib -I/home/<USER>/EnergyStorage/AI_Box/apilib/define -I/home/<USER>/EnergyStorage/AI_Box/apilib/define/netdef -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/detail -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/detail/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/experimental -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/experimental/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/generic -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/generic/detail -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/generic/detail/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ip -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ip/detail -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ip/detail/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ip/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/local -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/local/detail -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/local/detail/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/posix -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ssl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ssl/detail -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ssl/detail/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ssl/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ts -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/windows -I/home/<USER>/EnergyStorage/AI_Box/apilib/net -I/home/<USER>/EnergyStorage/AI_Box/apilib/net/CMakeFiles -I/home/<USER>/EnergyStorage/AI_Box/apilib/net/CMakeFiles/Net.dir -I/home/<USER>/EnergyStorage/AI_Box/apilib/db -I/home/<USER>/EnergyStorage/AI_Box/apilib/db/CMakeFiles -I/home/<USER>/EnergyStorage/AI_Box/apilib/db/CMakeFiles/DataBase.dir -I/home/<USER>/EnergyStorage/AI_Box/apilib/db/CMakeFiles/DataBase.dir/Implementation -I/home/<USER>/EnergyStorage/AI_Box/apilib/db/Implementation -I/home/<USER>/EnergyStorage/AI_Box/apilib/log -I/home/<USER>/EnergyStorage/AI_Box/apilib/log/CMakeFiles -I/home/<USER>/EnergyStorage/AI_Box/apilib/log/CMakeFiles/Log.dir -I/home/<USER>/EnergyStorage/AI_Box/apilib/util -I/home/<USER>/EnergyStorage/AI_Box/apilib/util/CMakeFiles -I/home/<USER>/EnergyStorage/AI_Box/apilib/util/CMakeFiles/Util.dir -I/home/<USER>/EnergyStorage/AI_Box/vision_data.h -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/fmt -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/utf8cpp -isystem /usr/include/mysql -std=c++14 -O3 -fPIC -g -std=gnu++14 -o CMakeFiles/AI_Box.dir/data/CDirManager.cpp.o -c /home/<USER>/EnergyStorage/AI_Box/src/aibox/data/CDirManager.cpp", "file": "/home/<USER>/EnergyStorage/AI_Box/src/aibox/data/CDirManager.cpp", "output": "src/aibox/CMakeFiles/AI_Box.dir/data/CDirManager.cpp.o"}, {"directory": "/home/<USER>/EnergyStorage/AI_Box/src/aibox", "command": "/usr/bin/c++ -<PERSON><PERSON><PERSON>_STANDALONE -DBOOST_ATOMIC_DYN_LINK -DBOOST_ATOMIC_NO_LIB -DBOOST_FILESYSTEM_DYN_LINK -DBOOST_FILESYSTEM_NO_LIB -DBOOST_SYSTEM_DYN_LINK -DBOOST_SYSTEM_NO_LIB -DFMT_USE_DELETED_FUNCTIONS -DFMT_USE_EXTERN_TEMPLATES -DFMT_USE_OVERRIDE -DFMT_USE_RVALUE_REFERENCES -DFMT_USE_VARIADIC_TEMPLATES -DLENDY_API_EXPORT_GAME -DLENDY_API_USE_DYNAMIC_LIBS -DPROTOCOL_STREAM -I/usr/local/openssl-1.0.2/include -I/home/<USER>/EnergyStorage/AI_Box/src/msgdef -I/home/<USER>/EnergyStorage/AI_Box/src/aibox -I/home/<USER>/EnergyStorage/AI_Box/src/aibox/CMakeFiles -I/home/<USER>/EnergyStorage/AI_Box/src/aibox/CMakeFiles/AI_Box.dir -I/home/<USER>/EnergyStorage/AI_Box/src/aibox/CMakeFiles/AI_Box.dir/data -I/home/<USER>/EnergyStorage/AI_Box/src/aibox/CMakeFiles/AI_Box.dir/http -I/home/<USER>/EnergyStorage/AI_Box/src/aibox/CMakeFiles/AI_Box.dir/http/handler -I/home/<USER>/EnergyStorage/AI_Box/src/aibox/CMakeFiles/valgrind_test.dir -I/home/<USER>/EnergyStorage/AI_Box/src/aibox/data -I/home/<USER>/EnergyStorage/AI_Box/src/aibox/data/NotifyItem -I/home/<USER>/EnergyStorage/AI_Box/src/aibox/http -I/home/<USER>/EnergyStorage/AI_Box/src/aibox/http/handler -I/home/<USER>/EnergyStorage/AI_Box/apilib -I/home/<USER>/EnergyStorage/AI_Box/apilib/define -I/home/<USER>/EnergyStorage/AI_Box/apilib/define/netdef -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/detail -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/detail/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/experimental -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/experimental/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/generic -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/generic/detail -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/generic/detail/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ip -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ip/detail -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ip/detail/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ip/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/local -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/local/detail -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/local/detail/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/posix -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ssl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ssl/detail -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ssl/detail/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ssl/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ts -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/windows -I/home/<USER>/EnergyStorage/AI_Box/apilib/net -I/home/<USER>/EnergyStorage/AI_Box/apilib/net/CMakeFiles -I/home/<USER>/EnergyStorage/AI_Box/apilib/net/CMakeFiles/Net.dir -I/home/<USER>/EnergyStorage/AI_Box/apilib/db -I/home/<USER>/EnergyStorage/AI_Box/apilib/db/CMakeFiles -I/home/<USER>/EnergyStorage/AI_Box/apilib/db/CMakeFiles/DataBase.dir -I/home/<USER>/EnergyStorage/AI_Box/apilib/db/CMakeFiles/DataBase.dir/Implementation -I/home/<USER>/EnergyStorage/AI_Box/apilib/db/Implementation -I/home/<USER>/EnergyStorage/AI_Box/apilib/log -I/home/<USER>/EnergyStorage/AI_Box/apilib/log/CMakeFiles -I/home/<USER>/EnergyStorage/AI_Box/apilib/log/CMakeFiles/Log.dir -I/home/<USER>/EnergyStorage/AI_Box/apilib/util -I/home/<USER>/EnergyStorage/AI_Box/apilib/util/CMakeFiles -I/home/<USER>/EnergyStorage/AI_Box/apilib/util/CMakeFiles/Util.dir -I/home/<USER>/EnergyStorage/AI_Box/vision_data.h -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/fmt -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/utf8cpp -isystem /usr/include/mysql -std=c++14 -O3 -fPIC -g -std=gnu++14 -o CMakeFiles/AI_Box.dir/data/CGlobal.cpp.o -c /home/<USER>/EnergyStorage/AI_Box/src/aibox/data/CGlobal.cpp", "file": "/home/<USER>/EnergyStorage/AI_Box/src/aibox/data/CGlobal.cpp", "output": "src/aibox/CMakeFiles/AI_Box.dir/data/CGlobal.cpp.o"}, {"directory": "/home/<USER>/EnergyStorage/AI_Box/src/aibox", "command": "/usr/bin/c++ -<PERSON><PERSON><PERSON>_STANDALONE -DBOOST_ATOMIC_DYN_LINK -DBOOST_ATOMIC_NO_LIB -DBOOST_FILESYSTEM_DYN_LINK -DBOOST_FILESYSTEM_NO_LIB -DBOOST_SYSTEM_DYN_LINK -DBOOST_SYSTEM_NO_LIB -DFMT_USE_DELETED_FUNCTIONS -DFMT_USE_EXTERN_TEMPLATES -DFMT_USE_OVERRIDE -DFMT_USE_RVALUE_REFERENCES -DFMT_USE_VARIADIC_TEMPLATES -DLENDY_API_EXPORT_GAME -DLENDY_API_USE_DYNAMIC_LIBS -DPROTOCOL_STREAM -I/usr/local/openssl-1.0.2/include -I/home/<USER>/EnergyStorage/AI_Box/src/msgdef -I/home/<USER>/EnergyStorage/AI_Box/src/aibox -I/home/<USER>/EnergyStorage/AI_Box/src/aibox/CMakeFiles -I/home/<USER>/EnergyStorage/AI_Box/src/aibox/CMakeFiles/AI_Box.dir -I/home/<USER>/EnergyStorage/AI_Box/src/aibox/CMakeFiles/AI_Box.dir/data -I/home/<USER>/EnergyStorage/AI_Box/src/aibox/CMakeFiles/AI_Box.dir/http -I/home/<USER>/EnergyStorage/AI_Box/src/aibox/CMakeFiles/AI_Box.dir/http/handler -I/home/<USER>/EnergyStorage/AI_Box/src/aibox/CMakeFiles/valgrind_test.dir -I/home/<USER>/EnergyStorage/AI_Box/src/aibox/data -I/home/<USER>/EnergyStorage/AI_Box/src/aibox/data/NotifyItem -I/home/<USER>/EnergyStorage/AI_Box/src/aibox/http -I/home/<USER>/EnergyStorage/AI_Box/src/aibox/http/handler -I/home/<USER>/EnergyStorage/AI_Box/apilib -I/home/<USER>/EnergyStorage/AI_Box/apilib/define -I/home/<USER>/EnergyStorage/AI_Box/apilib/define/netdef -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/detail -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/detail/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/experimental -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/experimental/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/generic -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/generic/detail -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/generic/detail/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ip -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ip/detail -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ip/detail/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ip/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/local -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/local/detail -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/local/detail/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/posix -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ssl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ssl/detail -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ssl/detail/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ssl/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ts -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/windows -I/home/<USER>/EnergyStorage/AI_Box/apilib/net -I/home/<USER>/EnergyStorage/AI_Box/apilib/net/CMakeFiles -I/home/<USER>/EnergyStorage/AI_Box/apilib/net/CMakeFiles/Net.dir -I/home/<USER>/EnergyStorage/AI_Box/apilib/db -I/home/<USER>/EnergyStorage/AI_Box/apilib/db/CMakeFiles -I/home/<USER>/EnergyStorage/AI_Box/apilib/db/CMakeFiles/DataBase.dir -I/home/<USER>/EnergyStorage/AI_Box/apilib/db/CMakeFiles/DataBase.dir/Implementation -I/home/<USER>/EnergyStorage/AI_Box/apilib/db/Implementation -I/home/<USER>/EnergyStorage/AI_Box/apilib/log -I/home/<USER>/EnergyStorage/AI_Box/apilib/log/CMakeFiles -I/home/<USER>/EnergyStorage/AI_Box/apilib/log/CMakeFiles/Log.dir -I/home/<USER>/EnergyStorage/AI_Box/apilib/util -I/home/<USER>/EnergyStorage/AI_Box/apilib/util/CMakeFiles -I/home/<USER>/EnergyStorage/AI_Box/apilib/util/CMakeFiles/Util.dir -I/home/<USER>/EnergyStorage/AI_Box/vision_data.h -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/fmt -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/utf8cpp -isystem /usr/include/mysql -std=c++14 -O3 -fPIC -g -std=gnu++14 -o CMakeFiles/AI_Box.dir/data/CServerGlobal.cpp.o -c /home/<USER>/EnergyStorage/AI_Box/src/aibox/data/CServerGlobal.cpp", "file": "/home/<USER>/EnergyStorage/AI_Box/src/aibox/data/CServerGlobal.cpp", "output": "src/aibox/CMakeFiles/AI_Box.dir/data/CServerGlobal.cpp.o"}, {"directory": "/home/<USER>/EnergyStorage/AI_Box/src/aibox", "command": "/usr/bin/c++ -<PERSON><PERSON><PERSON>_STANDALONE -DBOOST_ATOMIC_DYN_LINK -DBOOST_ATOMIC_NO_LIB -DBOOST_FILESYSTEM_DYN_LINK -DBOOST_FILESYSTEM_NO_LIB -DBOOST_SYSTEM_DYN_LINK -DBOOST_SYSTEM_NO_LIB -DFMT_USE_DELETED_FUNCTIONS -DFMT_USE_EXTERN_TEMPLATES -DFMT_USE_OVERRIDE -DFMT_USE_RVALUE_REFERENCES -DFMT_USE_VARIADIC_TEMPLATES -DLENDY_API_EXPORT_GAME -DLENDY_API_USE_DYNAMIC_LIBS -DPROTOCOL_STREAM -I/usr/local/openssl-1.0.2/include -I/home/<USER>/EnergyStorage/AI_Box/src/msgdef -I/home/<USER>/EnergyStorage/AI_Box/src/aibox -I/home/<USER>/EnergyStorage/AI_Box/src/aibox/CMakeFiles -I/home/<USER>/EnergyStorage/AI_Box/src/aibox/CMakeFiles/AI_Box.dir -I/home/<USER>/EnergyStorage/AI_Box/src/aibox/CMakeFiles/AI_Box.dir/data -I/home/<USER>/EnergyStorage/AI_Box/src/aibox/CMakeFiles/AI_Box.dir/http -I/home/<USER>/EnergyStorage/AI_Box/src/aibox/CMakeFiles/AI_Box.dir/http/handler -I/home/<USER>/EnergyStorage/AI_Box/src/aibox/CMakeFiles/valgrind_test.dir -I/home/<USER>/EnergyStorage/AI_Box/src/aibox/data -I/home/<USER>/EnergyStorage/AI_Box/src/aibox/data/NotifyItem -I/home/<USER>/EnergyStorage/AI_Box/src/aibox/http -I/home/<USER>/EnergyStorage/AI_Box/src/aibox/http/handler -I/home/<USER>/EnergyStorage/AI_Box/apilib -I/home/<USER>/EnergyStorage/AI_Box/apilib/define -I/home/<USER>/EnergyStorage/AI_Box/apilib/define/netdef -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/detail -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/detail/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/experimental -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/experimental/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/generic -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/generic/detail -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/generic/detail/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ip -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ip/detail -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ip/detail/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ip/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/local -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/local/detail -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/local/detail/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/posix -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ssl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ssl/detail -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ssl/detail/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ssl/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ts -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/windows -I/home/<USER>/EnergyStorage/AI_Box/apilib/net -I/home/<USER>/EnergyStorage/AI_Box/apilib/net/CMakeFiles -I/home/<USER>/EnergyStorage/AI_Box/apilib/net/CMakeFiles/Net.dir -I/home/<USER>/EnergyStorage/AI_Box/apilib/db -I/home/<USER>/EnergyStorage/AI_Box/apilib/db/CMakeFiles -I/home/<USER>/EnergyStorage/AI_Box/apilib/db/CMakeFiles/DataBase.dir -I/home/<USER>/EnergyStorage/AI_Box/apilib/db/CMakeFiles/DataBase.dir/Implementation -I/home/<USER>/EnergyStorage/AI_Box/apilib/db/Implementation -I/home/<USER>/EnergyStorage/AI_Box/apilib/log -I/home/<USER>/EnergyStorage/AI_Box/apilib/log/CMakeFiles -I/home/<USER>/EnergyStorage/AI_Box/apilib/log/CMakeFiles/Log.dir -I/home/<USER>/EnergyStorage/AI_Box/apilib/util -I/home/<USER>/EnergyStorage/AI_Box/apilib/util/CMakeFiles -I/home/<USER>/EnergyStorage/AI_Box/apilib/util/CMakeFiles/Util.dir -I/home/<USER>/EnergyStorage/AI_Box/vision_data.h -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/fmt -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/utf8cpp -isystem /usr/include/mysql -std=c++14 -O3 -fPIC -g -std=gnu++14 -o CMakeFiles/AI_Box.dir/data/CUserInfo.cpp.o -c /home/<USER>/EnergyStorage/AI_Box/src/aibox/data/CUserInfo.cpp", "file": "/home/<USER>/EnergyStorage/AI_Box/src/aibox/data/CUserInfo.cpp", "output": "src/aibox/CMakeFiles/AI_Box.dir/data/CUserInfo.cpp.o"}, {"directory": "/home/<USER>/EnergyStorage/AI_Box/src/aibox", "command": "/usr/bin/c++ -<PERSON><PERSON><PERSON>_STANDALONE -DBOOST_ATOMIC_DYN_LINK -DBOOST_ATOMIC_NO_LIB -DBOOST_FILESYSTEM_DYN_LINK -DBOOST_FILESYSTEM_NO_LIB -DBOOST_SYSTEM_DYN_LINK -DBOOST_SYSTEM_NO_LIB -DFMT_USE_DELETED_FUNCTIONS -DFMT_USE_EXTERN_TEMPLATES -DFMT_USE_OVERRIDE -DFMT_USE_RVALUE_REFERENCES -DFMT_USE_VARIADIC_TEMPLATES -DLENDY_API_EXPORT_GAME -DLENDY_API_USE_DYNAMIC_LIBS -DPROTOCOL_STREAM -I/usr/local/openssl-1.0.2/include -I/home/<USER>/EnergyStorage/AI_Box/src/msgdef -I/home/<USER>/EnergyStorage/AI_Box/src/aibox -I/home/<USER>/EnergyStorage/AI_Box/src/aibox/CMakeFiles -I/home/<USER>/EnergyStorage/AI_Box/src/aibox/CMakeFiles/AI_Box.dir -I/home/<USER>/EnergyStorage/AI_Box/src/aibox/CMakeFiles/AI_Box.dir/data -I/home/<USER>/EnergyStorage/AI_Box/src/aibox/CMakeFiles/AI_Box.dir/http -I/home/<USER>/EnergyStorage/AI_Box/src/aibox/CMakeFiles/AI_Box.dir/http/handler -I/home/<USER>/EnergyStorage/AI_Box/src/aibox/CMakeFiles/valgrind_test.dir -I/home/<USER>/EnergyStorage/AI_Box/src/aibox/data -I/home/<USER>/EnergyStorage/AI_Box/src/aibox/data/NotifyItem -I/home/<USER>/EnergyStorage/AI_Box/src/aibox/http -I/home/<USER>/EnergyStorage/AI_Box/src/aibox/http/handler -I/home/<USER>/EnergyStorage/AI_Box/apilib -I/home/<USER>/EnergyStorage/AI_Box/apilib/define -I/home/<USER>/EnergyStorage/AI_Box/apilib/define/netdef -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/detail -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/detail/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/experimental -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/experimental/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/generic -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/generic/detail -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/generic/detail/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ip -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ip/detail -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ip/detail/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ip/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/local -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/local/detail -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/local/detail/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/posix -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ssl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ssl/detail -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ssl/detail/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ssl/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ts -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/windows -I/home/<USER>/EnergyStorage/AI_Box/apilib/net -I/home/<USER>/EnergyStorage/AI_Box/apilib/net/CMakeFiles -I/home/<USER>/EnergyStorage/AI_Box/apilib/net/CMakeFiles/Net.dir -I/home/<USER>/EnergyStorage/AI_Box/apilib/db -I/home/<USER>/EnergyStorage/AI_Box/apilib/db/CMakeFiles -I/home/<USER>/EnergyStorage/AI_Box/apilib/db/CMakeFiles/DataBase.dir -I/home/<USER>/EnergyStorage/AI_Box/apilib/db/CMakeFiles/DataBase.dir/Implementation -I/home/<USER>/EnergyStorage/AI_Box/apilib/db/Implementation -I/home/<USER>/EnergyStorage/AI_Box/apilib/log -I/home/<USER>/EnergyStorage/AI_Box/apilib/log/CMakeFiles -I/home/<USER>/EnergyStorage/AI_Box/apilib/log/CMakeFiles/Log.dir -I/home/<USER>/EnergyStorage/AI_Box/apilib/util -I/home/<USER>/EnergyStorage/AI_Box/apilib/util/CMakeFiles -I/home/<USER>/EnergyStorage/AI_Box/apilib/util/CMakeFiles/Util.dir -I/home/<USER>/EnergyStorage/AI_Box/vision_data.h -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/fmt -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/utf8cpp -isystem /usr/include/mysql -std=c++14 -O3 -fPIC -g -std=gnu++14 -o CMakeFiles/AI_Box.dir/http/CHandler.cpp.o -c /home/<USER>/EnergyStorage/AI_Box/src/aibox/http/CHandler.cpp", "file": "/home/<USER>/EnergyStorage/AI_Box/src/aibox/http/CHandler.cpp", "output": "src/aibox/CMakeFiles/AI_Box.dir/http/CHandler.cpp.o"}, {"directory": "/home/<USER>/EnergyStorage/AI_Box/src/aibox", "command": "/usr/bin/c++ -<PERSON><PERSON><PERSON>_STANDALONE -DBOOST_ATOMIC_DYN_LINK -DBOOST_ATOMIC_NO_LIB -DBOOST_FILESYSTEM_DYN_LINK -DBOOST_FILESYSTEM_NO_LIB -DBOOST_SYSTEM_DYN_LINK -DBOOST_SYSTEM_NO_LIB -DFMT_USE_DELETED_FUNCTIONS -DFMT_USE_EXTERN_TEMPLATES -DFMT_USE_OVERRIDE -DFMT_USE_RVALUE_REFERENCES -DFMT_USE_VARIADIC_TEMPLATES -DLENDY_API_EXPORT_GAME -DLENDY_API_USE_DYNAMIC_LIBS -DPROTOCOL_STREAM -I/usr/local/openssl-1.0.2/include -I/home/<USER>/EnergyStorage/AI_Box/src/msgdef -I/home/<USER>/EnergyStorage/AI_Box/src/aibox -I/home/<USER>/EnergyStorage/AI_Box/src/aibox/CMakeFiles -I/home/<USER>/EnergyStorage/AI_Box/src/aibox/CMakeFiles/AI_Box.dir -I/home/<USER>/EnergyStorage/AI_Box/src/aibox/CMakeFiles/AI_Box.dir/data -I/home/<USER>/EnergyStorage/AI_Box/src/aibox/CMakeFiles/AI_Box.dir/http -I/home/<USER>/EnergyStorage/AI_Box/src/aibox/CMakeFiles/AI_Box.dir/http/handler -I/home/<USER>/EnergyStorage/AI_Box/src/aibox/CMakeFiles/valgrind_test.dir -I/home/<USER>/EnergyStorage/AI_Box/src/aibox/data -I/home/<USER>/EnergyStorage/AI_Box/src/aibox/data/NotifyItem -I/home/<USER>/EnergyStorage/AI_Box/src/aibox/http -I/home/<USER>/EnergyStorage/AI_Box/src/aibox/http/handler -I/home/<USER>/EnergyStorage/AI_Box/apilib -I/home/<USER>/EnergyStorage/AI_Box/apilib/define -I/home/<USER>/EnergyStorage/AI_Box/apilib/define/netdef -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/detail -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/detail/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/experimental -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/experimental/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/generic -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/generic/detail -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/generic/detail/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ip -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ip/detail -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ip/detail/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ip/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/local -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/local/detail -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/local/detail/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/posix -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ssl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ssl/detail -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ssl/detail/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ssl/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ts -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/windows -I/home/<USER>/EnergyStorage/AI_Box/apilib/net -I/home/<USER>/EnergyStorage/AI_Box/apilib/net/CMakeFiles -I/home/<USER>/EnergyStorage/AI_Box/apilib/net/CMakeFiles/Net.dir -I/home/<USER>/EnergyStorage/AI_Box/apilib/db -I/home/<USER>/EnergyStorage/AI_Box/apilib/db/CMakeFiles -I/home/<USER>/EnergyStorage/AI_Box/apilib/db/CMakeFiles/DataBase.dir -I/home/<USER>/EnergyStorage/AI_Box/apilib/db/CMakeFiles/DataBase.dir/Implementation -I/home/<USER>/EnergyStorage/AI_Box/apilib/db/Implementation -I/home/<USER>/EnergyStorage/AI_Box/apilib/log -I/home/<USER>/EnergyStorage/AI_Box/apilib/log/CMakeFiles -I/home/<USER>/EnergyStorage/AI_Box/apilib/log/CMakeFiles/Log.dir -I/home/<USER>/EnergyStorage/AI_Box/apilib/util -I/home/<USER>/EnergyStorage/AI_Box/apilib/util/CMakeFiles -I/home/<USER>/EnergyStorage/AI_Box/apilib/util/CMakeFiles/Util.dir -I/home/<USER>/EnergyStorage/AI_Box/vision_data.h -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/fmt -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/utf8cpp -isystem /usr/include/mysql -std=c++14 -O3 -fPIC -g -std=gnu++14 -o CMakeFiles/AI_Box.dir/http/HttpServer.cpp.o -c /home/<USER>/EnergyStorage/AI_Box/src/aibox/http/HttpServer.cpp", "file": "/home/<USER>/EnergyStorage/AI_Box/src/aibox/http/HttpServer.cpp", "output": "src/aibox/CMakeFiles/AI_Box.dir/http/HttpServer.cpp.o"}, {"directory": "/home/<USER>/EnergyStorage/AI_Box/src/aibox", "command": "/usr/bin/c++ -<PERSON><PERSON><PERSON>_STANDALONE -DBOOST_ATOMIC_DYN_LINK -DBOOST_ATOMIC_NO_LIB -DBOOST_FILESYSTEM_DYN_LINK -DBOOST_FILESYSTEM_NO_LIB -DBOOST_SYSTEM_DYN_LINK -DBOOST_SYSTEM_NO_LIB -DFMT_USE_DELETED_FUNCTIONS -DFMT_USE_EXTERN_TEMPLATES -DFMT_USE_OVERRIDE -DFMT_USE_RVALUE_REFERENCES -DFMT_USE_VARIADIC_TEMPLATES -DLENDY_API_EXPORT_GAME -DLENDY_API_USE_DYNAMIC_LIBS -DPROTOCOL_STREAM -I/usr/local/openssl-1.0.2/include -I/home/<USER>/EnergyStorage/AI_Box/src/msgdef -I/home/<USER>/EnergyStorage/AI_Box/src/aibox -I/home/<USER>/EnergyStorage/AI_Box/src/aibox/CMakeFiles -I/home/<USER>/EnergyStorage/AI_Box/src/aibox/CMakeFiles/AI_Box.dir -I/home/<USER>/EnergyStorage/AI_Box/src/aibox/CMakeFiles/AI_Box.dir/data -I/home/<USER>/EnergyStorage/AI_Box/src/aibox/CMakeFiles/AI_Box.dir/http -I/home/<USER>/EnergyStorage/AI_Box/src/aibox/CMakeFiles/AI_Box.dir/http/handler -I/home/<USER>/EnergyStorage/AI_Box/src/aibox/CMakeFiles/valgrind_test.dir -I/home/<USER>/EnergyStorage/AI_Box/src/aibox/data -I/home/<USER>/EnergyStorage/AI_Box/src/aibox/data/NotifyItem -I/home/<USER>/EnergyStorage/AI_Box/src/aibox/http -I/home/<USER>/EnergyStorage/AI_Box/src/aibox/http/handler -I/home/<USER>/EnergyStorage/AI_Box/apilib -I/home/<USER>/EnergyStorage/AI_Box/apilib/define -I/home/<USER>/EnergyStorage/AI_Box/apilib/define/netdef -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/detail -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/detail/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/experimental -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/experimental/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/generic -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/generic/detail -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/generic/detail/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ip -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ip/detail -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ip/detail/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ip/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/local -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/local/detail -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/local/detail/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/posix -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ssl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ssl/detail -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ssl/detail/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ssl/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ts -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/windows -I/home/<USER>/EnergyStorage/AI_Box/apilib/net -I/home/<USER>/EnergyStorage/AI_Box/apilib/net/CMakeFiles -I/home/<USER>/EnergyStorage/AI_Box/apilib/net/CMakeFiles/Net.dir -I/home/<USER>/EnergyStorage/AI_Box/apilib/db -I/home/<USER>/EnergyStorage/AI_Box/apilib/db/CMakeFiles -I/home/<USER>/EnergyStorage/AI_Box/apilib/db/CMakeFiles/DataBase.dir -I/home/<USER>/EnergyStorage/AI_Box/apilib/db/CMakeFiles/DataBase.dir/Implementation -I/home/<USER>/EnergyStorage/AI_Box/apilib/db/Implementation -I/home/<USER>/EnergyStorage/AI_Box/apilib/log -I/home/<USER>/EnergyStorage/AI_Box/apilib/log/CMakeFiles -I/home/<USER>/EnergyStorage/AI_Box/apilib/log/CMakeFiles/Log.dir -I/home/<USER>/EnergyStorage/AI_Box/apilib/util -I/home/<USER>/EnergyStorage/AI_Box/apilib/util/CMakeFiles -I/home/<USER>/EnergyStorage/AI_Box/apilib/util/CMakeFiles/Util.dir -I/home/<USER>/EnergyStorage/AI_Box/vision_data.h -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/fmt -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/utf8cpp -isystem /usr/include/mysql -std=c++14 -O3 -fPIC -g -std=gnu++14 -o CMakeFiles/AI_Box.dir/http/handler/CAlarmLogHandlerPrivate.cpp.o -c /home/<USER>/EnergyStorage/AI_Box/src/aibox/http/handler/CAlarmLogHandlerPrivate.cpp", "file": "/home/<USER>/EnergyStorage/AI_Box/src/aibox/http/handler/CAlarmLogHandlerPrivate.cpp", "output": "src/aibox/CMakeFiles/AI_Box.dir/http/handler/CAlarmLogHandlerPrivate.cpp.o"}, {"directory": "/home/<USER>/EnergyStorage/AI_Box/src/aibox", "command": "/usr/bin/c++ -<PERSON><PERSON><PERSON>_STANDALONE -DBOOST_ATOMIC_DYN_LINK -DBOOST_ATOMIC_NO_LIB -DBOOST_FILESYSTEM_DYN_LINK -DBOOST_FILESYSTEM_NO_LIB -DBOOST_SYSTEM_DYN_LINK -DBOOST_SYSTEM_NO_LIB -DFMT_USE_DELETED_FUNCTIONS -DFMT_USE_EXTERN_TEMPLATES -DFMT_USE_OVERRIDE -DFMT_USE_RVALUE_REFERENCES -DFMT_USE_VARIADIC_TEMPLATES -DLENDY_API_EXPORT_GAME -DLENDY_API_USE_DYNAMIC_LIBS -DPROTOCOL_STREAM -I/usr/local/openssl-1.0.2/include -I/home/<USER>/EnergyStorage/AI_Box/src/msgdef -I/home/<USER>/EnergyStorage/AI_Box/src/aibox -I/home/<USER>/EnergyStorage/AI_Box/src/aibox/CMakeFiles -I/home/<USER>/EnergyStorage/AI_Box/src/aibox/CMakeFiles/AI_Box.dir -I/home/<USER>/EnergyStorage/AI_Box/src/aibox/CMakeFiles/AI_Box.dir/data -I/home/<USER>/EnergyStorage/AI_Box/src/aibox/CMakeFiles/AI_Box.dir/http -I/home/<USER>/EnergyStorage/AI_Box/src/aibox/CMakeFiles/AI_Box.dir/http/handler -I/home/<USER>/EnergyStorage/AI_Box/src/aibox/CMakeFiles/valgrind_test.dir -I/home/<USER>/EnergyStorage/AI_Box/src/aibox/data -I/home/<USER>/EnergyStorage/AI_Box/src/aibox/data/NotifyItem -I/home/<USER>/EnergyStorage/AI_Box/src/aibox/http -I/home/<USER>/EnergyStorage/AI_Box/src/aibox/http/handler -I/home/<USER>/EnergyStorage/AI_Box/apilib -I/home/<USER>/EnergyStorage/AI_Box/apilib/define -I/home/<USER>/EnergyStorage/AI_Box/apilib/define/netdef -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/detail -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/detail/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/experimental -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/experimental/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/generic -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/generic/detail -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/generic/detail/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ip -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ip/detail -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ip/detail/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ip/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/local -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/local/detail -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/local/detail/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/posix -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ssl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ssl/detail -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ssl/detail/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ssl/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ts -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/windows -I/home/<USER>/EnergyStorage/AI_Box/apilib/net -I/home/<USER>/EnergyStorage/AI_Box/apilib/net/CMakeFiles -I/home/<USER>/EnergyStorage/AI_Box/apilib/net/CMakeFiles/Net.dir -I/home/<USER>/EnergyStorage/AI_Box/apilib/db -I/home/<USER>/EnergyStorage/AI_Box/apilib/db/CMakeFiles -I/home/<USER>/EnergyStorage/AI_Box/apilib/db/CMakeFiles/DataBase.dir -I/home/<USER>/EnergyStorage/AI_Box/apilib/db/CMakeFiles/DataBase.dir/Implementation -I/home/<USER>/EnergyStorage/AI_Box/apilib/db/Implementation -I/home/<USER>/EnergyStorage/AI_Box/apilib/log -I/home/<USER>/EnergyStorage/AI_Box/apilib/log/CMakeFiles -I/home/<USER>/EnergyStorage/AI_Box/apilib/log/CMakeFiles/Log.dir -I/home/<USER>/EnergyStorage/AI_Box/apilib/util -I/home/<USER>/EnergyStorage/AI_Box/apilib/util/CMakeFiles -I/home/<USER>/EnergyStorage/AI_Box/apilib/util/CMakeFiles/Util.dir -I/home/<USER>/EnergyStorage/AI_Box/vision_data.h -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/fmt -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/utf8cpp -isystem /usr/include/mysql -std=c++14 -O3 -fPIC -g -std=gnu++14 -o CMakeFiles/AI_Box.dir/http/handler/CAlgorithmHandlerPrivate.cpp.o -c /home/<USER>/EnergyStorage/AI_Box/src/aibox/http/handler/CAlgorithmHandlerPrivate.cpp", "file": "/home/<USER>/EnergyStorage/AI_Box/src/aibox/http/handler/CAlgorithmHandlerPrivate.cpp", "output": "src/aibox/CMakeFiles/AI_Box.dir/http/handler/CAlgorithmHandlerPrivate.cpp.o"}, {"directory": "/home/<USER>/EnergyStorage/AI_Box/src/aibox", "command": "/usr/bin/c++ -<PERSON><PERSON><PERSON>_STANDALONE -DBOOST_ATOMIC_DYN_LINK -DBOOST_ATOMIC_NO_LIB -DBOOST_FILESYSTEM_DYN_LINK -DBOOST_FILESYSTEM_NO_LIB -DBOOST_SYSTEM_DYN_LINK -DBOOST_SYSTEM_NO_LIB -DFMT_USE_DELETED_FUNCTIONS -DFMT_USE_EXTERN_TEMPLATES -DFMT_USE_OVERRIDE -DFMT_USE_RVALUE_REFERENCES -DFMT_USE_VARIADIC_TEMPLATES -DLENDY_API_EXPORT_GAME -DLENDY_API_USE_DYNAMIC_LIBS -DPROTOCOL_STREAM -I/usr/local/openssl-1.0.2/include -I/home/<USER>/EnergyStorage/AI_Box/src/msgdef -I/home/<USER>/EnergyStorage/AI_Box/src/aibox -I/home/<USER>/EnergyStorage/AI_Box/src/aibox/CMakeFiles -I/home/<USER>/EnergyStorage/AI_Box/src/aibox/CMakeFiles/AI_Box.dir -I/home/<USER>/EnergyStorage/AI_Box/src/aibox/CMakeFiles/AI_Box.dir/data -I/home/<USER>/EnergyStorage/AI_Box/src/aibox/CMakeFiles/AI_Box.dir/http -I/home/<USER>/EnergyStorage/AI_Box/src/aibox/CMakeFiles/AI_Box.dir/http/handler -I/home/<USER>/EnergyStorage/AI_Box/src/aibox/CMakeFiles/valgrind_test.dir -I/home/<USER>/EnergyStorage/AI_Box/src/aibox/data -I/home/<USER>/EnergyStorage/AI_Box/src/aibox/data/NotifyItem -I/home/<USER>/EnergyStorage/AI_Box/src/aibox/http -I/home/<USER>/EnergyStorage/AI_Box/src/aibox/http/handler -I/home/<USER>/EnergyStorage/AI_Box/apilib -I/home/<USER>/EnergyStorage/AI_Box/apilib/define -I/home/<USER>/EnergyStorage/AI_Box/apilib/define/netdef -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/detail -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/detail/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/experimental -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/experimental/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/generic -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/generic/detail -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/generic/detail/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ip -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ip/detail -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ip/detail/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ip/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/local -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/local/detail -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/local/detail/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/posix -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ssl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ssl/detail -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ssl/detail/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ssl/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ts -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/windows -I/home/<USER>/EnergyStorage/AI_Box/apilib/net -I/home/<USER>/EnergyStorage/AI_Box/apilib/net/CMakeFiles -I/home/<USER>/EnergyStorage/AI_Box/apilib/net/CMakeFiles/Net.dir -I/home/<USER>/EnergyStorage/AI_Box/apilib/db -I/home/<USER>/EnergyStorage/AI_Box/apilib/db/CMakeFiles -I/home/<USER>/EnergyStorage/AI_Box/apilib/db/CMakeFiles/DataBase.dir -I/home/<USER>/EnergyStorage/AI_Box/apilib/db/CMakeFiles/DataBase.dir/Implementation -I/home/<USER>/EnergyStorage/AI_Box/apilib/db/Implementation -I/home/<USER>/EnergyStorage/AI_Box/apilib/log -I/home/<USER>/EnergyStorage/AI_Box/apilib/log/CMakeFiles -I/home/<USER>/EnergyStorage/AI_Box/apilib/log/CMakeFiles/Log.dir -I/home/<USER>/EnergyStorage/AI_Box/apilib/util -I/home/<USER>/EnergyStorage/AI_Box/apilib/util/CMakeFiles -I/home/<USER>/EnergyStorage/AI_Box/apilib/util/CMakeFiles/Util.dir -I/home/<USER>/EnergyStorage/AI_Box/vision_data.h -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/fmt -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/utf8cpp -isystem /usr/include/mysql -std=c++14 -O3 -fPIC -g -std=gnu++14 -o CMakeFiles/AI_Box.dir/http/handler/CBaseBankHandlerPrivate.cpp.o -c /home/<USER>/EnergyStorage/AI_Box/src/aibox/http/handler/CBaseBankHandlerPrivate.cpp", "file": "/home/<USER>/EnergyStorage/AI_Box/src/aibox/http/handler/CBaseBankHandlerPrivate.cpp", "output": "src/aibox/CMakeFiles/AI_Box.dir/http/handler/CBaseBankHandlerPrivate.cpp.o"}, {"directory": "/home/<USER>/EnergyStorage/AI_Box/src/aibox", "command": "/usr/bin/c++ -<PERSON><PERSON><PERSON>_STANDALONE -DBOOST_ATOMIC_DYN_LINK -DBOOST_ATOMIC_NO_LIB -DBOOST_FILESYSTEM_DYN_LINK -DBOOST_FILESYSTEM_NO_LIB -DBOOST_SYSTEM_DYN_LINK -DBOOST_SYSTEM_NO_LIB -DFMT_USE_DELETED_FUNCTIONS -DFMT_USE_EXTERN_TEMPLATES -DFMT_USE_OVERRIDE -DFMT_USE_RVALUE_REFERENCES -DFMT_USE_VARIADIC_TEMPLATES -DLENDY_API_EXPORT_GAME -DLENDY_API_USE_DYNAMIC_LIBS -DPROTOCOL_STREAM -I/usr/local/openssl-1.0.2/include -I/home/<USER>/EnergyStorage/AI_Box/src/msgdef -I/home/<USER>/EnergyStorage/AI_Box/src/aibox -I/home/<USER>/EnergyStorage/AI_Box/src/aibox/CMakeFiles -I/home/<USER>/EnergyStorage/AI_Box/src/aibox/CMakeFiles/AI_Box.dir -I/home/<USER>/EnergyStorage/AI_Box/src/aibox/CMakeFiles/AI_Box.dir/data -I/home/<USER>/EnergyStorage/AI_Box/src/aibox/CMakeFiles/AI_Box.dir/http -I/home/<USER>/EnergyStorage/AI_Box/src/aibox/CMakeFiles/AI_Box.dir/http/handler -I/home/<USER>/EnergyStorage/AI_Box/src/aibox/CMakeFiles/valgrind_test.dir -I/home/<USER>/EnergyStorage/AI_Box/src/aibox/data -I/home/<USER>/EnergyStorage/AI_Box/src/aibox/data/NotifyItem -I/home/<USER>/EnergyStorage/AI_Box/src/aibox/http -I/home/<USER>/EnergyStorage/AI_Box/src/aibox/http/handler -I/home/<USER>/EnergyStorage/AI_Box/apilib -I/home/<USER>/EnergyStorage/AI_Box/apilib/define -I/home/<USER>/EnergyStorage/AI_Box/apilib/define/netdef -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/detail -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/detail/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/experimental -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/experimental/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/generic -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/generic/detail -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/generic/detail/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ip -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ip/detail -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ip/detail/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ip/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/local -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/local/detail -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/local/detail/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/posix -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ssl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ssl/detail -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ssl/detail/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ssl/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ts -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/windows -I/home/<USER>/EnergyStorage/AI_Box/apilib/net -I/home/<USER>/EnergyStorage/AI_Box/apilib/net/CMakeFiles -I/home/<USER>/EnergyStorage/AI_Box/apilib/net/CMakeFiles/Net.dir -I/home/<USER>/EnergyStorage/AI_Box/apilib/db -I/home/<USER>/EnergyStorage/AI_Box/apilib/db/CMakeFiles -I/home/<USER>/EnergyStorage/AI_Box/apilib/db/CMakeFiles/DataBase.dir -I/home/<USER>/EnergyStorage/AI_Box/apilib/db/CMakeFiles/DataBase.dir/Implementation -I/home/<USER>/EnergyStorage/AI_Box/apilib/db/Implementation -I/home/<USER>/EnergyStorage/AI_Box/apilib/log -I/home/<USER>/EnergyStorage/AI_Box/apilib/log/CMakeFiles -I/home/<USER>/EnergyStorage/AI_Box/apilib/log/CMakeFiles/Log.dir -I/home/<USER>/EnergyStorage/AI_Box/apilib/util -I/home/<USER>/EnergyStorage/AI_Box/apilib/util/CMakeFiles -I/home/<USER>/EnergyStorage/AI_Box/apilib/util/CMakeFiles/Util.dir -I/home/<USER>/EnergyStorage/AI_Box/vision_data.h -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/fmt -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/utf8cpp -isystem /usr/include/mysql -std=c++14 -O3 -fPIC -g -std=gnu++14 -o CMakeFiles/AI_Box.dir/http/handler/CDefaultHandlerPrivate.cpp.o -c /home/<USER>/EnergyStorage/AI_Box/src/aibox/http/handler/CDefaultHandlerPrivate.cpp", "file": "/home/<USER>/EnergyStorage/AI_Box/src/aibox/http/handler/CDefaultHandlerPrivate.cpp", "output": "src/aibox/CMakeFiles/AI_Box.dir/http/handler/CDefaultHandlerPrivate.cpp.o"}, {"directory": "/home/<USER>/EnergyStorage/AI_Box/src/aibox", "command": "/usr/bin/c++ -<PERSON><PERSON><PERSON>_STANDALONE -DBOOST_ATOMIC_DYN_LINK -DBOOST_ATOMIC_NO_LIB -DBOOST_FILESYSTEM_DYN_LINK -DBOOST_FILESYSTEM_NO_LIB -DBOOST_SYSTEM_DYN_LINK -DBOOST_SYSTEM_NO_LIB -DFMT_USE_DELETED_FUNCTIONS -DFMT_USE_EXTERN_TEMPLATES -DFMT_USE_OVERRIDE -DFMT_USE_RVALUE_REFERENCES -DFMT_USE_VARIADIC_TEMPLATES -DLENDY_API_EXPORT_GAME -DLENDY_API_USE_DYNAMIC_LIBS -DPROTOCOL_STREAM -I/usr/local/openssl-1.0.2/include -I/home/<USER>/EnergyStorage/AI_Box/src/msgdef -I/home/<USER>/EnergyStorage/AI_Box/src/aibox -I/home/<USER>/EnergyStorage/AI_Box/src/aibox/CMakeFiles -I/home/<USER>/EnergyStorage/AI_Box/src/aibox/CMakeFiles/AI_Box.dir -I/home/<USER>/EnergyStorage/AI_Box/src/aibox/CMakeFiles/AI_Box.dir/data -I/home/<USER>/EnergyStorage/AI_Box/src/aibox/CMakeFiles/AI_Box.dir/http -I/home/<USER>/EnergyStorage/AI_Box/src/aibox/CMakeFiles/AI_Box.dir/http/handler -I/home/<USER>/EnergyStorage/AI_Box/src/aibox/CMakeFiles/valgrind_test.dir -I/home/<USER>/EnergyStorage/AI_Box/src/aibox/data -I/home/<USER>/EnergyStorage/AI_Box/src/aibox/data/NotifyItem -I/home/<USER>/EnergyStorage/AI_Box/src/aibox/http -I/home/<USER>/EnergyStorage/AI_Box/src/aibox/http/handler -I/home/<USER>/EnergyStorage/AI_Box/apilib -I/home/<USER>/EnergyStorage/AI_Box/apilib/define -I/home/<USER>/EnergyStorage/AI_Box/apilib/define/netdef -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/detail -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/detail/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/experimental -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/experimental/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/generic -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/generic/detail -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/generic/detail/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ip -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ip/detail -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ip/detail/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ip/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/local -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/local/detail -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/local/detail/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/posix -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ssl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ssl/detail -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ssl/detail/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ssl/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ts -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/windows -I/home/<USER>/EnergyStorage/AI_Box/apilib/net -I/home/<USER>/EnergyStorage/AI_Box/apilib/net/CMakeFiles -I/home/<USER>/EnergyStorage/AI_Box/apilib/net/CMakeFiles/Net.dir -I/home/<USER>/EnergyStorage/AI_Box/apilib/db -I/home/<USER>/EnergyStorage/AI_Box/apilib/db/CMakeFiles -I/home/<USER>/EnergyStorage/AI_Box/apilib/db/CMakeFiles/DataBase.dir -I/home/<USER>/EnergyStorage/AI_Box/apilib/db/CMakeFiles/DataBase.dir/Implementation -I/home/<USER>/EnergyStorage/AI_Box/apilib/db/Implementation -I/home/<USER>/EnergyStorage/AI_Box/apilib/log -I/home/<USER>/EnergyStorage/AI_Box/apilib/log/CMakeFiles -I/home/<USER>/EnergyStorage/AI_Box/apilib/log/CMakeFiles/Log.dir -I/home/<USER>/EnergyStorage/AI_Box/apilib/util -I/home/<USER>/EnergyStorage/AI_Box/apilib/util/CMakeFiles -I/home/<USER>/EnergyStorage/AI_Box/apilib/util/CMakeFiles/Util.dir -I/home/<USER>/EnergyStorage/AI_Box/vision_data.h -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/fmt -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/utf8cpp -isystem /usr/include/mysql -std=c++14 -O3 -fPIC -g -std=gnu++14 -o CMakeFiles/AI_Box.dir/http/handler/CFileHandlerPrivate.cpp.o -c /home/<USER>/EnergyStorage/AI_Box/src/aibox/http/handler/CFileHandlerPrivate.cpp", "file": "/home/<USER>/EnergyStorage/AI_Box/src/aibox/http/handler/CFileHandlerPrivate.cpp", "output": "src/aibox/CMakeFiles/AI_Box.dir/http/handler/CFileHandlerPrivate.cpp.o"}, {"directory": "/home/<USER>/EnergyStorage/AI_Box/src/aibox", "command": "/usr/bin/c++ -<PERSON><PERSON><PERSON>_STANDALONE -DBOOST_ATOMIC_DYN_LINK -DBOOST_ATOMIC_NO_LIB -DBOOST_FILESYSTEM_DYN_LINK -DBOOST_FILESYSTEM_NO_LIB -DBOOST_SYSTEM_DYN_LINK -DBOOST_SYSTEM_NO_LIB -DFMT_USE_DELETED_FUNCTIONS -DFMT_USE_EXTERN_TEMPLATES -DFMT_USE_OVERRIDE -DFMT_USE_RVALUE_REFERENCES -DFMT_USE_VARIADIC_TEMPLATES -DLENDY_API_EXPORT_GAME -DLENDY_API_USE_DYNAMIC_LIBS -DPROTOCOL_STREAM -I/usr/local/openssl-1.0.2/include -I/home/<USER>/EnergyStorage/AI_Box/src/msgdef -I/home/<USER>/EnergyStorage/AI_Box/src/aibox -I/home/<USER>/EnergyStorage/AI_Box/src/aibox/CMakeFiles -I/home/<USER>/EnergyStorage/AI_Box/src/aibox/CMakeFiles/AI_Box.dir -I/home/<USER>/EnergyStorage/AI_Box/src/aibox/CMakeFiles/AI_Box.dir/data -I/home/<USER>/EnergyStorage/AI_Box/src/aibox/CMakeFiles/AI_Box.dir/http -I/home/<USER>/EnergyStorage/AI_Box/src/aibox/CMakeFiles/AI_Box.dir/http/handler -I/home/<USER>/EnergyStorage/AI_Box/src/aibox/CMakeFiles/valgrind_test.dir -I/home/<USER>/EnergyStorage/AI_Box/src/aibox/data -I/home/<USER>/EnergyStorage/AI_Box/src/aibox/data/NotifyItem -I/home/<USER>/EnergyStorage/AI_Box/src/aibox/http -I/home/<USER>/EnergyStorage/AI_Box/src/aibox/http/handler -I/home/<USER>/EnergyStorage/AI_Box/apilib -I/home/<USER>/EnergyStorage/AI_Box/apilib/define -I/home/<USER>/EnergyStorage/AI_Box/apilib/define/netdef -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/detail -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/detail/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/experimental -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/experimental/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/generic -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/generic/detail -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/generic/detail/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ip -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ip/detail -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ip/detail/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ip/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/local -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/local/detail -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/local/detail/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/posix -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ssl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ssl/detail -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ssl/detail/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ssl/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ts -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/windows -I/home/<USER>/EnergyStorage/AI_Box/apilib/net -I/home/<USER>/EnergyStorage/AI_Box/apilib/net/CMakeFiles -I/home/<USER>/EnergyStorage/AI_Box/apilib/net/CMakeFiles/Net.dir -I/home/<USER>/EnergyStorage/AI_Box/apilib/db -I/home/<USER>/EnergyStorage/AI_Box/apilib/db/CMakeFiles -I/home/<USER>/EnergyStorage/AI_Box/apilib/db/CMakeFiles/DataBase.dir -I/home/<USER>/EnergyStorage/AI_Box/apilib/db/CMakeFiles/DataBase.dir/Implementation -I/home/<USER>/EnergyStorage/AI_Box/apilib/db/Implementation -I/home/<USER>/EnergyStorage/AI_Box/apilib/log -I/home/<USER>/EnergyStorage/AI_Box/apilib/log/CMakeFiles -I/home/<USER>/EnergyStorage/AI_Box/apilib/log/CMakeFiles/Log.dir -I/home/<USER>/EnergyStorage/AI_Box/apilib/util -I/home/<USER>/EnergyStorage/AI_Box/apilib/util/CMakeFiles -I/home/<USER>/EnergyStorage/AI_Box/apilib/util/CMakeFiles/Util.dir -I/home/<USER>/EnergyStorage/AI_Box/vision_data.h -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/fmt -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/utf8cpp -isystem /usr/include/mysql -std=c++14 -O3 -fPIC -g -std=gnu++14 -o CMakeFiles/AI_Box.dir/http/handler/CHttpTools.cpp.o -c /home/<USER>/EnergyStorage/AI_Box/src/aibox/http/handler/CHttpTools.cpp", "file": "/home/<USER>/EnergyStorage/AI_Box/src/aibox/http/handler/CHttpTools.cpp", "output": "src/aibox/CMakeFiles/AI_Box.dir/http/handler/CHttpTools.cpp.o"}, {"directory": "/home/<USER>/EnergyStorage/AI_Box/src/aibox", "command": "/usr/bin/c++ -<PERSON><PERSON><PERSON>_STANDALONE -DBOOST_ATOMIC_DYN_LINK -DBOOST_ATOMIC_NO_LIB -DBOOST_FILESYSTEM_DYN_LINK -DBOOST_FILESYSTEM_NO_LIB -DBOOST_SYSTEM_DYN_LINK -DBOOST_SYSTEM_NO_LIB -DFMT_USE_DELETED_FUNCTIONS -DFMT_USE_EXTERN_TEMPLATES -DFMT_USE_OVERRIDE -DFMT_USE_RVALUE_REFERENCES -DFMT_USE_VARIADIC_TEMPLATES -DLENDY_API_EXPORT_GAME -DLENDY_API_USE_DYNAMIC_LIBS -DPROTOCOL_STREAM -I/usr/local/openssl-1.0.2/include -I/home/<USER>/EnergyStorage/AI_Box/src/msgdef -I/home/<USER>/EnergyStorage/AI_Box/src/aibox -I/home/<USER>/EnergyStorage/AI_Box/src/aibox/CMakeFiles -I/home/<USER>/EnergyStorage/AI_Box/src/aibox/CMakeFiles/AI_Box.dir -I/home/<USER>/EnergyStorage/AI_Box/src/aibox/CMakeFiles/AI_Box.dir/data -I/home/<USER>/EnergyStorage/AI_Box/src/aibox/CMakeFiles/AI_Box.dir/http -I/home/<USER>/EnergyStorage/AI_Box/src/aibox/CMakeFiles/AI_Box.dir/http/handler -I/home/<USER>/EnergyStorage/AI_Box/src/aibox/CMakeFiles/valgrind_test.dir -I/home/<USER>/EnergyStorage/AI_Box/src/aibox/data -I/home/<USER>/EnergyStorage/AI_Box/src/aibox/data/NotifyItem -I/home/<USER>/EnergyStorage/AI_Box/src/aibox/http -I/home/<USER>/EnergyStorage/AI_Box/src/aibox/http/handler -I/home/<USER>/EnergyStorage/AI_Box/apilib -I/home/<USER>/EnergyStorage/AI_Box/apilib/define -I/home/<USER>/EnergyStorage/AI_Box/apilib/define/netdef -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/detail -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/detail/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/experimental -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/experimental/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/generic -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/generic/detail -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/generic/detail/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ip -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ip/detail -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ip/detail/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ip/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/local -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/local/detail -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/local/detail/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/posix -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ssl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ssl/detail -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ssl/detail/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ssl/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ts -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/windows -I/home/<USER>/EnergyStorage/AI_Box/apilib/net -I/home/<USER>/EnergyStorage/AI_Box/apilib/net/CMakeFiles -I/home/<USER>/EnergyStorage/AI_Box/apilib/net/CMakeFiles/Net.dir -I/home/<USER>/EnergyStorage/AI_Box/apilib/db -I/home/<USER>/EnergyStorage/AI_Box/apilib/db/CMakeFiles -I/home/<USER>/EnergyStorage/AI_Box/apilib/db/CMakeFiles/DataBase.dir -I/home/<USER>/EnergyStorage/AI_Box/apilib/db/CMakeFiles/DataBase.dir/Implementation -I/home/<USER>/EnergyStorage/AI_Box/apilib/db/Implementation -I/home/<USER>/EnergyStorage/AI_Box/apilib/log -I/home/<USER>/EnergyStorage/AI_Box/apilib/log/CMakeFiles -I/home/<USER>/EnergyStorage/AI_Box/apilib/log/CMakeFiles/Log.dir -I/home/<USER>/EnergyStorage/AI_Box/apilib/util -I/home/<USER>/EnergyStorage/AI_Box/apilib/util/CMakeFiles -I/home/<USER>/EnergyStorage/AI_Box/apilib/util/CMakeFiles/Util.dir -I/home/<USER>/EnergyStorage/AI_Box/vision_data.h -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/fmt -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/utf8cpp -isystem /usr/include/mysql -std=c++14 -O3 -fPIC -g -std=gnu++14 -o CMakeFiles/AI_Box.dir/http/handler/CInputSourceHandlerPrivate.cpp.o -c /home/<USER>/EnergyStorage/AI_Box/src/aibox/http/handler/CInputSourceHandlerPrivate.cpp", "file": "/home/<USER>/EnergyStorage/AI_Box/src/aibox/http/handler/CInputSourceHandlerPrivate.cpp", "output": "src/aibox/CMakeFiles/AI_Box.dir/http/handler/CInputSourceHandlerPrivate.cpp.o"}, {"directory": "/home/<USER>/EnergyStorage/AI_Box/src/aibox", "command": "/usr/bin/c++ -<PERSON><PERSON><PERSON>_STANDALONE -DBOOST_ATOMIC_DYN_LINK -DBOOST_ATOMIC_NO_LIB -DBOOST_FILESYSTEM_DYN_LINK -DBOOST_FILESYSTEM_NO_LIB -DBOOST_SYSTEM_DYN_LINK -DBOOST_SYSTEM_NO_LIB -DFMT_USE_DELETED_FUNCTIONS -DFMT_USE_EXTERN_TEMPLATES -DFMT_USE_OVERRIDE -DFMT_USE_RVALUE_REFERENCES -DFMT_USE_VARIADIC_TEMPLATES -DLENDY_API_EXPORT_GAME -DLENDY_API_USE_DYNAMIC_LIBS -DPROTOCOL_STREAM -I/usr/local/openssl-1.0.2/include -I/home/<USER>/EnergyStorage/AI_Box/src/msgdef -I/home/<USER>/EnergyStorage/AI_Box/src/aibox -I/home/<USER>/EnergyStorage/AI_Box/src/aibox/CMakeFiles -I/home/<USER>/EnergyStorage/AI_Box/src/aibox/CMakeFiles/AI_Box.dir -I/home/<USER>/EnergyStorage/AI_Box/src/aibox/CMakeFiles/AI_Box.dir/data -I/home/<USER>/EnergyStorage/AI_Box/src/aibox/CMakeFiles/AI_Box.dir/http -I/home/<USER>/EnergyStorage/AI_Box/src/aibox/CMakeFiles/AI_Box.dir/http/handler -I/home/<USER>/EnergyStorage/AI_Box/src/aibox/CMakeFiles/valgrind_test.dir -I/home/<USER>/EnergyStorage/AI_Box/src/aibox/data -I/home/<USER>/EnergyStorage/AI_Box/src/aibox/data/NotifyItem -I/home/<USER>/EnergyStorage/AI_Box/src/aibox/http -I/home/<USER>/EnergyStorage/AI_Box/src/aibox/http/handler -I/home/<USER>/EnergyStorage/AI_Box/apilib -I/home/<USER>/EnergyStorage/AI_Box/apilib/define -I/home/<USER>/EnergyStorage/AI_Box/apilib/define/netdef -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/detail -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/detail/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/experimental -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/experimental/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/generic -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/generic/detail -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/generic/detail/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ip -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ip/detail -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ip/detail/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ip/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/local -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/local/detail -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/local/detail/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/posix -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ssl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ssl/detail -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ssl/detail/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ssl/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ts -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/windows -I/home/<USER>/EnergyStorage/AI_Box/apilib/net -I/home/<USER>/EnergyStorage/AI_Box/apilib/net/CMakeFiles -I/home/<USER>/EnergyStorage/AI_Box/apilib/net/CMakeFiles/Net.dir -I/home/<USER>/EnergyStorage/AI_Box/apilib/db -I/home/<USER>/EnergyStorage/AI_Box/apilib/db/CMakeFiles -I/home/<USER>/EnergyStorage/AI_Box/apilib/db/CMakeFiles/DataBase.dir -I/home/<USER>/EnergyStorage/AI_Box/apilib/db/CMakeFiles/DataBase.dir/Implementation -I/home/<USER>/EnergyStorage/AI_Box/apilib/db/Implementation -I/home/<USER>/EnergyStorage/AI_Box/apilib/log -I/home/<USER>/EnergyStorage/AI_Box/apilib/log/CMakeFiles -I/home/<USER>/EnergyStorage/AI_Box/apilib/log/CMakeFiles/Log.dir -I/home/<USER>/EnergyStorage/AI_Box/apilib/util -I/home/<USER>/EnergyStorage/AI_Box/apilib/util/CMakeFiles -I/home/<USER>/EnergyStorage/AI_Box/apilib/util/CMakeFiles/Util.dir -I/home/<USER>/EnergyStorage/AI_Box/vision_data.h -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/fmt -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/utf8cpp -isystem /usr/include/mysql -std=c++14 -O3 -fPIC -g -std=gnu++14 -o CMakeFiles/AI_Box.dir/http/handler/COverlineHandlerPrivate.cpp.o -c /home/<USER>/EnergyStorage/AI_Box/src/aibox/http/handler/COverlineHandlerPrivate.cpp", "file": "/home/<USER>/EnergyStorage/AI_Box/src/aibox/http/handler/COverlineHandlerPrivate.cpp", "output": "src/aibox/CMakeFiles/AI_Box.dir/http/handler/COverlineHandlerPrivate.cpp.o"}, {"directory": "/home/<USER>/EnergyStorage/AI_Box/src/aibox", "command": "/usr/bin/c++ -<PERSON><PERSON><PERSON>_STANDALONE -DBOOST_ATOMIC_DYN_LINK -DBOOST_ATOMIC_NO_LIB -DBOOST_FILESYSTEM_DYN_LINK -DBOOST_FILESYSTEM_NO_LIB -DBOOST_SYSTEM_DYN_LINK -DBOOST_SYSTEM_NO_LIB -DFMT_USE_DELETED_FUNCTIONS -DFMT_USE_EXTERN_TEMPLATES -DFMT_USE_OVERRIDE -DFMT_USE_RVALUE_REFERENCES -DFMT_USE_VARIADIC_TEMPLATES -DLENDY_API_EXPORT_GAME -DLENDY_API_USE_DYNAMIC_LIBS -DPROTOCOL_STREAM -I/usr/local/openssl-1.0.2/include -I/home/<USER>/EnergyStorage/AI_Box/src/msgdef -I/home/<USER>/EnergyStorage/AI_Box/src/aibox -I/home/<USER>/EnergyStorage/AI_Box/src/aibox/CMakeFiles -I/home/<USER>/EnergyStorage/AI_Box/src/aibox/CMakeFiles/AI_Box.dir -I/home/<USER>/EnergyStorage/AI_Box/src/aibox/CMakeFiles/AI_Box.dir/data -I/home/<USER>/EnergyStorage/AI_Box/src/aibox/CMakeFiles/AI_Box.dir/http -I/home/<USER>/EnergyStorage/AI_Box/src/aibox/CMakeFiles/AI_Box.dir/http/handler -I/home/<USER>/EnergyStorage/AI_Box/src/aibox/CMakeFiles/valgrind_test.dir -I/home/<USER>/EnergyStorage/AI_Box/src/aibox/data -I/home/<USER>/EnergyStorage/AI_Box/src/aibox/data/NotifyItem -I/home/<USER>/EnergyStorage/AI_Box/src/aibox/http -I/home/<USER>/EnergyStorage/AI_Box/src/aibox/http/handler -I/home/<USER>/EnergyStorage/AI_Box/apilib -I/home/<USER>/EnergyStorage/AI_Box/apilib/define -I/home/<USER>/EnergyStorage/AI_Box/apilib/define/netdef -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/detail -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/detail/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/experimental -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/experimental/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/generic -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/generic/detail -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/generic/detail/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ip -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ip/detail -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ip/detail/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ip/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/local -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/local/detail -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/local/detail/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/posix -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ssl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ssl/detail -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ssl/detail/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ssl/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ts -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/windows -I/home/<USER>/EnergyStorage/AI_Box/apilib/net -I/home/<USER>/EnergyStorage/AI_Box/apilib/net/CMakeFiles -I/home/<USER>/EnergyStorage/AI_Box/apilib/net/CMakeFiles/Net.dir -I/home/<USER>/EnergyStorage/AI_Box/apilib/db -I/home/<USER>/EnergyStorage/AI_Box/apilib/db/CMakeFiles -I/home/<USER>/EnergyStorage/AI_Box/apilib/db/CMakeFiles/DataBase.dir -I/home/<USER>/EnergyStorage/AI_Box/apilib/db/CMakeFiles/DataBase.dir/Implementation -I/home/<USER>/EnergyStorage/AI_Box/apilib/db/Implementation -I/home/<USER>/EnergyStorage/AI_Box/apilib/log -I/home/<USER>/EnergyStorage/AI_Box/apilib/log/CMakeFiles -I/home/<USER>/EnergyStorage/AI_Box/apilib/log/CMakeFiles/Log.dir -I/home/<USER>/EnergyStorage/AI_Box/apilib/util -I/home/<USER>/EnergyStorage/AI_Box/apilib/util/CMakeFiles -I/home/<USER>/EnergyStorage/AI_Box/apilib/util/CMakeFiles/Util.dir -I/home/<USER>/EnergyStorage/AI_Box/vision_data.h -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/fmt -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/utf8cpp -isystem /usr/include/mysql -std=c++14 -O3 -fPIC -g -std=gnu++14 -o CMakeFiles/AI_Box.dir/http/handler/CSysHandlerPrivate.cpp.o -c /home/<USER>/EnergyStorage/AI_Box/src/aibox/http/handler/CSysHandlerPrivate.cpp", "file": "/home/<USER>/EnergyStorage/AI_Box/src/aibox/http/handler/CSysHandlerPrivate.cpp", "output": "src/aibox/CMakeFiles/AI_Box.dir/http/handler/CSysHandlerPrivate.cpp.o"}, {"directory": "/home/<USER>/EnergyStorage/AI_Box/src/aibox", "command": "/usr/bin/c++ -<PERSON><PERSON><PERSON>_STANDALONE -DBOOST_ATOMIC_DYN_LINK -DBOOST_ATOMIC_NO_LIB -DBOOST_FILESYSTEM_DYN_LINK -DBOOST_FILESYSTEM_NO_LIB -DBOOST_SYSTEM_DYN_LINK -DBOOST_SYSTEM_NO_LIB -DFMT_USE_DELETED_FUNCTIONS -DFMT_USE_EXTERN_TEMPLATES -DFMT_USE_OVERRIDE -DFMT_USE_RVALUE_REFERENCES -DFMT_USE_VARIADIC_TEMPLATES -DLENDY_API_EXPORT_GAME -DLENDY_API_USE_DYNAMIC_LIBS -DPROTOCOL_STREAM -I/usr/local/openssl-1.0.2/include -I/home/<USER>/EnergyStorage/AI_Box/src/msgdef -I/home/<USER>/EnergyStorage/AI_Box/src/aibox -I/home/<USER>/EnergyStorage/AI_Box/src/aibox/CMakeFiles -I/home/<USER>/EnergyStorage/AI_Box/src/aibox/CMakeFiles/AI_Box.dir -I/home/<USER>/EnergyStorage/AI_Box/src/aibox/CMakeFiles/AI_Box.dir/data -I/home/<USER>/EnergyStorage/AI_Box/src/aibox/CMakeFiles/AI_Box.dir/http -I/home/<USER>/EnergyStorage/AI_Box/src/aibox/CMakeFiles/AI_Box.dir/http/handler -I/home/<USER>/EnergyStorage/AI_Box/src/aibox/CMakeFiles/valgrind_test.dir -I/home/<USER>/EnergyStorage/AI_Box/src/aibox/data -I/home/<USER>/EnergyStorage/AI_Box/src/aibox/data/NotifyItem -I/home/<USER>/EnergyStorage/AI_Box/src/aibox/http -I/home/<USER>/EnergyStorage/AI_Box/src/aibox/http/handler -I/home/<USER>/EnergyStorage/AI_Box/apilib -I/home/<USER>/EnergyStorage/AI_Box/apilib/define -I/home/<USER>/EnergyStorage/AI_Box/apilib/define/netdef -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/detail -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/detail/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/experimental -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/experimental/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/generic -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/generic/detail -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/generic/detail/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ip -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ip/detail -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ip/detail/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ip/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/local -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/local/detail -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/local/detail/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/posix -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ssl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ssl/detail -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ssl/detail/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ssl/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ts -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/windows -I/home/<USER>/EnergyStorage/AI_Box/apilib/net -I/home/<USER>/EnergyStorage/AI_Box/apilib/net/CMakeFiles -I/home/<USER>/EnergyStorage/AI_Box/apilib/net/CMakeFiles/Net.dir -I/home/<USER>/EnergyStorage/AI_Box/apilib/db -I/home/<USER>/EnergyStorage/AI_Box/apilib/db/CMakeFiles -I/home/<USER>/EnergyStorage/AI_Box/apilib/db/CMakeFiles/DataBase.dir -I/home/<USER>/EnergyStorage/AI_Box/apilib/db/CMakeFiles/DataBase.dir/Implementation -I/home/<USER>/EnergyStorage/AI_Box/apilib/db/Implementation -I/home/<USER>/EnergyStorage/AI_Box/apilib/log -I/home/<USER>/EnergyStorage/AI_Box/apilib/log/CMakeFiles -I/home/<USER>/EnergyStorage/AI_Box/apilib/log/CMakeFiles/Log.dir -I/home/<USER>/EnergyStorage/AI_Box/apilib/util -I/home/<USER>/EnergyStorage/AI_Box/apilib/util/CMakeFiles -I/home/<USER>/EnergyStorage/AI_Box/apilib/util/CMakeFiles/Util.dir -I/home/<USER>/EnergyStorage/AI_Box/vision_data.h -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/fmt -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/utf8cpp -isystem /usr/include/mysql -std=c++14 -O3 -fPIC -g -std=gnu++14 -o CMakeFiles/AI_Box.dir/http/handler/CTools.cpp.o -c /home/<USER>/EnergyStorage/AI_Box/src/aibox/http/handler/CTools.cpp", "file": "/home/<USER>/EnergyStorage/AI_Box/src/aibox/http/handler/CTools.cpp", "output": "src/aibox/CMakeFiles/AI_Box.dir/http/handler/CTools.cpp.o"}, {"directory": "/home/<USER>/EnergyStorage/AI_Box/src/aibox", "command": "/usr/bin/c++ -<PERSON><PERSON><PERSON>_STANDALONE -DBOOST_ATOMIC_DYN_LINK -DBOOST_ATOMIC_NO_LIB -DBOOST_FILESYSTEM_DYN_LINK -DBOOST_FILESYSTEM_NO_LIB -DBOOST_SYSTEM_DYN_LINK -DBOOST_SYSTEM_NO_LIB -DFMT_USE_DELETED_FUNCTIONS -DFMT_USE_EXTERN_TEMPLATES -DFMT_USE_OVERRIDE -DFMT_USE_RVALUE_REFERENCES -DFMT_USE_VARIADIC_TEMPLATES -DLENDY_API_EXPORT_GAME -DLENDY_API_USE_DYNAMIC_LIBS -DPROTOCOL_STREAM -I/usr/local/openssl-1.0.2/include -I/home/<USER>/EnergyStorage/AI_Box/src/msgdef -I/home/<USER>/EnergyStorage/AI_Box/src/aibox -I/home/<USER>/EnergyStorage/AI_Box/src/aibox/CMakeFiles -I/home/<USER>/EnergyStorage/AI_Box/src/aibox/CMakeFiles/AI_Box.dir -I/home/<USER>/EnergyStorage/AI_Box/src/aibox/CMakeFiles/AI_Box.dir/data -I/home/<USER>/EnergyStorage/AI_Box/src/aibox/CMakeFiles/AI_Box.dir/http -I/home/<USER>/EnergyStorage/AI_Box/src/aibox/CMakeFiles/AI_Box.dir/http/handler -I/home/<USER>/EnergyStorage/AI_Box/src/aibox/CMakeFiles/valgrind_test.dir -I/home/<USER>/EnergyStorage/AI_Box/src/aibox/data -I/home/<USER>/EnergyStorage/AI_Box/src/aibox/data/NotifyItem -I/home/<USER>/EnergyStorage/AI_Box/src/aibox/http -I/home/<USER>/EnergyStorage/AI_Box/src/aibox/http/handler -I/home/<USER>/EnergyStorage/AI_Box/apilib -I/home/<USER>/EnergyStorage/AI_Box/apilib/define -I/home/<USER>/EnergyStorage/AI_Box/apilib/define/netdef -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/detail -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/detail/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/experimental -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/experimental/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/generic -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/generic/detail -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/generic/detail/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ip -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ip/detail -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ip/detail/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ip/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/local -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/local/detail -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/local/detail/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/posix -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ssl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ssl/detail -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ssl/detail/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ssl/impl -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/ts -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/asio/asio/windows -I/home/<USER>/EnergyStorage/AI_Box/apilib/net -I/home/<USER>/EnergyStorage/AI_Box/apilib/net/CMakeFiles -I/home/<USER>/EnergyStorage/AI_Box/apilib/net/CMakeFiles/Net.dir -I/home/<USER>/EnergyStorage/AI_Box/apilib/db -I/home/<USER>/EnergyStorage/AI_Box/apilib/db/CMakeFiles -I/home/<USER>/EnergyStorage/AI_Box/apilib/db/CMakeFiles/DataBase.dir -I/home/<USER>/EnergyStorage/AI_Box/apilib/db/CMakeFiles/DataBase.dir/Implementation -I/home/<USER>/EnergyStorage/AI_Box/apilib/db/Implementation -I/home/<USER>/EnergyStorage/AI_Box/apilib/log -I/home/<USER>/EnergyStorage/AI_Box/apilib/log/CMakeFiles -I/home/<USER>/EnergyStorage/AI_Box/apilib/log/CMakeFiles/Log.dir -I/home/<USER>/EnergyStorage/AI_Box/apilib/util -I/home/<USER>/EnergyStorage/AI_Box/apilib/util/CMakeFiles -I/home/<USER>/EnergyStorage/AI_Box/apilib/util/CMakeFiles/Util.dir -I/home/<USER>/EnergyStorage/AI_Box/vision_data.h -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/fmt -I/home/<USER>/EnergyStorage/AI_Box/apilib/dep/utf8cpp -isystem /usr/include/mysql -std=c++14 -O3 -fPIC -g -std=gnu++14 -o CMakeFiles/AI_Box.dir/http/handler/CUserHandlerPrivate.cpp.o -c /home/<USER>/EnergyStorage/AI_Box/src/aibox/http/handler/CUserHandlerPrivate.cpp", "file": "/home/<USER>/EnergyStorage/AI_Box/src/aibox/http/handler/CUserHandlerPrivate.cpp", "output": "src/aibox/CMakeFiles/AI_Box.dir/http/handler/CUserHandlerPrivate.cpp.o"}]