2025-07-15_04:47:50 INFO  Starting Logonserver
2025-07-15_04:47:50 INFO  Host:[0.0.0.0] Port:[8600] ThreadCount:[4]
2025-07-15_04:47:50 INFO  Automatic database updates are disabled for all databases!
2025-07-15_04:47:50 INFO  Opening DatabasePool 'ai_box'. Asynchronous connections: 1, synchronous connections: 1.
2025-07-15_04:47:50 INFO  MySQL client library: 8.0.42
2025-07-15_04:47:50 INFO  MySQL server ver: 8.0.42-0ubuntu0.24.04.1 
2025-07-15_04:47:50 INFO  Connected to MySQL database at 127.0.0.1
2025-07-15_04:47:50 INFO  MySQL client library: 8.0.42
2025-07-15_04:47:50 INFO  MySQL server ver: 8.0.42-0ubuntu0.24.04.1 
2025-07-15_04:47:50 INFO  Connected to MySQL database at 127.0.0.1
2025-07-15_04:47:50 INFO  DatabasePool 'ai_box' opened successfully. 2 total connections running.
2025-07-15_04:47:50 INFO  Service started successfully
2025-07-15_04:47:51 INFO  Client connected: 0.0.0.0, Socket ID: 0
2025-07-15_04:47:53 INFO  OnMainBoardRegister mainboardId:[0.0.0.0]  success
2025-07-15_04:47:53 DEBUG [24 ms] SQL:  SELECT `id`,`‌battery_power`,`rated_capacity_kwh`,`operating_status`,`temp`, `mainboardid`  FROM energy_storage_cabin_table WHERE `mainboardid` = "0.0.0.0" 
2025-07-15_04:47:55 INFO  OnMainBoardRegister mainboardId:[0.0.0.0]  success
2025-07-15_04:47:55 DEBUG [1 ms] SQL:  SELECT `id`,`‌battery_power`,`rated_capacity_kwh`,`operating_status`,`temp`, `mainboardid`  FROM energy_storage_cabin_table WHERE `mainboardid` = "0.0.0.0" 
2025-07-15_04:47:57 INFO  OnMainBoardRegister mainboardId:[0.0.0.0]  success
2025-07-15_04:47:57 DEBUG [1 ms] SQL:  SELECT `id`,`‌battery_power`,`rated_capacity_kwh`,`operating_status`,`temp`, `mainboardid`  FROM energy_storage_cabin_table WHERE `mainboardid` = "0.0.0.0" 
2025-07-15_04:47:59 INFO  OnMainBoardRegister mainboardId:[0.0.0.0]  success
2025-07-15_04:47:59 DEBUG [1 ms] SQL:  SELECT `id`,`‌battery_power`,`rated_capacity_kwh`,`operating_status`,`temp`, `mainboardid`  FROM energy_storage_cabin_table WHERE `mainboardid` = "0.0.0.0" 
2025-07-15_04:48:01 INFO  OnMainBoardRegister mainboardId:[0.0.0.0]  success
2025-07-15_04:48:01 DEBUG [1 ms] SQL:  SELECT `id`,`‌battery_power`,`rated_capacity_kwh`,`operating_status`,`temp`, `mainboardid`  FROM energy_storage_cabin_table WHERE `mainboardid` = "0.0.0.0" 
2025-07-15_04:48:02 INFO  Client connected: 192.168.1.54, Socket ID: 1
2025-07-15_04:48:03 INFO  OnMainBoardRegister mainboardId:[0.0.0.0]  success
2025-07-15_04:48:03 DEBUG [0 ms] SQL:  SELECT `id`,`‌battery_power`,`rated_capacity_kwh`,`operating_status`,`temp`, `mainboardid`  FROM energy_storage_cabin_table WHERE `mainboardid` = "0.0.0.0" 
2025-07-15_04:48:05 INFO  OnMainBoardRegister mainboardId:[0.0.0.0]  success
2025-07-15_04:48:05 DEBUG [0 ms] SQL:  SELECT `id`,`‌battery_power`,`rated_capacity_kwh`,`operating_status`,`temp`, `mainboardid`  FROM energy_storage_cabin_table WHERE `mainboardid` = "0.0.0.0" 
2025-07-15_04:48:07 INFO  OnMainBoardRegister mainboardId:[0.0.0.0]  success
2025-07-15_04:48:07 DEBUG [0 ms] SQL:  SELECT `id`,`‌battery_power`,`rated_capacity_kwh`,`operating_status`,`temp`, `mainboardid`  FROM energy_storage_cabin_table WHERE `mainboardid` = "0.0.0.0" 
2025-07-15_04:48:09 INFO  OnMainBoardRegister mainboardId:[0.0.0.0]  success
2025-07-15_04:48:09 DEBUG [1 ms] SQL:  SELECT `id`,`‌battery_power`,`rated_capacity_kwh`,`operating_status`,`temp`, `mainboardid`  FROM energy_storage_cabin_table WHERE `mainboardid` = "0.0.0.0" 
2025-07-15_04:48:11 INFO  OnMainBoardRegister mainboardId:[0.0.0.0]  success
2025-07-15_04:48:11 DEBUG [2 ms] SQL:  SELECT `id`,`‌battery_power`,`rated_capacity_kwh`,`operating_status`,`temp`, `mainboardid`  FROM energy_storage_cabin_table WHERE `mainboardid` = "0.0.0.0" 
2025-07-15_04:48:13 INFO  OnMainBoardRegister mainboardId:[0.0.0.0]  success
2025-07-15_04:48:13 DEBUG [1 ms] SQL:  SELECT `id`,`‌battery_power`,`rated_capacity_kwh`,`operating_status`,`temp`, `mainboardid`  FROM energy_storage_cabin_table WHERE `mainboardid` = "0.0.0.0" 
