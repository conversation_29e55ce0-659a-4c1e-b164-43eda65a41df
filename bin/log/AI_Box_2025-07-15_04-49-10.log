2025-07-15_04:49:10 INFO  Starting Logonserver
2025-07-15_04:49:10 INFO  Host:[0.0.0.0] Port:[8600] ThreadCount:[4]
2025-07-15_04:49:10 INFO  Automatic database updates are disabled for all databases!
2025-07-15_04:49:10 INFO  Opening DatabasePool 'ai_box'. Asynchronous connections: 1, synchronous connections: 1.
2025-07-15_04:49:10 INFO  MySQL client library: 8.0.42
2025-07-15_04:49:10 INFO  MySQL server ver: 8.0.42-0ubuntu0.24.04.1 
2025-07-15_04:49:10 INFO  Connected to MySQL database at 127.0.0.1
2025-07-15_04:49:10 INFO  MySQL client library: 8.0.42
2025-07-15_04:49:10 INFO  MySQL server ver: 8.0.42-0ubuntu0.24.04.1 
2025-07-15_04:49:10 INF<PERSON>  Connected to MySQL database at 127.0.0.1
2025-07-15_04:49:10 INFO  DatabasePool 'ai_box' opened successfully. 2 total connections running.
2025-07-15_04:49:10 INFO  Service started successfully
2025-07-15_04:49:10 INFO  Client connected: 0.0.0.0, Socket ID: 0
2025-07-15_04:49:11 INFO  OnMainBoardRegister mainboardId:[0.0.0.0]  success
2025-07-15_04:49:11 DEBUG [1 ms] SQL:  SELECT `id`,`‌battery_power`,`rated_capacity_kwh`,`operating_status`,`temp`, `mainboardid`  FROM energy_storage_cabin_table WHERE `mainboardid` = "0.0.0.0" 
2025-07-15_04:49:13 INFO  OnMainBoardRegister mainboardId:[0.0.0.0]  success
2025-07-15_04:49:13 DEBUG [16 ms] SQL:  SELECT `id`,`‌battery_power`,`rated_capacity_kwh`,`operating_status`,`temp`, `mainboardid`  FROM energy_storage_cabin_table WHERE `mainboardid` = "0.0.0.0" 
2025-07-15_04:49:15 INFO  OnMainBoardRegister mainboardId:[0.0.0.0]  success
2025-07-15_04:49:15 DEBUG [0 ms] SQL:  SELECT `id`,`‌battery_power`,`rated_capacity_kwh`,`operating_status`,`temp`, `mainboardid`  FROM energy_storage_cabin_table WHERE `mainboardid` = "0.0.0.0" 
2025-07-15_04:49:17 INFO  OnMainBoardRegister mainboardId:[0.0.0.0]  success
2025-07-15_04:49:17 DEBUG [0 ms] SQL:  SELECT `id`,`‌battery_power`,`rated_capacity_kwh`,`operating_status`,`temp`, `mainboardid`  FROM energy_storage_cabin_table WHERE `mainboardid` = "0.0.0.0" 
