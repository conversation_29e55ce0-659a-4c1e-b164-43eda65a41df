2025-07-14_06:39:48 INFO  Starting Logonserver
2025-07-14_06:39:48 INFO  Host:[0.0.0.0] Port:[8600] ThreadCount:[4]
2025-07-14_06:39:48 INFO  Automatic database updates are disabled for all databases!
2025-07-14_06:39:48 INFO  Opening DatabasePool 'ai_box'. Asynchronous connections: 1, synchronous connections: 1.
2025-07-14_06:39:48 INFO  MySQL client library: 8.0.42
2025-07-14_06:39:48 INFO  MySQL server ver: 8.0.42-0ubuntu0.24.04.1 
2025-07-14_06:39:48 INFO  Connected to MySQL database at 127.0.0.1
2025-07-14_06:39:48 INFO  MySQL client library: 8.0.42
2025-07-14_06:39:48 INFO  MySQL server ver: 8.0.42-0ubuntu0.24.04.1 
2025-07-14_06:39:48 INF<PERSON>  Connected to MySQL database at 127.0.0.1
2025-07-14_06:39:48 INFO  DatabasePool 'ai_box' opened successfully. 2 total connections running.
2025-07-14_06:39:49 INFO  Correspond server connection failed [ 111 ], will reconnect in 5 seconds
2025-07-14_06:39:49 INFO  Service started successfully
2025-07-14_06:39:49 INFO  The connection to the correspond server is closed, and will reconnect in 5 seconds
2025-07-14_06:40:02 INFO  Client connected: *************, Socket ID: 0
2025-07-14_06:40:02 INFO  OnMainBoardRegister mainboardId:[*************]  success
2025-07-14_06:40:02 INFO  SQL: SELECT `id`,`battery_power`,`rated_capacity_kwh`,`operating_status`,`temp`, `mainboard_ip`  FROM energy_storage_cabin_table WHERE `mainboard_ip` = ************* 
2025-07-14_06:40:02 ERROR [1064] You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near '.1.101' at line 1
2025-07-14_06:40:02 ERROR Error while parsing SQL. Core fix required.
