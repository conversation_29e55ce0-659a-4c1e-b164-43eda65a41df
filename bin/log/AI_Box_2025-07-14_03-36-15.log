2025-07-14_03:36:15 INFO  Starting Logonserver
2025-07-14_03:36:15 INFO  Host:[0.0.0.0] Port:[8600] ThreadCount:[4]
2025-07-14_03:36:15 INFO  Automatic database updates are disabled for all databases!
2025-07-14_03:36:15 INFO  Opening DatabasePool 'ai_box'. Asynchronous connections: 1, synchronous connections: 1.
2025-07-14_03:36:15 INFO  MySQL client library: 8.0.42
2025-07-14_03:36:15 INFO  MySQL server ver: 8.0.42-0ubuntu0.24.04.1 
2025-07-14_03:36:15 INFO  Connected to MySQL database at 127.0.0.1
2025-07-14_03:36:15 INFO  MySQL client library: 8.0.42
2025-07-14_03:36:15 INFO  MySQL server ver: 8.0.42-0ubuntu0.24.04.1 
2025-07-14_03:36:15 <PERSON><PERSON><PERSON>  Connected to MySQL database at 127.0.0.1
2025-07-14_03:36:15 INFO  DatabasePool 'ai_box' opened successfully. 2 total connections running.
2025-07-14_03:36:15 INFO  Correspond server connection failed [ 111 ], will reconnect in 5 seconds
2025-07-14_03:36:15 INFO  Service started successfully
2025-07-14_03:36:15 INFO  The connection to the correspond server is closed, and will reconnect in 5 seconds
2025-07-14_03:36:16 DEBUG [56 ms] SQL: delete from alarm_log_table where `date` < 1749872176 ;
2025-07-14_04:48:56 INFO  Client connected: 192.168.1.28, Socket ID: 0
2025-07-14_04:48:56 INFO  OnClientRegister name:[admin] pwd:[123456] success
2025-07-14_04:48:56 DEBUG [124 ms] SQL: select * from client_table where `name` = 'admin' and `pwd` = '123456' 
2025-07-14_05:02:48 INFO  Client connected: 192.168.1.101, Socket ID: 1
2025-07-14_05:08:00 INFO  Client connected: 192.168.1.101, Socket ID: 1
2025-07-14_05:12:19 INFO  Client connected: 192.168.1.101, Socket ID: 1
2025-07-14_05:16:36 INFO  Client connected: 192.168.1.101, Socket ID: 1
2025-07-14_05:20:54 INFO  Client connected: 192.168.1.101, Socket ID: 1
2025-07-14_05:25:12 INFO  Client connected: 192.168.1.101, Socket ID: 1
2025-07-14_05:29:30 INFO  Client connected: 192.168.1.101, Socket ID: 1
