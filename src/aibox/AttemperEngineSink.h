#ifndef ATTEMPER_ENGINE_SINK_H
#define ATTEMPER_ENGINE_SINK_H

#include "AIBoxListManager.h"
#include "DBExports.h"
#include "EnergyStorageCabinManager.h"
#include "Header.h"
#include "KernelEngineHead.h"
#include "RoomListManager.h"
#include <map>

#define Data_Size 1024 * 1024 * 2 // 30m

struct MsgInfo {
    int count = 0; // �Խ��ܵ�����
    int id = 0;    // �ϸ���Ϣ��ID
    char msg[Data_Size];
    MsgInfo() { memset(msg, 0, sizeof(msg)); }
};

namespace AiBox {

struct UserInfo {
    std::string name;
    std::string pwd;
    uint32 dwSocketID;
    UserInfo() { dwSocketID = 0; }
};

typedef std::shared_ptr<UserInfo> UserInfoPtr;

// �󶨲���
struct tagBindParameter {
    // �������
    uint64 dwSocketID;   // �����ʶ
    uint64 dwClientAddr; // ���ӵ�ַ
    uint8 cbClientKind;  // ��������
};

enum LinkType {
    LT_FLASH = 1,    // ��ҳ����
    LT_MOBILE = 2,   // �ֻ�����
    LT_COMPUTER = 3, // ��������
};

class CAttemperEngineSink : public IAttemperEngineSink {
    friend class ServiceUnits;

  public:
    CAttemperEngineSink();
    virtual ~CAttemperEngineSink();

    virtual void Release();
    virtual void *QueryInterface(GGUID uuid);

    // �첽�ӿ�
  public:
    // �����¼�
    virtual bool OnAttemperEngineStart(IUnknownEx *pIUnknownEx);
    // ֹͣ�¼�
    virtual bool OnAttemperEngineConclude(IUnknownEx *pIUnknownEx);

    // �����¼�
  public:
    // �����¼�
    virtual bool OnEventTCPSocketLink(uint16 wServiceID, int iErrorCode);
    // �ر��¼�
    virtual bool OnEventTCPSocketShut(uint16 wServiceID, uint8 cbShutReason);
    // ��ȡ�¼�
    virtual bool OnEventTCPSocketRead(uint16 wServiceID, TCP_Command Command, void *pData, uint16 wDataSize);

    // �����¼�
  public:
    // Ӧ���¼�
    virtual bool OnEventTCPNetworkBind(uint32 dwClientAddr, uint32 dwSocketID);
    // �ر��¼�
    virtual bool OnEventTCPNetworkShut(uint32 dwClientAddr, uint32 dwSocketID);
    // ��ȡ�¼�
    virtual bool OnEventTCPNetworkRead(Net::TCP_Command Command, void *pData, uint16 wDataSize, uint32 dwSocketID);

    // �ӿ��¼�
  public:
    // �����¼�
    virtual bool OnEventControl(uint16 wControlID, void *pData, uint16 wDataSize);

    // �ں��¼�
  public:
    // ʱ���¼�
    virtual bool OnEventTimer(uint32 dwTimerID);

    // ���Ӵ���
  protected:
    // ע���¼�
    bool OnTCPSocketMainRegister(uint16 wSubCmdID, void *pData, uint16 wDataSize);
    // �б��¼�
    bool OnTCPSocketMainServiceInfo(uint16 wSubCmdID, void *pData, uint16 wDataSize);

    // �ֻ��¼�
  protected:
    // ��¼����
    bool OnTCPNetworkMainMBLogon(uint16 wSubCmdID, void *pData, uint16 wDataSize, uint32 dwSocketID);

  protected:
    // �ο͵�¼
    bool OnTCPNetworkSubMBLogonVisitor(void *pData, uint16 wDataSize, uint32 dwSocketID);

    // �ֻ��б�
  protected:
    // ���ͷ���
    void SendKindListInfo(uint32 dwSocketID);
    // ���ͷ���
    void SendRoomListInfo(uint32 dwSocketID, uint16 wKindID);

  protected:
    // ��½ʧ��
    bool OnLogonFailure(uint32 dwSocketID, LogonErrorCode &lec);

    // ����ͷ�¼�
  protected:
    void onCameraStatusChange();
    // ������Ϣ
    bool OnTCPNetworkMainCSImage(uint16 wSubCmdID, void *pData, uint16 wDataSize, uint32 dwSocketID);
    bool OnTCPNetworkSubTempWarn(void *pData, uint16 wDataSize, uint32 dwSocketID);
    bool OnTCPNetworkSubTempInfo(void *pData, uint16 wDataSize, uint32 dwSocketID);
    bool OnTCPNetworkSubCSImage(void *pData, uint16 wDataSize, uint32 dwSocketID);
    bool saveImageToFile(const std::vector<unsigned char> &imgbuffer, const std::string &filename);
    void onUpdatesDeepimglib(void *pData, uint16 wDataSize);

    // ��Ե��ע��
    bool OnBoxSubEvent(uint16 wSubCmdID, void *pData, uint16 wDataSize, uint32 dwSocketID);
    bool OnBoxRegister(void *pData, uint16 wDataSize, uint32 dwSocketID);
    bool OnBoxTempEvent(uint16 wSubCmdID, void *pData, uint16 wDataSize, uint32 dwSocketID);

    // ��������ͷ�㷨����
    void SendCameraAglConfig(uint32 dwSocketID, uint32 boxId, std::string &sCameraList);

    void InitEnergyStorageCabin(uint32 cabinId);

    void UpdateEnergyStorageCabinTemp(uint32 uCameraID, float fTemp);

  protected:
    bool OnClientMainEvent(uint16 wSubCmdID, void *pData, uint16 wDataSize, uint32 dwSocketID);
    bool OnClientRegister(void *pData, uint16 wDataSize, uint32 dwSocketID);
    void NoticeClientWarning( uint32 nCameraid,float fHightemp,long long llTime, uint8_t uWarnType, const std::string &sImgpath);
    void NoticeClientTemp( uint32 nCameraid,float fTemp);
    bool OnMainBoarMainEvent(uint16 wSubCmdID, void *pData, uint16 wDataSize, uint32 dwSocketID);

    private : tagBindParameter *m_pBindParameter; // ��������

    // ����ӿ�
  protected:
    ITimerEngine *m_pITimerEngine;           // ʱ������
    ITCPNetworkEngine *m_pITCPNetworkEngine; // ��������
    ITCPSocketService *m_pITCPSocketService;

    // �������
  protected:
    CRoomListManager m_RoomListManager; // �б�����
    tagBindParameter m_ClientInfo;      // �󶨲���
    // std::array<uint8_t, 1024 * 1024 * 30> dataArr;
    int data = 0;
    std::mutex m_mapMut;
    std::map<uint64_t, std::shared_ptr<MsgInfo>> m_msgPool;

    // ��Ե�����
    CAIBoxListManager m_BoxListManager;
    // ���ܲ�
    CEnergyStorageCabinManager m_EnergyStorageCabinManager;

    std::map<uint32, UserInfoPtr> m_UserInfoMap;
    //

    // char img[Data_Size];
};
} // namespace AiBox

#endif