#ifndef ATTEMPER_ENGINE_SINK_H
#define ATTEMPER_ENGINE_SINK_H

#include "AIBoxListManager.h"
#include "DBExports.h"
#include "EnergyStorageCabinManager.h"
#include "Header.h"
#include "KernelEngineHead.h"
#include "RoomListManager.h"
#include <map>

#define Data_Size 1024 * 1024 * 2 // 30m

struct MsgInfo
{
    int count = 0; // ??????????
    int id = 0;    // ????????ID
    char msg[Data_Size];
    MsgInfo()
    {
        memset(msg, 0, sizeof(msg));
    }
};

namespace AiBox
{

struct UserInfo
{
    std::string name;
    std::string pwd;
    uint32 dwSocketID;
    UserInfo()
    {
        dwSocketID = 0;
    }
};

typedef std::shared_ptr<UserInfo> UserInfoPtr;

// ?????
struct tagBindParameter
{
    // ???????
    uint64 dwSocketID;   // ??????
    uint64 dwClientAddr; // ??????
    uint8 cbClientKind;  // ????????
};

enum LinkType
{
    LT_FLASH = 1,    // ???????
    LT_MOBILE = 2,   // ???????
    LT_COMPUTER = 3, // ????????
};

class CAttemperEngineSink : public IAttemperEngineSink
{
    friend class ServiceUnits;

  public:
    CAttemperEngineSink();
    virtual ~CAttemperEngineSink();

    virtual void Release();
    virtual void *QueryInterface(GGUID uuid);

    // ?????
  public:
    // ???????
    virtual bool OnAttemperEngineStart(IUnknownEx *pIUnknownEx);
    // ?????
    virtual bool OnAttemperEngineConclude(IUnknownEx *pIUnknownEx);

    // ???????
  public:
    // ???????
    virtual bool OnEventTCPSocketLink(uint16 wServiceID, int iErrorCode);
    // ??????
    virtual bool OnEventTCPSocketShut(uint16 wServiceID, uint8 cbShutReason);
    // ??????
    virtual bool OnEventTCPSocketRead(uint16 wServiceID, TCP_Command Command, void *pData, uint16 wDataSize);

    // ???????
  public:
    // ??????
    virtual bool OnEventTCPNetworkBind(uint32 dwClientAddr, uint32 dwSocketID);
    // ??????
    virtual bool OnEventTCPNetworkShut(uint32 dwClientAddr, uint32 dwSocketID);
    // ??????
    virtual bool OnEventTCPNetworkRead(Net::TCP_Command Command, void *pData, uint16 wDataSize, uint32 dwSocketID);

    // ??????
  public:
    // ???????
    virtual bool OnEventControl(uint16 wControlID, void *pData, uint16 wDataSize);

    // ??????
  public:
    // ??????
    virtual bool OnEventTimer(uint32 dwTimerID);

    // ???????
  protected:
    // ??????
    bool OnTCPSocketMainRegister(uint16 wSubCmdID, void *pData, uint16 wDataSize);
    // ?��????
    bool OnTCPSocketMainServiceInfo(uint16 wSubCmdID, void *pData, uint16 wDataSize);

    // ??????
  protected:
    // ???????
    bool OnTCPNetworkMainMBLogon(uint16 wSubCmdID, void *pData, uint16 wDataSize, uint32 dwSocketID);

  protected:
    // ?��???
    bool OnTCPNetworkSubMBLogonVisitor(void *pData, uint16 wDataSize, uint32 dwSocketID);

    // ????��?
  protected:
    // ???????
    void SendKindListInfo(uint32 dwSocketID);
    // ???????
    void SendRoomListInfo(uint32 dwSocketID, uint16 wKindID);

  protected:
    // ??????
    bool OnLogonFailure(uint32 dwSocketID, LogonErrorCode &lec);

    // ????????
  protected:
    void onCameraStatusChange();
    // ???????
    bool OnTCPNetworkMainCSImage(uint16 wSubCmdID, void *pData, uint16 wDataSize, uint32 dwSocketID);
    bool OnTCPNetworkSubTempWarn(void *pData, uint16 wDataSize, uint32 dwSocketID);
    bool OnTCPNetworkSubTempInfo(void *pData, uint16 wDataSize, uint32 dwSocketID);
    bool OnTCPNetworkSubCSImage(void *pData, uint16 wDataSize, uint32 dwSocketID);
    bool saveImageToFile(const std::vector<unsigned char> &imgbuffer, const std::string &filename);
    void onUpdatesDeepimglib(void *pData, uint16 wDataSize);

    // ????????
    bool OnBoxSubEvent(uint16 wSubCmdID, void *pData, uint16 wDataSize, uint32 dwSocketID);
    bool OnBoxRegister(void *pData, uint16 wDataSize, uint32 dwSocketID);
    bool OnBoxTempEvent(uint16 wSubCmdID, void *pData, uint16 wDataSize, uint32 dwSocketID);

    // ???????????????
    void SendCameraAglConfig(uint32 dwSocketID, uint32 boxId, std::string &sCameraList);

    void InitEnergyStorageCabin(uint32 cabinId);

    void UpdateEnergyStorageCabinTemp(uint32 uCameraID, float fTemp);


    //客户端
  protected:
    bool OnClientMainEvent(uint16 wSubCmdID, void *pData, uint16 wDataSize, uint32 dwSocketID);
    bool OnClientRegister(void *pData, uint16 wDataSize, uint32 dwSocketID);
    void NoticeClientWarning(uint32 nCameraid, float fHightemp, long long llTime, uint8_t uWarnType, const std::string &sImgpath);
    void NoticeClientTemp(uint32 nCameraid, float fTemp);
    bool OnClientQueryMsg(void *pData, uint16 wDataSize, uint32 dwSocketID);
    bool OnClientDevOpen(void *pData, uint16 wDataSize, uint32 dwSocketID);
    bool OnClientCabinSta(void *pData, uint16 wDataSize, uint32 dwSocketID);
    
    // 主板
    protected : 
    bool OnMainBoarMainEvent(uint16 wSubCmdID, void *pData, uint16 wDataSize, uint32 dwSocketID);
    bool OnMainBoardRegister(void *pData, uint16 wDataSize, uint32 dwSocketID);
    bool OnMainBoardStatus(void *pData, uint16 wDataSize, uint32 dwSocketID);
    bool OnMainBoardDevOptResq(void *pData, uint16 wDataSize, uint32 dwSocketID);
    void InitEnergyStorageCabinByMainBoardId(const std::string& mainboardIp);
    bool OnMainBoardDevOnlineResq(void *pData, uint16 wDataSize, uint32 dwSocketID);

    private : tagBindParameter *m_pBindParameter; //

    //
  protected:
    Net::ITimerEngine *m_pITimerEngine;           //
    Net::ITCPNetworkEngine *m_pITCPNetworkEngine; //
    Net::ITCPSocketService *m_pITCPSocketService;

    //
  protected:
    CRoomListManager m_RoomListManager; //
    tagBindParameter m_ClientInfo;      //
    // std::array<uint8_t, 1024 * 1024 * 30> dataArr;
    bool m_nOpt = false;
    int data = 0;
    std::mutex m_mapMut;
    std::map<uint64_t, std::shared_ptr<MsgInfo>> m_msgPool;

    //
    CAIBoxListManager m_BoxListManager;
    //
    CEnergyStorageCabinManager m_EnergyStorageCabinManager;

    std::map<uint32, UserInfoPtr> m_UserInfoMap;

    //指令队列
    

    // char img[Data_Size];
};
} // namespace AiBox

#endif