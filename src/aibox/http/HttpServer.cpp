﻿#include "HttpServer.h"

#include "httplib.h"


#include <assert.h>
#include <cassert>

#include "CHandler.h"
#include "CGlobal.h"
#include "CServerGlobal.h"

static constexpr int _http_port = 18850;

// protocol://hostname[:port]/path/[:parameters][?query]#fragment

HttpServer::HttpServer()
{
   
    XGlobal;//主线程初始化
    XGlobal_Get(CAbstractServer, pServer, Server);
    m_serverPtr = pServer;
    // m_serverPtr = std::make_shared<CServerGlobal>();
    if(m_serverPtr) {
        m_serverPtr->createServer();
    }
    
}

HttpServer::~HttpServer()
{
    
  this->stop();
}

bool HttpServer::startServer()
{
  this->m_serverThread = new std::thread(&HttpServer::run, this);
    // this->start();
    return true;
}

void HttpServer::stop()
{
   this->m_serverPtr->stopServer();
   this->m_serverThread->join();
   if(this->m_serverThread) {
       delete this->m_serverThread;
       this->m_serverThread = nullptr;
   }
   this->m_serverPtr->releaseServer();
}

void HttpServer::start()
{
   
}

void HttpServer::run()
{
//    stop();
//    m_serverPtr->releaseServer();

    const std::string host = "0.0.0.0";

   

    if(host.empty()) {
        std::cerr << u8"http host is null" << std::endl;
        return;
    }

    //注册各类服务
    CHandler::registeFunc(m_serverPtr);
    if(!m_serverPtr->getServer()->listen(host, _http_port))
    {
        std::cerr << u8"http server start failed ..." << std::endl;
    }
}
