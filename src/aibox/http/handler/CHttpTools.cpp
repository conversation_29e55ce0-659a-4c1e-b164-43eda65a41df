﻿#include "CHttpTools.h"

#include <string>
#include <memory>
#include "fmt/printf.h"
// #include <QString>
// #include <QSqlError>
// #include <QtDebug>
#include <string>
#include "../../data/CParam.h"
#include "CAbstractServer.h"
#include "CGlobal.h"
// #include "CPublisher.h"
// #include "CNotifyChangedItem.h"

// #include "LogMacro.h"

std::shared_ptr<CAbstractServer> CHttpTools::getServer()
{
    XGlobal_Get(CAbstractServer, pServer, Server);
    return pServer;
}

bool CHttpTools::toBool(const std::string &val)
{
    // std::string str = QString::fromStdString(val).trimmed();
    // if(str == "1" || str.toUpper() == "TRUE")
    //     return true;
    // else if(str == "0" || str.toUpper() == "FALSE")
    //     return false;

    // clog_debug << "=>to bool failed:" << qPrintable(str);
    return false;
}

int32_t CHttpTools::toInt32(const std::string &val)
{
    // return QString::fromStdString(val).toInt();
    return 0;
}

int64_t CHttpTools::toInt64(const std::string &val)
{
    // return QString::fromStdString(val).toLongLong();
    return 0;
}

float CHttpTools::toFloat(const std::string &val)
{
    // return QString::fromStdString(val).toFloat();
    return 0.0f;
}

double CHttpTools::toDouble(const std::string &val)
{
    // return QString::fromStdString(val).toDouble();
    return 0.0;
}

bool CHttpTools::checkSession(const httplib::Request& req, httplib::Response& res)
{
    auto pServer = CHttpTools::getServer();
    if(!pServer) {
        std::cout << "==>get server failed." << std::endl;
        return false;
    }

    std::string session;
    if(req.has_header(HttpParam::session))
    {
        session = req.get_header_value(HttpParam::session);
    }

#if 1
    if(!pServer->checkSession(session))
    {
        res.status = httplib::StatusCode::OK_200;
        res.set_content(CHttpTools::json(false, u8"session 验证失败."),
                        HttpContentType::json);
        std::cout << "==>get server failed22222." << std::endl;
        return false;
    }
#endif
    return true;
}

std::string CHttpTools::JsonToString(const Json::Value& json) {
  Json::StreamWriterBuilder builder;
  return Json::writeString(builder, json);
}

std::string CHttpTools::json(int code, const std::string& data, const std::string& msg)

{
  Json::Value ret;
  ret["code"]=code;
  ret["data"]=data;
  ret["msg"]=msg;
    return JsonToString(ret);

}

std::string CHttpTools::jsonObj(int code, const std::string& obj, const std::string& msg)
{
    // std::cout << "==>jsonObj222222222222222:" << obj << std::endl;
    std::string str = fmt::format(R"({{"code": {}, "data": {}, "msg": "{}"}})", code, obj, msg);
    
    // std::cout << "==>jsonObj:" << str << std::endl;
    return str;
//   return ret.toStyledString();
}

bool CHttpTools::readParam(const httplib::Request &req,
                           const std::string &key,
                           COptional<std::string> &val)
{
    if(req.has_param(key)) {
        val = req.get_param_value(key);
        return true;
    }
    return false;
}

bool CHttpTools::readFile(const httplib::Request &req,
                          const std::string &key,
                          COptional<std::string> &val)
{
    if(req.has_file(key)) {
        auto data = req.get_file_value(key);
        val = req.get_file_value(key).content;
        return true;
    }
    return false;
}

void CHttpTools::notifyFuncChanged()
{
    // XGlobal_Get(CPublisher, pusher, Publisher);
    // if(pusher) {
    //     auto pItem = std::make_shared<CNotifyAlgorithmChangedItem>();
    //     pusher->notify(pItem);
    // }
}

// void CHttpTools::notifyAddSource(int sourceId,
//                                  const QString &stream_url)
// {
//     // std::cout<<"notifyAddSource111111-"<<std::endl;
//     // XGlobal_Get(CPublisher, pusher, Publisher);
//     // if(pusher) {
//     //     auto pItem = std::make_shared<CNotifyAddSourceItem>();
//     //     pItem->setSourceId(sourceId);
//     //     pItem->setStreamUrl(stream_url);
//     //     std::cout<<"notifyAddSource22222-"<<std::endl;
//     //     pusher->notify(pItem);
//     // }
// }

// void CHttpTools::notifyModifySource(int sourceId,
//                                     const QString &stream_url)
// {
//     XGlobal_Get(CPublisher, pusher, Publisher);
//     if(pusher) {
//         auto pItem = std::make_shared<CNotifyModifySourceItem>();
//         pItem->setSourceId(sourceId);
//         pItem->setStreamUrl(stream_url);
//         pusher->notify(pItem);
//     }
// }

// void CHttpTools::notifyRemoveSource(int sourceId)
// {
//     XGlobal_Get(CPublisher, pusher, Publisher);
//     if(pusher) {
//         auto pItem = std::make_shared<CNotifyRemoveSourceItem>();
//         pItem->setSourceId(sourceId);
//         pusher->notify(pItem);
//     }
// }

