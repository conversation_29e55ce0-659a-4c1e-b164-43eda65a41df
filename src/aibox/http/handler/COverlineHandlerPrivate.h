﻿#ifndef _COVERLINEHANDLERPRIVATEH
#define _COVERLINEHANDLERPRIVATEH


#include "httplib.h"

//跨线计数
class COverlineHandlerPrivate
{
public:
    static void query(const httplib::Request &req, httplib::Response &res);
    static void zero(const httplib::Request &req, httplib::Response &res);//
    static void autoZero(const httplib::Request &req, httplib::Response &res);//
};


#endif /* _COVERLINEHANDLERPRIVATEH */

