﻿#include "COverlineHandlerPrivate.h"

// #include <QtDebug>
// #include <QSqlError>
// #include <QSqlRecord>
// #include <QDateTime>
// #include <QFile>
#include "CGlobal.h"
#include "CDirManager.h"
#include "../../db/DBExports.h"
#include "../../util/StringFormat.h"
// #include "CDBHelper.h"
#include "CHttpTools.h"
// #include "CIniConf.h"
#include "CParam.h"
#include "../../util/Timer.h"

void COverlineHandlerPrivate::query(const httplib::Request &req, httplib::Response &res)
{
    if(!CHttpTools::checkSession(req, res))
        return;

    Req_Read_File(page);     //第几页(如果从第一页开始算，需要-1)
    Req_Read_File(pageCount);//一页多少条

    if(!page.valid() || !pageCount.valid()) {
        res.set_content(CHttpTools::json(false, u8"缺少参数"), HttpContentType::json);
        return;
    }


    


    Req_Read_File(camera_id);
    Req_Read_File(ip);
    Req_Read_File(begin_time);
    Req_Read_File(end_time);

    std::string condition;
    
    if(camera_id.valid() && !camera_id.value().empty()) {
        condition += fmt::format(" and a.camera_id={}", camera_id.value());
        
    }

    if(ip.valid() && !ip.value().empty()) {
        condition += fmt::format(" and b.ip like '%{}%' ", ip.value());
       
    }

    if(begin_time.valid() && end_time.valid() &&
            !begin_time.value().empty() &&
            !end_time.value().empty()) {
        
        condition+= fmt::format(" and a.dtime BETWEEN {} AND {} ", std::stoll(begin_time.value()), std::stoll(end_time.value()));
    }
    else if(begin_time.valid() && !begin_time.value().empty()) {
        
        condition += fmt::format(" and a.dtime>=?", std::stoll(begin_time.value()));
    }
    else if(end_time.valid() && !end_time.value().empty()) {
        condition += fmt::format(" and a.dtime<=? ", std::stoll(end_time.value()));
    }


    if(1)
    {

        

        std::string sub =
            std::string("select a.id,b.name,b.ip,a.creat_time,a.agl_id,c.direction,"
                        "a.image,sum(a.count) "
                        "from overline_details_log_table as a "
                        "left join overline_log_table as c on a.camera_id=c.camera_id and a.line_id = c.id and "
                        "a.agl_id = c.type  left join   input_source_table as b on a.camera_id=b.id "
                        "where b.enable=1 ");
        if(condition.size() >0) {
            sub += condition;
        }
        sub += " group by b.name,a.agl_id,c.direction ";
        // 查询总记录
        // select count(*) from ( ? ) as subquery
        std::string sql = fmt::format("select count(*) from ( {} ) as subquery ", sub);
        DB::QueryResult query = DB::LogonDatabasePool.Query(sql.c_str());

        
        int totalCount = 0;
        if(query) {
            auto field = query->Fetch();
            totalCount = field[0].GetInt32();

        }

       
        if(totalCount < 1) {
            res.set_content(CHttpTools::jsonObj(true, "[]"), HttpContentType::json);
            return;
        }

       

        const int totalPage = std::ceil(totalCount * 1.0 / std::stoi(pageCount.value()));

        int offset = std::stoi(pageCount.value()) * (std::stoi(page.value()) - 1);
        if(offset<0){
            offset = 0;
        }
        std::string strLimit = fmt::format(" LIMIT {0} OFFSET {1} ;", std::stoi(pageCount.value()), offset);

        std::string sql1 = sub + strLimit;
        DB::QueryResult query1 = DB::LogonDatabasePool.Query(sql1.c_str());
        if(!query1) {
            res.set_content(CHttpTools::json(false, u8"查询失败"), HttpContentType::json);
            return;
        }else
        {
            Json::Value json;
            do
            {
                Json::Value value;
                auto field = query1->Fetch();
                value["id"] = field[0].GetInt32();
                value["name"] = field[1].GetString();
                value["ip"] = field[2].GetString();
                value["dtime"] = field[3].GetInt32();
                value["type"] = field[4].GetInt32();
                value["direction"] = field[5].GetInt32();
                value["image"] = field[6].GetString();
                value["count"] = field[7].GetInt32();

                json.append(value);

            } while (query1->NextRow());

            
            Json::Value jsonObj;
            jsonObj["page"] = std::stoi(page.value());
            jsonObj["totalPage"] = totalPage;
            jsonObj["count"] = totalCount;
            jsonObj["list"] = json;
            std::string ret = jsonObj.toStyledString();
            res.set_content(CHttpTools::jsonObj(true, ret), HttpContentType::json);
        }
        res.status = httplib::StatusCode::OK_200;
    }
}

void COverlineHandlerPrivate::zero(const httplib::Request &req, httplib::Response &res)
{
    if(!CHttpTools::checkSession(req, res))
        return;

    Req_Read_File(camera_id);
    if(!camera_id.valid() || camera_id.value().empty()) {
        res.set_content(CHttpTools::json(false, u8"缺少参数"), HttpContentType::json);
        return;
    }

    DB::PreparedStatement *stmt =
        DB::LogonDatabasePool.GetPreparedStatement(AI_BOX_OVERLINE_LOG_ZERO);
    auto startTime = DB::getCurrentTimeSec();
    stmt->SetInt64(0, startTime);
    stmt->SetString(1, camera_id.value());
    DB::LogonDatabasePool.Query(stmt);


    res.set_content(CHttpTools::json(true), HttpContentType::json);
    res.status = httplib::StatusCode::OK_200;
}

void COverlineHandlerPrivate::autoZero(const httplib::Request &req, httplib::Response &res)
{
    // if(!CHttpTools::checkSession(req, res))
    //     return;

    // Req_Read_File(state);   //开关状态
    // Req_Read_File(strategy);//策略  每周
    // Req_Read_File(index);   //每周星期几  每月几号
    // Req_Read_File(time);    //清除时间

    // //1.写入配置，
    // XGlobal_Get(CDirManager, pDir, Dir);
    // CIniConf ini(pDir->getPath(CDirManager::ConfigPath), "auto_zero_info.ini");
    // if(ini.open())
    // {
    //     if(state.valid() && !state.value().empty())
    //         ini.set("state" , std::stoi(state.value()) );

    //     if(strategy.valid() && !strategy.value().empty())
    //         ini.set("strategy" , std::stoi(strategy.value()) );

    //     if(index.valid() && !index.value().empty())
    //         ini.set("index" , std::stoi(index.value()) );

    //     if(time.valid() && !time.value().empty())
    //         ini.set("time" , std::stoi(time.value()) );

    //     ini.sync();
    //     ini.close();
    // }

    // res.set_content(CHttpTools::json(true), HttpContentType::json);
    // res.status = httplib::StatusCode::OK_200;

    //2.通知策略更新
}
