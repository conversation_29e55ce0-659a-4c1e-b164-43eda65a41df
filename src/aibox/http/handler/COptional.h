
#ifndef COPTIONAL_H_
#define COPTIONAL_H_

#include <utility>

template <class T>
struct COptional
{
    COptional()
        : mValid(false),
          mValue(T())
    {}

    explicit COptional(const T &valueIn)
        : mValid(true),
          mValue(valueIn)
    {}

    COptional(const COptional &other)
        : mValid(other.mValid),
          mValue(other.mValue)
    {}

    COptional &operator=(const COptional &other)
    {
        this->mValid = other.mValid;
        this->mValue = other.mValue;
        return *this;
    }

    COptional &operator=(const T &value)
    {
        mValue = value;
        mValid = true;
        return *this;
    }

    COptional &operator=(T &&value)
    {
        mValue = std::move(value);
        mValid = true;
        return *this;
    }

    void reset()
    {
        mValid = false;
    }

    operator bool() const {
        return mValid;
    }

    operator const T&() const {
        return mValue;
    }

    operator T&() {
        return mValue;
    }

    static COptional Invalid() { return COptional(); }

    bool valid() const { return mValid; }
    const T &value() const { return mValue; }
    T &value() { return mValue; }

    bool operator==(const COptional &other) const
    {
        return ((mValid == other.mValid) && (!mValid || (mValue == other.mValue)));
    }

    bool operator!=(const COptional &other) const
    {
        return !(*this == other);
    }

private:
    bool mValid;
    T mValue;
};

#endif // COPTIONAL_H_