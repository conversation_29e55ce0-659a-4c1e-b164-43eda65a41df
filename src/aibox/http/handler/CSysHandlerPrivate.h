﻿#ifndef _CSYSHANDLERPRIVATEH
#define _CSYSHANDLERPRIVATEH
#include "httplib.h"
class CSysHandlerPrivate
{
public:
    //1.运维系统
    //1.1设备管理->设备信息
    static void getDeviceInfo(const httplib::Request& req, httplib::Response& res);
    static void setDeviceInfo(const httplib::Request& req, httplib::Response& res);

    //1.2告警视频->告警视频时长
    static void getAlarmVideoLength(const httplib::Request& req, httplib::Response& res);
    static void setAlarmVideoLength(const httplib::Request& req, httplib::Response& res);

    //1.3系统资源 cpu使用情况/内存使用概括/存储空间使用情况
    static void getSysResources(const httplib::Request& req, httplib::Response& res);

    //1.4日志管理 下载日志
    static void downloadLog(const httplib::Request& req, httplib::Response& res);

    //1.5网络设置(todo)

    //1.6License管理(todo)

    //1.7系统时间
    static void getSysTime(const httplib::Request& req, httplib::Response& res);
    static void setSysTime(const httplib::Request& req, httplib::Response& res);


    //1.8安全设置(todo)


    //2.算法导入
    static void importAlgorithm(const httplib::Request& req, httplib::Response& res,
                                const httplib::ContentReader &content_reader);

    //3.设备升级
    static void deviceUpgrade(const httplib::Request& req, httplib::Response& res,
                              const httplib::ContentReader &content_reader);

    //4.上传商标，并设置标题 简介
    static void getCompanyInfo(const httplib::Request& req, httplib::Response& res);
    static void setCompanyInfo(const httplib::Request& req, httplib::Response& res,
                               const httplib::ContentReader &content_reader);

    //5.高级设置(todo)
};

#endif /* _CSYSHANDLERPRIVATEH */



