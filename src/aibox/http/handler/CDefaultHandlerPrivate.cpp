﻿#include "CDefaultHandlerPrivate.h"


#include "CAbstractServer.h"
#include "CHttpTools.h"
#include "../../data/CParam.h"


void CDefaultHandlerPrivate::logger(const httplib::Request& req,
                                    const httplib::Response& res)
{
    (void) req;
    (void) res;
    CHttpTools::getServer()->updateSessionValid();//刷新有效时长
};

void CDefaultHandlerPrivate::version(const httplib::Request& req, httplib::Response& res)
{
    // (void) req;
    // std::string ver = qApp->applicationVersion().toStdString();
    std::string ver = "1.0.0";
    const auto json = CHttpTools::json(true, ver);
    res.set_content(json, HttpContentType::json);
};

void CDefaultHandlerPrivate::stop(const httplib::Request& req, httplib::Response& res)
{
    (void) req;
    res.set_content(CHttpTools::json(true, u8"停止http服务."), HttpContentType::json);
    res.status = httplib::StatusCode::OK_200;
    CHttpTools::getServer()->stopServer();
};

void CDefaultHandlerPrivate::error(const httplib::Request &req, httplib::Response &res)
{
// #if 0
//     //res.set_content(CHttpTools::json(false, u8"handler error."), HttpContentType::json);
//     res.status = 200;
// #else
//     (void) req;
//     (void) res;
// #endif
};
