﻿#ifndef _CUSERHANDLERPRIVATEH
#define _CUSERHANDLERPRIVATEH

#include "httplib.h"


class CUserHandlerPrivate
{
public:
    static void login(const httplib::Request& req, httplib::Response& res);
    static void logout(const httplib::Request& req, httplib::Response& res);
    static void modifyPwd(const httplib::Request& req, httplib::Response& res);
    static void resetPwd(const httplib::Request& req, httplib::Response& res);
};


#endif /* _CUSERHANDLERPRIVATEH */

