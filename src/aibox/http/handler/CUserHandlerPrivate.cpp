﻿#include "CUserHandlerPrivate.h"

// #include <QtDebug>

#include "CAbstractServer.h"
#include "CTools.h"
#include "../../data/CParam.h"
#include "CHttpTools.h"


//login
void CUserHandlerPrivate::login(const httplib::Request& req, httplib::Response& res)
{
 
    COptional<std::string> val("");
    if (req.has_file("user_name")) {
        val = req.get_file_value("user_name").content;
        std::cout << "pppppppuser_name: " << val.value() << "  pwd:" << req.get_file_value("pwd").content << std::endl;
    }else{
        std::cout << "11111111111111111ser_name: " << val.value() << std::endl;
    }
    
    
    auto name1 = req.get_param_value("user_name");
    std::cout << "login start" << CHttpTools::readFile(req, "user_name", val) << std::endl;
    Req_Read_File(user_name);
    Req_Read_File(pwd);
    std::cout << "CUserHandlerPrivate::login" << " user_name:" << user_name.value() << " pwd:" << pwd.value()
              << "  name:" << name1 << std::endl;
    auto pServer = CHttpTools::getServer();

    std::string msg;
    bool b1 = pServer->login(user_name.value(),
                             pwd.value(),
                             msg);
    if(b1) {
        auto sesson = pServer->getSession();
        if(!pServer->checkSession(sesson)){
            auto str = CTools::randomString(16);
            auto session = std::string(str.begin(), str.end());
            pServer->updateSession(session);
            res.set_header(HttpParam::session, session);
            res.set_content(CHttpTools::json(b1, session), HttpContentType::json);
            std::cout<<"login: 33333"<<pServer->getSession()<<std::endl;
        }else{
            std::cout<<"login: 1122222"<<pServer->getSession()<<std::endl;
            res.set_header(HttpParam::session, pServer->getSession());
            res.set_content(CHttpTools::json(b1, pServer->getSession()), HttpContentType::json);
        }


    }
    else {
        std::cout<<"login error: "<<msg<<std::endl;
        res.set_content(CHttpTools::json(b1, msg), HttpContentType::json);
    }
    res.status = httplib::StatusCode::OK_200;

    std::cout<<"CUserHandlerPrivate::login end"<<std::endl;

}


//logout
void CUserHandlerPrivate::logout(const httplib::Request& req, httplib::Response& res)
{
    (void) req;
    CHttpTools::getServer()->clearSession();//清除session

    res.set_content(CHttpTools::json(true), HttpContentType::json);
    res.status = httplib::StatusCode::OK_200;
}

//
void CUserHandlerPrivate::modifyPwd(const httplib::Request& req, httplib::Response& res)
{
    Req_Read_File(user_name);
    Req_Read_File(old_pwd);
    Req_Read_File(new_pwd);

    std::string msg;
    bool b1 = CHttpTools::getServer()->modify(user_name.value(),
                                              old_pwd.value(),
                                              new_pwd.value(), msg);
    res.set_content(CHttpTools::json(b1, msg), HttpContentType::json);
    res.status = httplib::StatusCode::OK_200;
}


void CUserHandlerPrivate::resetPwd(const httplib::Request& req, httplib::Response& res)
{
    Req_Read_File(root_pwd);

    std::string msg;
    bool b1 = CHttpTools::getServer()->reset_pwd(root_pwd.value(), msg);
    res.set_content(CHttpTools::json(b1, msg), HttpContentType::json);
    res.status = httplib::StatusCode::OK_200;
}
