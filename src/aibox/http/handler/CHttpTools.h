﻿#ifndef _CHTTPTOOLSH
#define _CHTTPTOOLSH

#include <string>
#include <memory>
// #include <QString>
#include "COptional.h"
#include <json/json.h>
#include "httplib.h"

class CAbstractServer;

#define Read_File(req, key) \
    COptional<std::string> key; \
    CHttpTools::readFile(req, #key, key);

#define Req_Read_File(key)  Read_File(req, key);

#define Read_Param(req, key) \
    COptional<std::string> key; \
    CHttpTools::readParam(req, #key, key);

#define Req_Read_Param(key) Read_Param(req, key)

///
/// \brief http的一些常用的工具处理类
///
class CHttpTools
{
public:
    static std::shared_ptr<CAbstractServer> getServer();
    static std::string JsonToString(const Json::Value& json);

    static bool    toBool(const std::string &val);
    static int32_t toInt32(const std::string &val);
    static int64_t toInt64(const std::string &val);
    static float   toFloat(const std::string &val);
    static double  toDouble(const std::string &val);

    //检查session
    static bool checkSession(const httplib::Request& req,
                             httplib::Response& res);

    static std::string json(int code,
                            const std::string &data = std::string(),
                            const std::string &msg = std::string());

    static std::string jsonObj(int code,
                               const std::string &obj = std::string(),
                               const std::string &msg = std::string());

    //读取get参数
    static bool readParam(const httplib::Request &req,
                          const std::string &key,
                          COptional<std::string> &val);

    //读取post参数
    static bool readFile(const httplib::Request &req,
                         const std::string &key,
                         COptional<std::string> &val);

    //通知算法变化
    static void notifyFuncChanged();

    // static void notifyAddSource(int sourceId,
    //                             const std::string &stream_url);

    // static void notifyModifySource(int sourceId,
    //                                const std::string &stream_url);

    // static void notifyRemoveSource(int sourceId);

public:
    CHttpTools() = default;
    virtual ~CHttpTools() = default;
};


#endif /* _CHTTPTOOLSH */


