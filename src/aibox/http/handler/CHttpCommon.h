#ifndef CHTTPCOMMON_H
#define CHTTPCOMMON_H

#include <string>
struct OverLineInfo
{
    OverLineInfo() {}
    int id  = 0;
    int camera_id = 0;
    std::string camera_name ="";
    std::string line_name ="";
    int direction = 0;
    int algType = 0;
    std::string start_points_x= "";
    std::string start_points_y= "";
    std::string end_points_x= "";
    std::string end_points_y= "";
    std::string description= "";
    int enable = 1;
};


#endif // CHTTPCOMMON_H
