﻿#ifndef _CDEFAULTHANDLERPRIVATEH
#define _CDEFAULTHANDLERPRIVATEH

#include "httplib.h"

class CDefaultHandlerPrivate
{
public:
    static void logger(const httplib::Request& req, const httplib::Response& res);
    static void version(const httplib::Request& req, httplib::Response& res);
    static void stop(const httplib::Request& req, httplib::Response& res);
    static void error(const httplib::Request& req, httplib::Response& res);
};

#endif /* _CDEFAULTHANDLERPRIVATEH */



