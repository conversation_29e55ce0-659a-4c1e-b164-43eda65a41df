﻿#ifndef _CINPUTSOURCEHANDLERPRIVATEH
#define _CINPUTSOURCEHANDLERPRIVATEH


#include "httplib.h"
class CInputSourceHandlerPrivate
{
public:
    static void getList(const httplib::Request &req, httplib::Response &res); //获取视频列表
    static void query(const httplib::Request &req, httplib::Response &res);   //查询
    static void total(const httplib::Request &req, httplib::Response &res);   //数据统计
    static void enable(const httplib::Request &req, httplib::Response &res);  //启用/禁用
    static void remove(const httplib::Request &req, httplib::Response &res);  //删除

    static void add(const httplib::Request &req, httplib::Response &res);     //添加 -
    static void edit(const httplib::Request &req, httplib::Response &res);    //编辑 -
    static void batchDelete(const httplib::Request &req, httplib::Response &res);//批量删除


    //查询 通知 （内部使用）
    static void queryAndNotify();
    static bool queryParam(int id, std::string &param);



private:
    static bool getAlgConfigFileContent(int id, std::string &content);
    static void setAlgConfigFileContent(int id, const std::string &content);
    static void insertLines(int cameraId,const int& aglType,const std::string& cameraName,const std::string& lines);
};


#endif /* _CINPUTSOURCEHANDLERPRIVATEH */



