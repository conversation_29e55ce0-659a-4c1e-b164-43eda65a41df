﻿#include "CSysHandlerPrivate.h"

// #include <QDateTime>
// #include <QFile>
// #include <QtDebug>

// #include "json.hpp"
// #include "CTools.h"
#include <sstream>
#include "CHttpTools.h"
#include "CParam.h"
// #include <format>
// #include "CIniConf.h"
#include "CGlobal.h"
#include "CDirManager.h"
#include "CAlgorithmHandlerPrivate.h"
#include <chrono>
#include "../../util/StringFormat.h"
#include "../../log/Log.h"

using namespace LogComm;
// #include "CRingBuffer.h"
// #include "LogMacro.h"

void CSysHandlerPrivate::getDeviceInfo(const httplib::Request &req,
                                       httplib::Response &res)
{
    // if(!CHttpTools::checkSession(req, res))
    //     return;

    // XGlobal_Get(CDirManager, pDir, Dir);

    // QString str = "{}";
    // CIniConf ini(pDir->getPath(CDirManager::ConfigPath), "device_info.ini");
    // if(ini.open())
    // {
    //     auto id = ini.get("id" ,   "-");
    //     auto name = ini.get("name" , "-");
    //     auto desc = ini.get("desc" , "-");
    //     ini.close();

    //     str = QString(R"({"id":"%1", "name":"%2", "desc":"%3"})")
    //             .arg(id.toString())
    //             .arg(name.toString())
    //             .arg(desc.toString());
    // }
    // std::string ret = str.toStdString();
    // res.set_content(CHttpTools::jsonObj(true, ret), HttpContentType::json);
    // res.status = httplib::StatusCode::OK_200;
}

void CSysHandlerPrivate::setDeviceInfo(const httplib::Request &req,
                                       httplib::Response &res)
{
    // if(!CHttpTools::checkSession(req, res))
    //     return;

    // Req_Read_File(id);
    // Req_Read_File(name);
    // Req_Read_File(desc);

    // XGlobal_Get(CDirManager, pDir, Dir);
    // CIniConf ini(pDir->getPath(CDirManager::ConfigPath), "device_info.ini");
    // if(ini.open())
    // {
    //     if(id.valid())
    //         ini.set("id",   QString::fromStdString(id.value()) );

    //     if(name.valid())
    //         ini.set("name", QString::fromStdString(name.value()) );

    //     if(desc.valid())
    //         ini.set("desc", QString::fromStdString(desc.value()) );

    //     ini.sync();
    //     ini.close();
    // }
    // res.set_content(CHttpTools::json(true, u8"成功"), HttpContentType::json);
    // res.status = httplib::StatusCode::OK_200;
}

void CSysHandlerPrivate::getAlarmVideoLength(const httplib::Request &req,
                                             httplib::Response &res)
{
    // if(!CHttpTools::checkSession(req, res))
    //     return;

    // QString str = "{}";
    // XGlobal_Get(CDirManager, pDir, Dir);
    // CIniConf ini(pDir->getPath(CDirManager::ConfigPath), "alarm_video_info.ini");
    // if(ini.open())
    // {
    //     auto begin = ini.get("begin" , 0);
    //     auto end = ini.get("end" , 3);
    //     ini.close();

    //     str = QString(R"({"begin":%1, "end":%2})")
    //             .arg(begin.toString())
    //             .arg(end.toString());
    // }
    // std::string ret = str.toStdString();
    // res.set_content(CHttpTools::jsonObj(true, ret), HttpContentType::json);
    // res.status = httplib::StatusCode::OK_200;
}

void CSysHandlerPrivate::setAlarmVideoLength(const httplib::Request &req,
                                             httplib::Response &res)
{
    // if(!CHttpTools::checkSession(req, res))
    //     return;

    // Req_Read_File(begin);
    // Req_Read_File(end);

    // XGlobal_Get(CDirManager, pDir, Dir);
    // CIniConf ini(pDir->getPath(CDirManager::ConfigPath), "alarm_video_info.ini");
    // if(ini.open())
    // {
    //     if(begin.valid())
    //         ini.set("name" , QString::fromStdString(begin.value()).toInt() );

    //     if(end.valid())
    //         ini.set("desc" , QString::fromStdString(end.value()).toInt() );

    //     ini.sync();
    //     ini.close();
    // }
    // res.set_content(CHttpTools::json(true, u8"成功"), HttpContentType::json);
    // res.status = httplib::StatusCode::OK_200;
}

void CSysHandlerPrivate::getSysResources(const httplib::Request &req,
                                         httplib::Response &res)
{
    // if(!CHttpTools::checkSession(req, res))
    //     return;
}

void CSysHandlerPrivate::downloadLog(const httplib::Request &req,
                                     httplib::Response &res)
{
    // if(!CHttpTools::checkSession(req, res))
    //     return;
}

void CSysHandlerPrivate::getSysTime(const httplib::Request &req,
                                    httplib::Response &res)
{
    if(!CHttpTools::checkSession(req, res))
        return;

    (void) req;
    auto now = std::chrono::system_clock::now();
    auto ntimer = std::chrono::system_clock::to_time_t(now);
    std::stringstream ss;
    ss<<std::put_time(std::localtime(&ntimer),"%Y-%m-%d %X");
    
    res.set_content(CHttpTools::json(true, ss.str()), HttpContentType::json);
}

void CSysHandlerPrivate::setSysTime(const httplib::Request &req,
                                    httplib::Response &res)
{
    if(!CHttpTools::checkSession(req, res))
        return;

    Req_Read_File(dt);

    if(dt.valid()) {
        //std::string exec = "sudo timedatectl set-time '" + dt.value() + "'";
        //system( exec.c_str() );
        res.set_content(CHttpTools::json(true, u8"ok"), HttpContentType::json);
    } else {
        res.set_content(CHttpTools::json(false, u8"error"), HttpContentType::json);
    }
}

void CSysHandlerPrivate::importAlgorithm(const httplib::Request &req,
                                         httplib::Response &res,
                                         const httplib::ContentReader &content_reader)
{
    if(!CHttpTools::checkSession(req, res))
        return;

    //上传算法包
    if(req.is_multipart_form_data())
    {
        bool is_file = false;
        // CRingBuffer buf(5 * 1024 * 1024);

        XGlobal_Get(CDirManager, pDir, Dir);
        Json::Value root;
        // 1.先拿到 file 信息
        content_reader([&](const httplib::MultipartFormData &file)
        {
            if(file.filename.empty()) {
                is_file = false;
            }
            is_file = true;
            return true;
        },
        // 2.再流式读取
        [&](const char *data, size_t data_length)
        {
            if(is_file)
            {
                std::string strjson = std::string(data, data_length);
                std::cout<<"==>importAlgorithm json:" << strjson << std::endl;
            //   Json::CharReaderBuilder builder;
            //   std::unique_ptr<Json::CharReader> reader(builder.newCharReader());
            try
            {
                std::string errs;
                root = Util::stringToJson(strjson, errs);
                //    reader->parse(data, data + data_length, &root, &errs);
                if (!errs.empty()) {
                    std::cerr << "==>parse json:" << errs << std::endl;
                    return false;
                }
            }
            catch(const std::exception& e)
            {
                std::cerr << e.what() << '\n';
            }
            }
            return true;
        });

        std::vector<sAlgorithmItem> alg_list;

        //读取配置信息
        try {
            if(root.isNull()) {
                res.set_content(CHttpTools::json(false, u8"没有可导入的算法"), HttpContentType::json);
                return;
            }
            if(!root.isArray()) {
                std::cout<<"==>importAlgorithm json is not array type: " << root.type() << std::endl;
                res.set_content(CHttpTools::json(false, u8"格式错误"), HttpContentType::json);
                return;
            }

            for(const auto &param : root)
            {

                
                if (param.isObject() )
                {
                    int group_id = 0;
                    if (param.isMember("group_id") && param["group_id"].isInt()) {
                        group_id = param["group_id"].asInt();
                    }
                    int alg_id = 0;
                    if (param.isMember("alg_id") && param["alg_id"].isInt()) {
                        alg_id = param["alg_id"].asInt();
                    }
                    std::string alg_enum = "";
                    if (param.isMember("alg_enum") && param["alg_enum"].isString()) {
                        alg_enum = param["alg_enum"].asString();
                    }
                    std::string name = "";
                    if (param.isMember("name") && param["name"].isString()) {
                        name = param["name"].asString();
                    }
                    std::string desc = "";
                    if (param.isMember("desc") && param["desc"].isString()) {
                        desc = param["desc"].asString();
                    }
                    std::string params = "";
                    if (param.isMember("param") ) {
                        params = param["param"].toStyledString();
                    }
                    std::string version = "";   
                    if (param.isMember("version") && param["version"].isString()) {
                        version = param["version"].asString();
                    }
                    bool dlib = false;
                    if (param.isMember("dlib") && param["dlib"].isBool()) {
                        dlib = param["dlib"].asBool();
                    }
                    bool mask = false;
                    if (param.isMember("mask") && param["mask"].isBool()) { 
                        mask = param["mask"].asBool();
                    }

                    sAlgorithmItem item;
                    item.group_id = group_id;
                    item.alg_id = alg_id;
                    item.alg_enum = alg_enum;
                    item.name = name;
                    item.desc = desc;
                    item.params = params;
                    item.version = version;
                    item.dlib = dlib;
                    item.mask = mask;

                    alg_list.emplace_back(item);
                    std::cout << "==>importAlgorithm item: " << item.alg_id << ", " << item.name << ", "
                              << item.alg_enum << ", " << item.version << std::endl;
                }
                

                // const int group_id = param["group_id"].asInt();
                // const int alg_id = param["alg_id"].asInt();
                // const std::string alg_enum = param["alg_enum"].asString();
                // const std::string name = param["name"].asString();
                // const std::string desc = param["desc"].asString();
                // const std::string params = param["param"].asString();
                // const std::string version = param["version"].asString();
                // const bool dlib = param["dlib"].asBool();
                // const bool mask = param["mask"].asBool();


               
            }
        }
        catch (const std::exception &ex) {
          LOG_ERROR("http.importAlgorithm", " parse json: %s  ", ex.what());
          res.set_content(CHttpTools::json(false, u8"格式错误"), HttpContentType::json);
          return;
        }

        if(alg_list.size() > 0) {
            std::vector<int> errorList;
            CAlgorithmHandlerPrivate::add(alg_list, errorList);

          
            

            std::string retList = "";

            std::string sRet = Util::StringFormat("{\"error\":[%s]}",retList);
            res.set_content(CHttpTools::jsonObj(true, sRet), HttpContentType::json);
        }
        else {
            res.set_content(CHttpTools::json(false, u8"没有可导入的算法"), HttpContentType::json);
        }

        res.status = httplib::StatusCode::OK_200;
    }

}

void CSysHandlerPrivate::deviceUpgrade(const httplib::Request &req, httplib::Response &res,
                                       const httplib::ContentReader &content_reader)
{
    // if(!CHttpTools::checkSession(req, res))
    //     return;

    // //上传设备数据
    // if(req.is_multipart_form_data())
    // {
    //     QString _file;

    //     XGlobal_Get(CDirManager, pDir, Dir);

    //     // 1.先拿到 file 信息
    //     std::ofstream outfile;
    //     content_reader([&](const httplib::MultipartFormData &file)
    //     {
    //         _file = QString::fromStdString(file.filename);
    //         QString fullPath = QString("%1/%2")
    //                 .arg(pDir->getPath(CDirManager::UpgradePath))
    //                 .arg(_file);
    //         auto file_fullpath = fullPath.toLocal8Bit().toStdString();
    //         outfile.open(file_fullpath, std::ifstream::binary | std::ifstream::trunc);
    //         return outfile.is_open();
    //     },
    //     // 2.再流式读取
    //     [&](const char *data, size_t data_length)
    //     {
    //         if (outfile.is_open()) { //如果是文件  写入
    //             if (data_length > 0) {
    //                 outfile.write(data, data_length);
    //             }
    //             else {
    //                 outfile.close();
    //             }
    //         }
    //         return true;
    //     });

    //     res.set_content(CHttpTools::json(true, u8"成功"), HttpContentType::json);
    //     res.status = httplib::StatusCode::OK_200;
    // }

    //重启设备(开机启动程序里面 添加流程: 解压缩->替换->启动)
    //system("sudo reboot");
}

void CSysHandlerPrivate::getCompanyInfo(const httplib::Request &req,
                                        httplib::Response &res)
{
    // if(!CHttpTools::checkSession(req, res))
    //     return;

    // XGlobal_Get(CDirManager, pDir, Dir);


    // QString str = "{}";
    // CIniConf ini(pDir->getPath(CDirManager::ConfigPath), "company_info.ini");
    // if(ini.open())
    // {
    //     auto name = ini.get("name" , "-");
    //     auto desc = ini.get("desc" , "-");
    //     ini.close();

    //     QString pngData = pDir->getSubPath(CDirManager::ConfigPath) + "/logo.png";
    //     str = QString(R"({"img":"%1", "name":"%2", "desc":"%3"})")
    //             .arg(pngData)
    //             .arg(name.toString())
    //             .arg(desc.toString());
    // }
    // std::string ret = str.toStdString();
    // res.set_content(CHttpTools::jsonObj(true, ret), HttpContentType::json);
    // res.status = httplib::StatusCode::OK_200;
}

void CSysHandlerPrivate::setCompanyInfo(const httplib::Request &req,
                                        httplib::Response &res,
                                        const httplib::ContentReader &content_reader)
{
    // if(!CHttpTools::checkSession(req, res))
    //     return;

    // if(req.is_multipart_form_data())
    // {
    //     bool is_file = false;
    //     QMap<QString, QString> info;
    //     QString name;
    //     QString _file;

    //     XGlobal_Get(CDirManager, pDir, Dir);

    //     // 1.先拿到 file 信息
    //     std::ofstream outfile;
    //     content_reader([&](const httplib::MultipartFormData &file)
    //     {
    //         if(file.filename.empty()) {
    //             is_file = false;
    //             name = QString::fromStdString(file.name);
    //             return true;
    //         }
    //         name.clear();
    //         is_file = true;
    //         _file = QString::fromStdString(file.filename);
    //         QString fullPath = QString("%1/logo.png")
    //                 .arg(pDir->getPath(CDirManager::ConfigPath));
    //         auto file_fullpath = fullPath.toLocal8Bit().toStdString();
    //         outfile.open(file_fullpath, std::ifstream::binary | std::ifstream::trunc);
    //         return outfile.is_open();
    //     },
    //     // 2.再流式读取
    //     [&](const char *data, size_t data_length)
    //     {
    //         if(!is_file) { //如果不是文件，存储属性
    //             info.insert(name, QByteArray(data, (int)data_length));
    //         }
    //         else if (outfile.is_open()) { //如果是文件  写入
    //             if (data_length > 0) {
    //                 outfile.write(data, data_length);
    //             }
    //             else {
    //                 outfile.close();
    //             }
    //         }
    //         return true;
    //     });

    //     QString path = pDir->getPath(CDirManager::ConfigPath);

    //     CIniConf ini(path, "company_info.ini");
    //     if(ini.open())
    //     {
    //         if(info.contains("name"))
    //             ini.set("name" , info["name"] );

    //         if(info.contains("desc"))
    //             ini.set("desc" , info["desc"] );

    //         ini.sync();
    //         ini.close();
    //     }
    //     res.set_content(CHttpTools::json(true, u8"成功"), HttpContentType::json);
    //     res.status = httplib::StatusCode::OK_200;
    // }
}
