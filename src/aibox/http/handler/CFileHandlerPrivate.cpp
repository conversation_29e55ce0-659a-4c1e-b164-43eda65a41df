﻿#include "CFileHandlerPrivate.h"

#include <iostream>
// #include <QFile>
// #include <QDir>
// #include <QtDebug>
// #include <QCoreApplication>
#include "../../data/CParam.h"
#include "CAbstractServer.h"
#include "../../data/CGlobal.h"
#include "CHttpTools.h"
#include <filesystem>
#include "fmt/printf.h"
// #include "CGlobal.h"
#include "CDirManager.h"
#include <boost/filesystem.hpp>
// #include "LogMacro.h"

namespace fs=boost::filesystem;


static const std::string _FileDir("FileManager");
static const std::string _filename("filename");


//
void CFileHandlerPrivate::upload(const httplib::Request &req, httplib::Response &res,
                                 const httplib::ContentReader &content_reader)
{
    // 二进制数据可以用：multipart/form-data 和 application/octet-stream
    // QString path = qApp->applicationDirPath() + "/" + _FileDir;
    // QDir dir;
    // if(!dir.exists(path))
    //     dir.mkpath(path);

    // std::string pathStr = path.toStdString();

    // if (req.is_multipart_form_data()) {
    //     // 1.先拿到 file 信息
    //     std::ofstream outfile;
    //     content_reader([&](const httplib::MultipartFormData &file) {
    //         if(file.filename.empty()) {
    //             return true;
    //         }
    //         std::cerr << "\tupload read " << file.filename << "\t" << file.content << std::endl;
    //         auto file_fullpath = pathStr + "/" + file.filename;
    //         outfile.open(file_fullpath, std::ifstream::binary | std::ifstream::trunc);
    //         return outfile.is_open();
    //     },
    //     // 2.再流式读取
    //     [&](const char *data, size_t data_length) {
    //         if (outfile.is_open()) {
    //             if (data_length > 0) {
    //                 outfile.write(data, data_length);
    //             }
    //             else {
    //                 outfile.close();
    //             }
    //         }
    //         return true;
    //     });
    // }
    // else {
    //     auto filename = req.get_header_value(_filename);
    //     auto file_fullpath = pathStr + "/" + filename;
    //     std::ofstream outfile(file_fullpath, std::ifstream::binary);
    //     if (!outfile.is_open()) {
    //         res.status = 500;
    //         return;
    //     }
    //     content_reader([&](const char* data, size_t data_length) {
    //         outfile.write(data, data_length);
    //         return true;
    //     });

    //     outfile.close();
    //     res.status = 200;
    // }
    // res.set_content(CHttpTools::json(true), HttpContentType::json);
}

//
void CFileHandlerPrivate::download(const httplib::Request& req, httplib::Response& res)
{
    Req_Read_Param(file_name)
    if(!file_name.valid() || file_name.value().empty()) {
        res.set_content(CHttpTools::json(false, u8"缺少参数"), HttpContentType::json);
        return;
    }
    XGlobal_Get(CDirManager, pDir, Dir);
    std::string file = fmt::format("{}/{}", pDir->rootPath(), file_name.value());
    
    if (fs::exists(file)) {
        res.set_file_content(file, HttpContentType::file);
        res.set_header(std::string("Content-Disposition"), std::string("attachment; filename=") + file_name.value());
    }
    else {
        res.set_content(CHttpTools::json(false, u8"file not exists"), HttpContentType::json);
        res.status = httplib::StatusCode::OK_200;
        return;
    }
    res.status = httplib::StatusCode::OK_200;
}
