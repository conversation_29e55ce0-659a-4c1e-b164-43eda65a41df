﻿#ifndef _CALGORITHMHANDLERPRIVATEH
#define _CALGORITHMHANDLERPRIVATEH
#include "../../data/OverLineInfo.h"
#include "httplib.h"
// #include <QList>
// #include <QString>

struct sAlgorithmItem
{
    int group_id;
    int alg_id;
    std::string alg_enum;
    std::string name;
    std::string desc;
    std::string params;
    std::string version;
    bool dlib;
    bool mask;
};

//算法
class CAlgorithmHandlerPrivate
{
public:
    static void query(const httplib::Request &req, httplib::Response &res);//查询
    static void edit(const httplib::Request &req, httplib::Response &res);

    //查询参数
    static void queryParam(const httplib::Request &req, httplib::Response &res);

    static void add(const std::vector<sAlgorithmItem> &list, std::vector<int> &errorList);
    static bool getThrs(const int& algId,std::string & thrs);
    static bool getLinePoints(const int &cameraId, std::vector<LineInfo> &pointsList);
};

#endif /* _CALGORITHMHANDLERPRIVATEH */





