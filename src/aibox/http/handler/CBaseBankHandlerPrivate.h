﻿#ifndef _CBASEBANKHANDLERPRIVATEH
#define _CBASEBANKHANDLERPRIVATEH

#include "httplib.h"

class CBaseBankHandlerPrivate
{
public:
    static void sub_group(const httplib::Request &req, httplib::Response &res);//查询分组 白名单/黑名单...
    static void add_group(const httplib::Request &req, httplib::Response &res);//添加分组
    static void remove_group(const httplib::Request &req, httplib::Response &res);//删除分组

    static void query(const httplib::Request &req, httplib::Response &res);    //查询
    static void edit(const httplib::Request &req, httplib::Response &res, const httplib::ContentReader &content_reader);     //编辑
    static void remove(const httplib::Request &req, httplib::Response &res);   //删除 批量删除

    static void add(const httplib::Request &req, httplib::Response &res,
                    const httplib::ContentReader &content_reader);//添加

    static void addZip(const httplib::Request &req, httplib::Response &res,
                       const httplib::ContentReader &content_reader);//批量导入

    static void load(const httplib::Request &req, httplib::Response &res);//载入底库

    //内部使用  根据sub_group_id 查询所有图片
    static bool queryFromSubGroup(int sub_group_id, std::string &ret);
    //批量导入
    static void batchDelete(const httplib::Request &req, httplib::Response &res) ;
};

#endif /* _CBASEBANKHANDLERPRIVATEH */



