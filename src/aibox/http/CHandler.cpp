﻿#include "CHandler.h"
#include "httplib.h"
#include "CAbstractServer.h"



#include "./handler/CDefaultHandlerPrivate.h"
#include "./handler/CUserHandlerPrivate.h"
#include "./handler/CSysHandlerPrivate.h"
#include "./handler/CInputSourceHandlerPrivate.h"
#include "./handler/CFileHandlerPrivate.h"
#include "./handler/CBaseBankHandlerPrivate.h"
#include "./handler/CAlgorithmHandlerPrivate.h"
#include "./handler/CAlarmLogHandlerPrivate.h"
#include "./handler/COverlineHandlerPrivate.h"

#define API "/api/v1/"

void CHandler::registeFunc(std::shared_ptr<CAbstractServer> pServer)
{
  std::cout<<"CHandler::registeFunc1111111"<<std::endl;
    auto *pHttpServer = pServer->getServer();

    pHttpServer->set_default_headers({
                                         {"Connection", "close"},
                                         {"Cache-Control", "no-cache"},
                                         {"Access-Control-Allow-Origin", "*"},
                                         {"Access-Control-Allow-Methods", "*"},
                                         {"Access-Control-Allow-Headers", "*"},
                                         {"Access-Control-Allow-Credentials", "true"}
                                     });

    //https://github.com/yhirose/cpp-httplib/issues/159
    pHttpServer->Options("/*", [&](const httplib::Request& req, httplib::Response& res) {
        (void) req;
        res.status = 200;
    });
    pHttpServer->Options("/", [&](const httplib::Request& req, httplib::Response& res) {
        (void) req;
        res.status = 200;
    });
    pHttpServer->Options("/(.*)", [&](const httplib::Request& req, httplib::Response& res) {
        (void) req;
        res.status = 200;
    });

    pHttpServer->set_read_timeout(std::chrono::milliseconds(1000));
    pHttpServer->set_write_timeout(std::chrono::milliseconds(1000));
    pHttpServer->set_tcp_nodelay(true);

    //
    pHttpServer->set_logger(CDefaultHandlerPrivate::logger);
    pHttpServer->set_error_handler(CDefaultHandlerPrivate::error);

    //
    pHttpServer->Get (API "version",  CDefaultHandlerPrivate::version);
    pHttpServer->Post(API "version",  CDefaultHandlerPrivate::version);

    pHttpServer->Get (API "stop",     CDefaultHandlerPrivate::stop);
    pHttpServer->Post(API "stop",     CDefaultHandlerPrivate::stop);

    //用户操作
    pHttpServer->Post(API "login",      CUserHandlerPrivate::login);
    pHttpServer->Post(API "modify_pwd", CUserHandlerPrivate::modifyPwd);
    pHttpServer->Post(API "reset_pwd",  CUserHandlerPrivate::resetPwd);
    pHttpServer->Post(API "logout",     CUserHandlerPrivate::logout);

    //系统设置
    pHttpServer->Get (API "get_device_info",        CSysHandlerPrivate::getDeviceInfo);
    pHttpServer->Post(API "get_device_info",        CSysHandlerPrivate::getDeviceInfo);
    pHttpServer->Post(API "set_device_info",        CSysHandlerPrivate::setDeviceInfo);

    pHttpServer->Get (API "get_alarm_video_length", CSysHandlerPrivate::getAlarmVideoLength);
    pHttpServer->Post(API "get_alarm_video_length", CSysHandlerPrivate::getAlarmVideoLength);
    pHttpServer->Post(API "set_alarm_video_length", CSysHandlerPrivate::setAlarmVideoLength);

    pHttpServer->Post(API "get_sys_resources",      CSysHandlerPrivate::getSysResources);
    pHttpServer->Post(API "download_log",           CSysHandlerPrivate::downloadLog);

    pHttpServer->Get (API "get_sys_time",           CSysHandlerPrivate::getSysTime);
    pHttpServer->Post(API "get_sys_time",           CSysHandlerPrivate::getSysTime);
    pHttpServer->Post(API "set_sys_time",           CSysHandlerPrivate::setSysTime);

    pHttpServer->Post(API "import_algorithm",       CSysHandlerPrivate::importAlgorithm);
    pHttpServer->Post(API "device_upgrade",         CSysHandlerPrivate::deviceUpgrade);

    pHttpServer->Get (API "get_company_info",       CSysHandlerPrivate::getCompanyInfo);
    pHttpServer->Post(API "get_company_info",       CSysHandlerPrivate::getCompanyInfo);
    pHttpServer->Post(API "set_company_info",       CSysHandlerPrivate::setCompanyInfo);

    //视频接入
    pHttpServer->Post(API "source/get_list",       CInputSourceHandlerPrivate::getList);
    pHttpServer->Post(API "source/query",          CInputSourceHandlerPrivate::query);
    pHttpServer->Post(API "source/total",          CInputSourceHandlerPrivate::total);
    pHttpServer->Post(API "source/enable",         CInputSourceHandlerPrivate::enable);
    pHttpServer->Post(API "source/add",            CInputSourceHandlerPrivate::add);//添加摄像头
    pHttpServer->Post(API "source/remove",         CInputSourceHandlerPrivate::remove);
    pHttpServer->Post(API "source/edit",           CInputSourceHandlerPrivate::edit);
    pHttpServer->Post(API "source/batchDelete",    CInputSourceHandlerPrivate::batchDelete);//批量删除

    //文件操作
    pHttpServer->Get (API "download",              CFileHandlerPrivate::download);

    //底库管理
    pHttpServer->Post(API "basebank/query_sub_group",  CBaseBankHandlerPrivate::sub_group);
    pHttpServer->Post(API "basebank/add_sub_group",    CBaseBankHandlerPrivate::add_group);
    pHttpServer->Post(API "basebank/remove_sub_group", CBaseBankHandlerPrivate::remove_group);
    pHttpServer->Post(API "basebank/query",            CBaseBankHandlerPrivate::query);
    pHttpServer->Post(API "basebank/remove",           CBaseBankHandlerPrivate::remove);
    pHttpServer->Post(API "basebank/edit",             CBaseBankHandlerPrivate::edit);//
    pHttpServer->Post(API "basebank/add",              CBaseBankHandlerPrivate::add);
    pHttpServer->Post(API "basebank/add_zip",          CBaseBankHandlerPrivate::addZip);
    pHttpServer->Post(API "basebank/load",             CBaseBankHandlerPrivate::load);
    pHttpServer->Post(API "basebank/batchDelete",      CBaseBankHandlerPrivate::batchDelete);//批量删除

    //算法厂库
    pHttpServer->Post(API "algorithm/query", CAlgorithmHandlerPrivate::query);
    pHttpServer->Post(API "algorithm/edit",  CAlgorithmHandlerPrivate::edit);
    pHttpServer->Post(API "algorithm/query_param", CAlgorithmHandlerPrivate::queryParam);

    //告警记录
    pHttpServer->Get (API "alarm/query_new", CAlarmLogHandlerPrivate::queryNew);
    pHttpServer->Post(API "alarm/query",     CAlarmLogHandlerPrivate::query);
    pHttpServer->Post(API "alarm/remove",    CAlarmLogHandlerPrivate::remove);
    pHttpServer->Post(API "alarm/solve",     CAlarmLogHandlerPrivate::solve);

    pHttpServer->Get(API "alarm/total",        CAlarmLogHandlerPrivate::total);
    pHttpServer->Get(API "alarm/group_day",    CAlarmLogHandlerPrivate::groupByDay);
    pHttpServer->Get(API "alarm/group_source", CAlarmLogHandlerPrivate::groupBySource);
    pHttpServer->Get(API "alarm/group_alg",    CAlarmLogHandlerPrivate::groupByAlgorithm);
    pHttpServer->Post(API "alarm/batchDelete", CAlarmLogHandlerPrivate::batchDelete);//批量删除
    pHttpServer->Post(API "alarm/crossline",     CAlarmLogHandlerPrivate::crossline);//批量删除


    //跨线查询
    pHttpServer->Post(API "overline/query",     COverlineHandlerPrivate::query);
    //跨线清零
    pHttpServer->Post(API "overline/zero",      COverlineHandlerPrivate::zero);
    //自动清0策略
    pHttpServer->Post(API "overline/auto_zero", COverlineHandlerPrivate::autoZero);

    std::cout<<"CHandler::registeFunc1111111  endl"<<std::endl;
}
