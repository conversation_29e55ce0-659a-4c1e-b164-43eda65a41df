#ifndef _ENERGYSTORAGECABINMANAGERH
#define _ENERGYSTORAGECABINMANAGERH
#include "Define.h"
#include <memory>
#include <map>

namespace AiBox {

struct EnergyStorageCabinInfo {
    
    uint32 m_uId;
    uint32 m_uBatteryPower;
    uint32 m_uRatedCapacityKwh;
    uint32 m_uOperatingStatus;
    uint32 m_uTemp;
    std::string m_sCode;
    std::string m_sName;
    std::string m_sdesc;


    EnergyStorageCabinInfo() {
        m_uId = 0;
        m_uBatteryPower = 0;
        m_uRatedCapacityKwh = 0;
        m_uOperatingStatus = 0;
        m_uTemp = 0;
    }
};


typedef std::shared_ptr<EnergyStorageCabinInfo> EnergyStorageCabinPtr;

class CEnergyStorageCabinManager {
  public:
    CEnergyStorageCabinManager();
    ~CEnergyStorageCabinManager();

  
    void AddEnergyStorageCabin(EnergyStorageCabinPtr pEnergyStorageCabin) {
        m_vEnergyStorageCabinList[pEnergyStorageCabin->m_uId] = pEnergyStorageCabin;
    }
    
    
    bool IsContain(uint32 uId) {
        return m_vEnergyStorageCabinList.find(uId) != m_vEnergyStorageCabinList.end();
    }


    EnergyStorageCabinPtr GetEnergyStorageCabin(uint32 uId) {
        auto it = m_vEnergyStorageCabinList.find(uId);
        if (it != m_vEnergyStorageCabinList.end()) {
            return it->second;
        }
        return nullptr;
    }

  private:
    std::map<uint32 ,EnergyStorageCabinPtr> m_vEnergyStorageCabinList;
};


} // namespace AiBox

#endif /* _ENERGYSTORAGECABINMANAGERH */


