#ifndef _ENERGYSTORAGECABINMANAGERH
#define _ENERGYSTORAGECABINMANAGERH
#include "CMD_AIBox.h"
#include "Define.h"
#include <map>
#include <memory>

namespace AiBox
{

struct MainBoardInfo
{
    std::string m_nMainBoardId;
    uint32 m_nSocketId;
    bool m_bIsOnline = false; // 是否在线
    std::vector<Dev_Status> m_vDevStatus;
};

typedef std::shared_ptr<MainBoardInfo> MainBoardInfoPtr;

struct EnergyStorageCabinInfo
{

    uint32 m_uId;
    uint32 m_uBatteryPower;
    uint32 m_uRatedCapacityKwh;
    uint32 m_uOperatingStatus;
    uint32 m_uTemp;
    std::string m_nMainBoardId;
    int32_t m_sCode;
    std::string m_sName;
    std::string m_sdesc;

    MainBoardInfoPtr m_pMainBoardInfo;

    EnergyStorageCabinInfo()
    {
        m_uId = 0;
        m_uBatteryPower = 0;
        m_uRatedCapacityKwh = 0;
        m_uOperatingStatus = 0;
        m_uTemp = 0;
        m_sCode = 0;
    }
};

typedef std::shared_ptr<EnergyStorageCabinInfo> EnergyStorageCabinPtr;

class CEnergyStorageCabinManager
{
  public:
    CEnergyStorageCabinManager();
    ~CEnergyStorageCabinManager();

    void AddEnergyStorageCabin(EnergyStorageCabinPtr pEnergyStorageCabin)
    {
        m_vEnergyStorageCabinList[pEnergyStorageCabin->m_uId] = pEnergyStorageCabin;
    }

    bool IsContain(uint32 uId)
    {
        return m_vEnergyStorageCabinList.find(uId) != m_vEnergyStorageCabinList.end();
    }

    EnergyStorageCabinPtr GetEnergyStorageCabin(uint32 uId)
    {
        auto it = m_vEnergyStorageCabinList.find(uId);
        if (it != m_vEnergyStorageCabinList.end())
        {
            return it->second;
        }
        return nullptr;
    }

    EnergyStorageCabinPtr GetEnergyStorageCabinByCode(uint32 uCode)
    {
        for (auto it = m_vEnergyStorageCabinList.begin(); it != m_vEnergyStorageCabinList.end();++it){
            if (it->second->m_sCode == uCode){
                return it->second;
            }
        }
        
        return nullptr;
    }

    EnergyStorageCabinPtr GetEnergyStorageCabinByMainBoardId(const std::string &uId)
    {
        for (auto &it : m_vEnergyStorageCabinList)
        {
            if (it.second->m_nMainBoardId == uId)
            {
                return it.second;
            }
        }
        return nullptr;
    }

    bool IsContainByMainBoardId(const std::string &uId)
    {
        for (auto &it : m_vEnergyStorageCabinList)
        {
            if (it.second->m_nMainBoardId == uId)
            {
                return true;
            }
        }
        return false;
    }

    EnergyStorageCabinPtr GetEnergyStorageCabinBySocketId(uint32 uId)
    {
        for (auto &it : m_vEnergyStorageCabinList)
        {
            if (it.second->m_pMainBoardInfo && it.second->m_pMainBoardInfo->m_nSocketId == uId)
            {
                return it.second;
            }
        }
        return nullptr;
    }

  private:
    std::map<uint32, EnergyStorageCabinPtr> m_vEnergyStorageCabinList;
};

} // namespace AiBox

#endif /* _ENERGYSTORAGECABINMANAGERH */
