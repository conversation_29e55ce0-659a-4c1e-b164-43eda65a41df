CollectSourceFiles(
    ${CMAKE_CURRENT_SOURCE_DIR}
    PRIVATE_SOURCES)

CollectIncludeDirectories(
  ${CMAKE_CURRENT_SOURCE_DIR}
  PUBLIC_INCLUDES)
  
  set(CMAKE_CXX_STANDARD 14)
  set(CMAKE_CXX_STANDARD_REQUIRED ON)
# CollectIncludeDirectories(
#   ${CMAKE_CURRENT_SOURCE_DIR}/webcc
#   PUBLIC_INCLUDES)

#   math(EXPR stack_size "100*1024*1024")  # ����100MB���ֽ���
#   set(CMAKE_EXE_LINKER_FLAGS "-Wl,--stack,${stack_size}") 

  set(CMAKE_BUILD_TYPE Debug)
add_definitions(-DLENDY_API_EXPORT_GAME)

add_executable(AI_Box ${PRIVATE_SOURCES})


find_program(VALGRIND valgrind)
if(VALGRIND)
    add_custom_target(valgrind_test
        COMMAND ${VALGRIND} 
                --leak-check=full 
                --track-origins=yes 
                --error-exitcode=1 
                $<TARGET_FILE:AI_Box>
        DEPENDS AI_Box
        COMMENT "Running Valgrind memory check on AI_Box"
        WORKING_DIRECTORY ${CMAKE_SOURCE_DIR}/bin
    )
endif()


# set(CMAKE_CXX_STANDARD 14)

target_include_directories(AI_Box 
  PUBLIC
    ${PUBLIC_INCLUDES}
  PRIVATE
    ${CMAKE_CURRENT_BINARY_DIR})



find_library(JSONCPP_LIB NAMES jsoncpp PATHS /usr/local/lib NO_DEFAULT_PATH)
find_package(Boost 1.70 REQUIRED COMPONENTS filesystem)
target_link_libraries(AI_Box PRIVATE ${JSONCPP_LIB})
# target_link_libraries(LogonServer PRIVATE stdc++fs) 
link_directories(/usr/local/lib)
# target_link_libraries(LogonServer PRIVATE libwebcc.a libjsoncpp.a)
target_link_libraries(AI_Box 
  PUBLIC
	DataBase
    Net
    jsoncpp
    stdc++fs
    xlnt
    Boost::filesystem
    )

    # target_link_options(your_target_name PRIVATE -rdynamic)
if( UNIX )
	add_custom_command(TARGET AI_Box
      POST_BUILD
	  COMMAND 
	  COMMAND ${CMAKE_COMMAND} -E copy ${CMAKE_CURRENT_SOURCE_DIR}/AI_Box ${CMAKE_SOURCE_DIR}/bin/
      COMMAND ${CMAKE_COMMAND} -E copy ${CMAKE_CURRENT_SOURCE_DIR}/AI_Box.ini ${CMAKE_SOURCE_DIR}/bin/)
endif()

if( UNIX )
  install(TARGETS AI_Box DESTINATION bin)
  install(FILES LogonConfig.ini DESTINATION bin)
elseif( WIN32 )
  install(TARGETS AI_Box DESTINATION bin)
  install(FILES LogonConfig.ini DESTINATION bin)
endif()