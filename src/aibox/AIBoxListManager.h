#ifndef _AIBOXLISTMANAGERH
#define _AIBOXLISTMANAGERH
#include <map>
#include <memory>
#include "Define.h"
namespace AiBox {
struct CameraDevInfo {
    uint32 m_nId;//摄像头ID
    std::string m_sName;//摄像头名称
    std::string m_sIp;//摄像头IP
    std::string m_sStreamUrl;//摄像头流地址
    std::string m_sPushUrl;//推送地址
    std::string m_sFormat;//格式
    std::string m_sUser;//账户
    std::string m_sPwd;//密码
    uint32 m_nDevice_id; // 设备编号
    uint32 m_nStatus;//摄像头状态
    uint32 m_nLocationType; // 位置类型所属位置类型：1外围 2储能舱
    uint32 m_nLocationId;   // 所属位置id 这个和储能舱，外围表对应
    CameraDevInfo() {
        m_nId = 0;
        m_nDevice_id = 0;
        m_nStatus = 0;
        m_nLocationType = 0;
        m_nLocationId = 0;
    }
};


typedef std::shared_ptr<CameraDevInfo> CameraDevInfoPtr;

struct AIBoxInfo {
    uint32 m_nBoxId;//边缘盒ID
    std::string m_sIp;//边缘盒IP
    uint32 m_nDeviceLocationId;//所属设备位置ID
    std::vector<CameraDevInfoPtr> m_vCameraList; // 摄像头列表
    uint32 m_nSocketId;//socketID
    std::string m_sDesc;//描述
    uint32 m_nAlgType; //算法类型


    AIBoxInfo() {
        m_nBoxId = 0;
        m_nDeviceLocationId = 0;
        m_nSocketId = 0;
        m_nAlgType = 0;
    }
};

typedef std::shared_ptr<AIBoxInfo> BoxPtr;


class CAIBoxListManager {
  private:
  std::map<uint32, BoxPtr> m_mBoxList;//sockedID -> AIBoxInfo
  public :
    CAIBoxListManager(/* args */);
    ~CAIBoxListManager();
    BoxPtr GetAIBoxInfo(uint32 nSocketId);
    void AddAIBoxInfo(BoxPtr pAIBoxInfo);
    bool DelAIBoxInfo(uint32 nSocketId);

    void FindCameraByCameraId(uint32 nCameraId, CameraDevInfoPtr &pCameraInfo);
    bool IsContainBox(uint32_t nBoxId);
    int GetBoxCount();
    BoxPtr GetBoxByIndex(int nIndex);
};
} // namespace AiBox

#endif /* _AIBOXLISTMANAGERH */