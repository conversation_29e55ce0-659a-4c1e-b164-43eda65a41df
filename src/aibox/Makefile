# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.28

# Default target executed when no arguments are given to make.
default_target: all
.PHONY : default_target

# Allow only one "make -f Makefile2" at a time, but pass parallelism.
.NOTPARALLEL:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Produce verbose output by default.
VERBOSE = 1

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/EnergyStorage/AI_Box

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/EnergyStorage/AI_Box

#=============================================================================
# Targets provided globally by CMake.

# Special rule for the target edit_cache
edit_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "No interactive CMake dialog available..."
	/usr/bin/cmake -E echo No\ interactive\ CMake\ dialog\ available.
.PHONY : edit_cache

# Special rule for the target edit_cache
edit_cache/fast: edit_cache
.PHONY : edit_cache/fast

# Special rule for the target rebuild_cache
rebuild_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Running CMake to regenerate build system..."
	/usr/bin/cmake --regenerate-during-build -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : rebuild_cache

# Special rule for the target rebuild_cache
rebuild_cache/fast: rebuild_cache
.PHONY : rebuild_cache/fast

# Special rule for the target list_install_components
list_install_components:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Available install components are: \"Unspecified\""
.PHONY : list_install_components

# Special rule for the target list_install_components
list_install_components/fast: list_install_components
.PHONY : list_install_components/fast

# Special rule for the target install
install: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Install the project..."
	/usr/bin/cmake -P cmake_install.cmake
.PHONY : install

# Special rule for the target install
install/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Install the project..."
	/usr/bin/cmake -P cmake_install.cmake
.PHONY : install/fast

# Special rule for the target install/local
install/local: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Installing only the local directory..."
	/usr/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local

# Special rule for the target install/local
install/local/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Installing only the local directory..."
	/usr/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local/fast

# Special rule for the target install/strip
install/strip: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Installing the project stripped..."
	/usr/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip

# Special rule for the target install/strip
install/strip/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Installing the project stripped..."
	/usr/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip/fast

# The main all target
all: cmake_check_build_system
	cd /home/<USER>/EnergyStorage/AI_Box && $(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/EnergyStorage/AI_Box/CMakeFiles /home/<USER>/EnergyStorage/AI_Box/src/aibox//CMakeFiles/progress.marks
	cd /home/<USER>/EnergyStorage/AI_Box && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 src/aibox/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/EnergyStorage/AI_Box/CMakeFiles 0
.PHONY : all

# The main clean target
clean:
	cd /home/<USER>/EnergyStorage/AI_Box && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 src/aibox/clean
.PHONY : clean

# The main clean target
clean/fast: clean
.PHONY : clean/fast

# Prepare targets for installation.
preinstall: all
	cd /home/<USER>/EnergyStorage/AI_Box && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 src/aibox/preinstall
.PHONY : preinstall

# Prepare targets for installation.
preinstall/fast:
	cd /home/<USER>/EnergyStorage/AI_Box && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 src/aibox/preinstall
.PHONY : preinstall/fast

# clear depends
depend:
	cd /home/<USER>/EnergyStorage/AI_Box && $(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 1
.PHONY : depend

# Convenience name for target.
src/aibox/CMakeFiles/AI_Box.dir/rule:
	cd /home/<USER>/EnergyStorage/AI_Box && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 src/aibox/CMakeFiles/AI_Box.dir/rule
.PHONY : src/aibox/CMakeFiles/AI_Box.dir/rule

# Convenience name for target.
AI_Box: src/aibox/CMakeFiles/AI_Box.dir/rule
.PHONY : AI_Box

# fast build rule for target.
AI_Box/fast:
	cd /home/<USER>/EnergyStorage/AI_Box && $(MAKE) $(MAKESILENT) -f src/aibox/CMakeFiles/AI_Box.dir/build.make src/aibox/CMakeFiles/AI_Box.dir/build
.PHONY : AI_Box/fast

# Convenience name for target.
src/aibox/CMakeFiles/valgrind_test.dir/rule:
	cd /home/<USER>/EnergyStorage/AI_Box && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 src/aibox/CMakeFiles/valgrind_test.dir/rule
.PHONY : src/aibox/CMakeFiles/valgrind_test.dir/rule

# Convenience name for target.
valgrind_test: src/aibox/CMakeFiles/valgrind_test.dir/rule
.PHONY : valgrind_test

# fast build rule for target.
valgrind_test/fast:
	cd /home/<USER>/EnergyStorage/AI_Box && $(MAKE) $(MAKESILENT) -f src/aibox/CMakeFiles/valgrind_test.dir/build.make src/aibox/CMakeFiles/valgrind_test.dir/build
.PHONY : valgrind_test/fast

AIBoxListManager.o: AIBoxListManager.cpp.o
.PHONY : AIBoxListManager.o

# target to build an object file
AIBoxListManager.cpp.o:
	cd /home/<USER>/EnergyStorage/AI_Box && $(MAKE) $(MAKESILENT) -f src/aibox/CMakeFiles/AI_Box.dir/build.make src/aibox/CMakeFiles/AI_Box.dir/AIBoxListManager.cpp.o
.PHONY : AIBoxListManager.cpp.o

AIBoxListManager.i: AIBoxListManager.cpp.i
.PHONY : AIBoxListManager.i

# target to preprocess a source file
AIBoxListManager.cpp.i:
	cd /home/<USER>/EnergyStorage/AI_Box && $(MAKE) $(MAKESILENT) -f src/aibox/CMakeFiles/AI_Box.dir/build.make src/aibox/CMakeFiles/AI_Box.dir/AIBoxListManager.cpp.i
.PHONY : AIBoxListManager.cpp.i

AIBoxListManager.s: AIBoxListManager.cpp.s
.PHONY : AIBoxListManager.s

# target to generate assembly for a file
AIBoxListManager.cpp.s:
	cd /home/<USER>/EnergyStorage/AI_Box && $(MAKE) $(MAKESILENT) -f src/aibox/CMakeFiles/AI_Box.dir/build.make src/aibox/CMakeFiles/AI_Box.dir/AIBoxListManager.cpp.s
.PHONY : AIBoxListManager.cpp.s

AttemperEngineSink.o: AttemperEngineSink.cpp.o
.PHONY : AttemperEngineSink.o

# target to build an object file
AttemperEngineSink.cpp.o:
	cd /home/<USER>/EnergyStorage/AI_Box && $(MAKE) $(MAKESILENT) -f src/aibox/CMakeFiles/AI_Box.dir/build.make src/aibox/CMakeFiles/AI_Box.dir/AttemperEngineSink.cpp.o
.PHONY : AttemperEngineSink.cpp.o

AttemperEngineSink.i: AttemperEngineSink.cpp.i
.PHONY : AttemperEngineSink.i

# target to preprocess a source file
AttemperEngineSink.cpp.i:
	cd /home/<USER>/EnergyStorage/AI_Box && $(MAKE) $(MAKESILENT) -f src/aibox/CMakeFiles/AI_Box.dir/build.make src/aibox/CMakeFiles/AI_Box.dir/AttemperEngineSink.cpp.i
.PHONY : AttemperEngineSink.cpp.i

AttemperEngineSink.s: AttemperEngineSink.cpp.s
.PHONY : AttemperEngineSink.s

# target to generate assembly for a file
AttemperEngineSink.cpp.s:
	cd /home/<USER>/EnergyStorage/AI_Box && $(MAKE) $(MAKESILENT) -f src/aibox/CMakeFiles/AI_Box.dir/build.make src/aibox/CMakeFiles/AI_Box.dir/AttemperEngineSink.cpp.s
.PHONY : AttemperEngineSink.cpp.s

EnergyStorageCabinManager.o: EnergyStorageCabinManager.cpp.o
.PHONY : EnergyStorageCabinManager.o

# target to build an object file
EnergyStorageCabinManager.cpp.o:
	cd /home/<USER>/EnergyStorage/AI_Box && $(MAKE) $(MAKESILENT) -f src/aibox/CMakeFiles/AI_Box.dir/build.make src/aibox/CMakeFiles/AI_Box.dir/EnergyStorageCabinManager.cpp.o
.PHONY : EnergyStorageCabinManager.cpp.o

EnergyStorageCabinManager.i: EnergyStorageCabinManager.cpp.i
.PHONY : EnergyStorageCabinManager.i

# target to preprocess a source file
EnergyStorageCabinManager.cpp.i:
	cd /home/<USER>/EnergyStorage/AI_Box && $(MAKE) $(MAKESILENT) -f src/aibox/CMakeFiles/AI_Box.dir/build.make src/aibox/CMakeFiles/AI_Box.dir/EnergyStorageCabinManager.cpp.i
.PHONY : EnergyStorageCabinManager.cpp.i

EnergyStorageCabinManager.s: EnergyStorageCabinManager.cpp.s
.PHONY : EnergyStorageCabinManager.s

# target to generate assembly for a file
EnergyStorageCabinManager.cpp.s:
	cd /home/<USER>/EnergyStorage/AI_Box && $(MAKE) $(MAKESILENT) -f src/aibox/CMakeFiles/AI_Box.dir/build.make src/aibox/CMakeFiles/AI_Box.dir/EnergyStorageCabinManager.cpp.s
.PHONY : EnergyStorageCabinManager.cpp.s

RoomListManager.o: RoomListManager.cpp.o
.PHONY : RoomListManager.o

# target to build an object file
RoomListManager.cpp.o:
	cd /home/<USER>/EnergyStorage/AI_Box && $(MAKE) $(MAKESILENT) -f src/aibox/CMakeFiles/AI_Box.dir/build.make src/aibox/CMakeFiles/AI_Box.dir/RoomListManager.cpp.o
.PHONY : RoomListManager.cpp.o

RoomListManager.i: RoomListManager.cpp.i
.PHONY : RoomListManager.i

# target to preprocess a source file
RoomListManager.cpp.i:
	cd /home/<USER>/EnergyStorage/AI_Box && $(MAKE) $(MAKESILENT) -f src/aibox/CMakeFiles/AI_Box.dir/build.make src/aibox/CMakeFiles/AI_Box.dir/RoomListManager.cpp.i
.PHONY : RoomListManager.cpp.i

RoomListManager.s: RoomListManager.cpp.s
.PHONY : RoomListManager.s

# target to generate assembly for a file
RoomListManager.cpp.s:
	cd /home/<USER>/EnergyStorage/AI_Box && $(MAKE) $(MAKESILENT) -f src/aibox/CMakeFiles/AI_Box.dir/build.make src/aibox/CMakeFiles/AI_Box.dir/RoomListManager.cpp.s
.PHONY : RoomListManager.cpp.s

ServiceUnits.o: ServiceUnits.cpp.o
.PHONY : ServiceUnits.o

# target to build an object file
ServiceUnits.cpp.o:
	cd /home/<USER>/EnergyStorage/AI_Box && $(MAKE) $(MAKESILENT) -f src/aibox/CMakeFiles/AI_Box.dir/build.make src/aibox/CMakeFiles/AI_Box.dir/ServiceUnits.cpp.o
.PHONY : ServiceUnits.cpp.o

ServiceUnits.i: ServiceUnits.cpp.i
.PHONY : ServiceUnits.i

# target to preprocess a source file
ServiceUnits.cpp.i:
	cd /home/<USER>/EnergyStorage/AI_Box && $(MAKE) $(MAKESILENT) -f src/aibox/CMakeFiles/AI_Box.dir/build.make src/aibox/CMakeFiles/AI_Box.dir/ServiceUnits.cpp.i
.PHONY : ServiceUnits.cpp.i

ServiceUnits.s: ServiceUnits.cpp.s
.PHONY : ServiceUnits.s

# target to generate assembly for a file
ServiceUnits.cpp.s:
	cd /home/<USER>/EnergyStorage/AI_Box && $(MAKE) $(MAKESILENT) -f src/aibox/CMakeFiles/AI_Box.dir/build.make src/aibox/CMakeFiles/AI_Box.dir/ServiceUnits.cpp.s
.PHONY : ServiceUnits.cpp.s

data/CDirManager.o: data/CDirManager.cpp.o
.PHONY : data/CDirManager.o

# target to build an object file
data/CDirManager.cpp.o:
	cd /home/<USER>/EnergyStorage/AI_Box && $(MAKE) $(MAKESILENT) -f src/aibox/CMakeFiles/AI_Box.dir/build.make src/aibox/CMakeFiles/AI_Box.dir/data/CDirManager.cpp.o
.PHONY : data/CDirManager.cpp.o

data/CDirManager.i: data/CDirManager.cpp.i
.PHONY : data/CDirManager.i

# target to preprocess a source file
data/CDirManager.cpp.i:
	cd /home/<USER>/EnergyStorage/AI_Box && $(MAKE) $(MAKESILENT) -f src/aibox/CMakeFiles/AI_Box.dir/build.make src/aibox/CMakeFiles/AI_Box.dir/data/CDirManager.cpp.i
.PHONY : data/CDirManager.cpp.i

data/CDirManager.s: data/CDirManager.cpp.s
.PHONY : data/CDirManager.s

# target to generate assembly for a file
data/CDirManager.cpp.s:
	cd /home/<USER>/EnergyStorage/AI_Box && $(MAKE) $(MAKESILENT) -f src/aibox/CMakeFiles/AI_Box.dir/build.make src/aibox/CMakeFiles/AI_Box.dir/data/CDirManager.cpp.s
.PHONY : data/CDirManager.cpp.s

data/CGlobal.o: data/CGlobal.cpp.o
.PHONY : data/CGlobal.o

# target to build an object file
data/CGlobal.cpp.o:
	cd /home/<USER>/EnergyStorage/AI_Box && $(MAKE) $(MAKESILENT) -f src/aibox/CMakeFiles/AI_Box.dir/build.make src/aibox/CMakeFiles/AI_Box.dir/data/CGlobal.cpp.o
.PHONY : data/CGlobal.cpp.o

data/CGlobal.i: data/CGlobal.cpp.i
.PHONY : data/CGlobal.i

# target to preprocess a source file
data/CGlobal.cpp.i:
	cd /home/<USER>/EnergyStorage/AI_Box && $(MAKE) $(MAKESILENT) -f src/aibox/CMakeFiles/AI_Box.dir/build.make src/aibox/CMakeFiles/AI_Box.dir/data/CGlobal.cpp.i
.PHONY : data/CGlobal.cpp.i

data/CGlobal.s: data/CGlobal.cpp.s
.PHONY : data/CGlobal.s

# target to generate assembly for a file
data/CGlobal.cpp.s:
	cd /home/<USER>/EnergyStorage/AI_Box && $(MAKE) $(MAKESILENT) -f src/aibox/CMakeFiles/AI_Box.dir/build.make src/aibox/CMakeFiles/AI_Box.dir/data/CGlobal.cpp.s
.PHONY : data/CGlobal.cpp.s

data/CServerGlobal.o: data/CServerGlobal.cpp.o
.PHONY : data/CServerGlobal.o

# target to build an object file
data/CServerGlobal.cpp.o:
	cd /home/<USER>/EnergyStorage/AI_Box && $(MAKE) $(MAKESILENT) -f src/aibox/CMakeFiles/AI_Box.dir/build.make src/aibox/CMakeFiles/AI_Box.dir/data/CServerGlobal.cpp.o
.PHONY : data/CServerGlobal.cpp.o

data/CServerGlobal.i: data/CServerGlobal.cpp.i
.PHONY : data/CServerGlobal.i

# target to preprocess a source file
data/CServerGlobal.cpp.i:
	cd /home/<USER>/EnergyStorage/AI_Box && $(MAKE) $(MAKESILENT) -f src/aibox/CMakeFiles/AI_Box.dir/build.make src/aibox/CMakeFiles/AI_Box.dir/data/CServerGlobal.cpp.i
.PHONY : data/CServerGlobal.cpp.i

data/CServerGlobal.s: data/CServerGlobal.cpp.s
.PHONY : data/CServerGlobal.s

# target to generate assembly for a file
data/CServerGlobal.cpp.s:
	cd /home/<USER>/EnergyStorage/AI_Box && $(MAKE) $(MAKESILENT) -f src/aibox/CMakeFiles/AI_Box.dir/build.make src/aibox/CMakeFiles/AI_Box.dir/data/CServerGlobal.cpp.s
.PHONY : data/CServerGlobal.cpp.s

data/CUserInfo.o: data/CUserInfo.cpp.o
.PHONY : data/CUserInfo.o

# target to build an object file
data/CUserInfo.cpp.o:
	cd /home/<USER>/EnergyStorage/AI_Box && $(MAKE) $(MAKESILENT) -f src/aibox/CMakeFiles/AI_Box.dir/build.make src/aibox/CMakeFiles/AI_Box.dir/data/CUserInfo.cpp.o
.PHONY : data/CUserInfo.cpp.o

data/CUserInfo.i: data/CUserInfo.cpp.i
.PHONY : data/CUserInfo.i

# target to preprocess a source file
data/CUserInfo.cpp.i:
	cd /home/<USER>/EnergyStorage/AI_Box && $(MAKE) $(MAKESILENT) -f src/aibox/CMakeFiles/AI_Box.dir/build.make src/aibox/CMakeFiles/AI_Box.dir/data/CUserInfo.cpp.i
.PHONY : data/CUserInfo.cpp.i

data/CUserInfo.s: data/CUserInfo.cpp.s
.PHONY : data/CUserInfo.s

# target to generate assembly for a file
data/CUserInfo.cpp.s:
	cd /home/<USER>/EnergyStorage/AI_Box && $(MAKE) $(MAKESILENT) -f src/aibox/CMakeFiles/AI_Box.dir/build.make src/aibox/CMakeFiles/AI_Box.dir/data/CUserInfo.cpp.s
.PHONY : data/CUserInfo.cpp.s

http/CHandler.o: http/CHandler.cpp.o
.PHONY : http/CHandler.o

# target to build an object file
http/CHandler.cpp.o:
	cd /home/<USER>/EnergyStorage/AI_Box && $(MAKE) $(MAKESILENT) -f src/aibox/CMakeFiles/AI_Box.dir/build.make src/aibox/CMakeFiles/AI_Box.dir/http/CHandler.cpp.o
.PHONY : http/CHandler.cpp.o

http/CHandler.i: http/CHandler.cpp.i
.PHONY : http/CHandler.i

# target to preprocess a source file
http/CHandler.cpp.i:
	cd /home/<USER>/EnergyStorage/AI_Box && $(MAKE) $(MAKESILENT) -f src/aibox/CMakeFiles/AI_Box.dir/build.make src/aibox/CMakeFiles/AI_Box.dir/http/CHandler.cpp.i
.PHONY : http/CHandler.cpp.i

http/CHandler.s: http/CHandler.cpp.s
.PHONY : http/CHandler.s

# target to generate assembly for a file
http/CHandler.cpp.s:
	cd /home/<USER>/EnergyStorage/AI_Box && $(MAKE) $(MAKESILENT) -f src/aibox/CMakeFiles/AI_Box.dir/build.make src/aibox/CMakeFiles/AI_Box.dir/http/CHandler.cpp.s
.PHONY : http/CHandler.cpp.s

http/HttpServer.o: http/HttpServer.cpp.o
.PHONY : http/HttpServer.o

# target to build an object file
http/HttpServer.cpp.o:
	cd /home/<USER>/EnergyStorage/AI_Box && $(MAKE) $(MAKESILENT) -f src/aibox/CMakeFiles/AI_Box.dir/build.make src/aibox/CMakeFiles/AI_Box.dir/http/HttpServer.cpp.o
.PHONY : http/HttpServer.cpp.o

http/HttpServer.i: http/HttpServer.cpp.i
.PHONY : http/HttpServer.i

# target to preprocess a source file
http/HttpServer.cpp.i:
	cd /home/<USER>/EnergyStorage/AI_Box && $(MAKE) $(MAKESILENT) -f src/aibox/CMakeFiles/AI_Box.dir/build.make src/aibox/CMakeFiles/AI_Box.dir/http/HttpServer.cpp.i
.PHONY : http/HttpServer.cpp.i

http/HttpServer.s: http/HttpServer.cpp.s
.PHONY : http/HttpServer.s

# target to generate assembly for a file
http/HttpServer.cpp.s:
	cd /home/<USER>/EnergyStorage/AI_Box && $(MAKE) $(MAKESILENT) -f src/aibox/CMakeFiles/AI_Box.dir/build.make src/aibox/CMakeFiles/AI_Box.dir/http/HttpServer.cpp.s
.PHONY : http/HttpServer.cpp.s

http/handler/CAlarmLogHandlerPrivate.o: http/handler/CAlarmLogHandlerPrivate.cpp.o
.PHONY : http/handler/CAlarmLogHandlerPrivate.o

# target to build an object file
http/handler/CAlarmLogHandlerPrivate.cpp.o:
	cd /home/<USER>/EnergyStorage/AI_Box && $(MAKE) $(MAKESILENT) -f src/aibox/CMakeFiles/AI_Box.dir/build.make src/aibox/CMakeFiles/AI_Box.dir/http/handler/CAlarmLogHandlerPrivate.cpp.o
.PHONY : http/handler/CAlarmLogHandlerPrivate.cpp.o

http/handler/CAlarmLogHandlerPrivate.i: http/handler/CAlarmLogHandlerPrivate.cpp.i
.PHONY : http/handler/CAlarmLogHandlerPrivate.i

# target to preprocess a source file
http/handler/CAlarmLogHandlerPrivate.cpp.i:
	cd /home/<USER>/EnergyStorage/AI_Box && $(MAKE) $(MAKESILENT) -f src/aibox/CMakeFiles/AI_Box.dir/build.make src/aibox/CMakeFiles/AI_Box.dir/http/handler/CAlarmLogHandlerPrivate.cpp.i
.PHONY : http/handler/CAlarmLogHandlerPrivate.cpp.i

http/handler/CAlarmLogHandlerPrivate.s: http/handler/CAlarmLogHandlerPrivate.cpp.s
.PHONY : http/handler/CAlarmLogHandlerPrivate.s

# target to generate assembly for a file
http/handler/CAlarmLogHandlerPrivate.cpp.s:
	cd /home/<USER>/EnergyStorage/AI_Box && $(MAKE) $(MAKESILENT) -f src/aibox/CMakeFiles/AI_Box.dir/build.make src/aibox/CMakeFiles/AI_Box.dir/http/handler/CAlarmLogHandlerPrivate.cpp.s
.PHONY : http/handler/CAlarmLogHandlerPrivate.cpp.s

http/handler/CAlgorithmHandlerPrivate.o: http/handler/CAlgorithmHandlerPrivate.cpp.o
.PHONY : http/handler/CAlgorithmHandlerPrivate.o

# target to build an object file
http/handler/CAlgorithmHandlerPrivate.cpp.o:
	cd /home/<USER>/EnergyStorage/AI_Box && $(MAKE) $(MAKESILENT) -f src/aibox/CMakeFiles/AI_Box.dir/build.make src/aibox/CMakeFiles/AI_Box.dir/http/handler/CAlgorithmHandlerPrivate.cpp.o
.PHONY : http/handler/CAlgorithmHandlerPrivate.cpp.o

http/handler/CAlgorithmHandlerPrivate.i: http/handler/CAlgorithmHandlerPrivate.cpp.i
.PHONY : http/handler/CAlgorithmHandlerPrivate.i

# target to preprocess a source file
http/handler/CAlgorithmHandlerPrivate.cpp.i:
	cd /home/<USER>/EnergyStorage/AI_Box && $(MAKE) $(MAKESILENT) -f src/aibox/CMakeFiles/AI_Box.dir/build.make src/aibox/CMakeFiles/AI_Box.dir/http/handler/CAlgorithmHandlerPrivate.cpp.i
.PHONY : http/handler/CAlgorithmHandlerPrivate.cpp.i

http/handler/CAlgorithmHandlerPrivate.s: http/handler/CAlgorithmHandlerPrivate.cpp.s
.PHONY : http/handler/CAlgorithmHandlerPrivate.s

# target to generate assembly for a file
http/handler/CAlgorithmHandlerPrivate.cpp.s:
	cd /home/<USER>/EnergyStorage/AI_Box && $(MAKE) $(MAKESILENT) -f src/aibox/CMakeFiles/AI_Box.dir/build.make src/aibox/CMakeFiles/AI_Box.dir/http/handler/CAlgorithmHandlerPrivate.cpp.s
.PHONY : http/handler/CAlgorithmHandlerPrivate.cpp.s

http/handler/CBaseBankHandlerPrivate.o: http/handler/CBaseBankHandlerPrivate.cpp.o
.PHONY : http/handler/CBaseBankHandlerPrivate.o

# target to build an object file
http/handler/CBaseBankHandlerPrivate.cpp.o:
	cd /home/<USER>/EnergyStorage/AI_Box && $(MAKE) $(MAKESILENT) -f src/aibox/CMakeFiles/AI_Box.dir/build.make src/aibox/CMakeFiles/AI_Box.dir/http/handler/CBaseBankHandlerPrivate.cpp.o
.PHONY : http/handler/CBaseBankHandlerPrivate.cpp.o

http/handler/CBaseBankHandlerPrivate.i: http/handler/CBaseBankHandlerPrivate.cpp.i
.PHONY : http/handler/CBaseBankHandlerPrivate.i

# target to preprocess a source file
http/handler/CBaseBankHandlerPrivate.cpp.i:
	cd /home/<USER>/EnergyStorage/AI_Box && $(MAKE) $(MAKESILENT) -f src/aibox/CMakeFiles/AI_Box.dir/build.make src/aibox/CMakeFiles/AI_Box.dir/http/handler/CBaseBankHandlerPrivate.cpp.i
.PHONY : http/handler/CBaseBankHandlerPrivate.cpp.i

http/handler/CBaseBankHandlerPrivate.s: http/handler/CBaseBankHandlerPrivate.cpp.s
.PHONY : http/handler/CBaseBankHandlerPrivate.s

# target to generate assembly for a file
http/handler/CBaseBankHandlerPrivate.cpp.s:
	cd /home/<USER>/EnergyStorage/AI_Box && $(MAKE) $(MAKESILENT) -f src/aibox/CMakeFiles/AI_Box.dir/build.make src/aibox/CMakeFiles/AI_Box.dir/http/handler/CBaseBankHandlerPrivate.cpp.s
.PHONY : http/handler/CBaseBankHandlerPrivate.cpp.s

http/handler/CDefaultHandlerPrivate.o: http/handler/CDefaultHandlerPrivate.cpp.o
.PHONY : http/handler/CDefaultHandlerPrivate.o

# target to build an object file
http/handler/CDefaultHandlerPrivate.cpp.o:
	cd /home/<USER>/EnergyStorage/AI_Box && $(MAKE) $(MAKESILENT) -f src/aibox/CMakeFiles/AI_Box.dir/build.make src/aibox/CMakeFiles/AI_Box.dir/http/handler/CDefaultHandlerPrivate.cpp.o
.PHONY : http/handler/CDefaultHandlerPrivate.cpp.o

http/handler/CDefaultHandlerPrivate.i: http/handler/CDefaultHandlerPrivate.cpp.i
.PHONY : http/handler/CDefaultHandlerPrivate.i

# target to preprocess a source file
http/handler/CDefaultHandlerPrivate.cpp.i:
	cd /home/<USER>/EnergyStorage/AI_Box && $(MAKE) $(MAKESILENT) -f src/aibox/CMakeFiles/AI_Box.dir/build.make src/aibox/CMakeFiles/AI_Box.dir/http/handler/CDefaultHandlerPrivate.cpp.i
.PHONY : http/handler/CDefaultHandlerPrivate.cpp.i

http/handler/CDefaultHandlerPrivate.s: http/handler/CDefaultHandlerPrivate.cpp.s
.PHONY : http/handler/CDefaultHandlerPrivate.s

# target to generate assembly for a file
http/handler/CDefaultHandlerPrivate.cpp.s:
	cd /home/<USER>/EnergyStorage/AI_Box && $(MAKE) $(MAKESILENT) -f src/aibox/CMakeFiles/AI_Box.dir/build.make src/aibox/CMakeFiles/AI_Box.dir/http/handler/CDefaultHandlerPrivate.cpp.s
.PHONY : http/handler/CDefaultHandlerPrivate.cpp.s

http/handler/CFileHandlerPrivate.o: http/handler/CFileHandlerPrivate.cpp.o
.PHONY : http/handler/CFileHandlerPrivate.o

# target to build an object file
http/handler/CFileHandlerPrivate.cpp.o:
	cd /home/<USER>/EnergyStorage/AI_Box && $(MAKE) $(MAKESILENT) -f src/aibox/CMakeFiles/AI_Box.dir/build.make src/aibox/CMakeFiles/AI_Box.dir/http/handler/CFileHandlerPrivate.cpp.o
.PHONY : http/handler/CFileHandlerPrivate.cpp.o

http/handler/CFileHandlerPrivate.i: http/handler/CFileHandlerPrivate.cpp.i
.PHONY : http/handler/CFileHandlerPrivate.i

# target to preprocess a source file
http/handler/CFileHandlerPrivate.cpp.i:
	cd /home/<USER>/EnergyStorage/AI_Box && $(MAKE) $(MAKESILENT) -f src/aibox/CMakeFiles/AI_Box.dir/build.make src/aibox/CMakeFiles/AI_Box.dir/http/handler/CFileHandlerPrivate.cpp.i
.PHONY : http/handler/CFileHandlerPrivate.cpp.i

http/handler/CFileHandlerPrivate.s: http/handler/CFileHandlerPrivate.cpp.s
.PHONY : http/handler/CFileHandlerPrivate.s

# target to generate assembly for a file
http/handler/CFileHandlerPrivate.cpp.s:
	cd /home/<USER>/EnergyStorage/AI_Box && $(MAKE) $(MAKESILENT) -f src/aibox/CMakeFiles/AI_Box.dir/build.make src/aibox/CMakeFiles/AI_Box.dir/http/handler/CFileHandlerPrivate.cpp.s
.PHONY : http/handler/CFileHandlerPrivate.cpp.s

http/handler/CHttpTools.o: http/handler/CHttpTools.cpp.o
.PHONY : http/handler/CHttpTools.o

# target to build an object file
http/handler/CHttpTools.cpp.o:
	cd /home/<USER>/EnergyStorage/AI_Box && $(MAKE) $(MAKESILENT) -f src/aibox/CMakeFiles/AI_Box.dir/build.make src/aibox/CMakeFiles/AI_Box.dir/http/handler/CHttpTools.cpp.o
.PHONY : http/handler/CHttpTools.cpp.o

http/handler/CHttpTools.i: http/handler/CHttpTools.cpp.i
.PHONY : http/handler/CHttpTools.i

# target to preprocess a source file
http/handler/CHttpTools.cpp.i:
	cd /home/<USER>/EnergyStorage/AI_Box && $(MAKE) $(MAKESILENT) -f src/aibox/CMakeFiles/AI_Box.dir/build.make src/aibox/CMakeFiles/AI_Box.dir/http/handler/CHttpTools.cpp.i
.PHONY : http/handler/CHttpTools.cpp.i

http/handler/CHttpTools.s: http/handler/CHttpTools.cpp.s
.PHONY : http/handler/CHttpTools.s

# target to generate assembly for a file
http/handler/CHttpTools.cpp.s:
	cd /home/<USER>/EnergyStorage/AI_Box && $(MAKE) $(MAKESILENT) -f src/aibox/CMakeFiles/AI_Box.dir/build.make src/aibox/CMakeFiles/AI_Box.dir/http/handler/CHttpTools.cpp.s
.PHONY : http/handler/CHttpTools.cpp.s

http/handler/CInputSourceHandlerPrivate.o: http/handler/CInputSourceHandlerPrivate.cpp.o
.PHONY : http/handler/CInputSourceHandlerPrivate.o

# target to build an object file
http/handler/CInputSourceHandlerPrivate.cpp.o:
	cd /home/<USER>/EnergyStorage/AI_Box && $(MAKE) $(MAKESILENT) -f src/aibox/CMakeFiles/AI_Box.dir/build.make src/aibox/CMakeFiles/AI_Box.dir/http/handler/CInputSourceHandlerPrivate.cpp.o
.PHONY : http/handler/CInputSourceHandlerPrivate.cpp.o

http/handler/CInputSourceHandlerPrivate.i: http/handler/CInputSourceHandlerPrivate.cpp.i
.PHONY : http/handler/CInputSourceHandlerPrivate.i

# target to preprocess a source file
http/handler/CInputSourceHandlerPrivate.cpp.i:
	cd /home/<USER>/EnergyStorage/AI_Box && $(MAKE) $(MAKESILENT) -f src/aibox/CMakeFiles/AI_Box.dir/build.make src/aibox/CMakeFiles/AI_Box.dir/http/handler/CInputSourceHandlerPrivate.cpp.i
.PHONY : http/handler/CInputSourceHandlerPrivate.cpp.i

http/handler/CInputSourceHandlerPrivate.s: http/handler/CInputSourceHandlerPrivate.cpp.s
.PHONY : http/handler/CInputSourceHandlerPrivate.s

# target to generate assembly for a file
http/handler/CInputSourceHandlerPrivate.cpp.s:
	cd /home/<USER>/EnergyStorage/AI_Box && $(MAKE) $(MAKESILENT) -f src/aibox/CMakeFiles/AI_Box.dir/build.make src/aibox/CMakeFiles/AI_Box.dir/http/handler/CInputSourceHandlerPrivate.cpp.s
.PHONY : http/handler/CInputSourceHandlerPrivate.cpp.s

http/handler/COverlineHandlerPrivate.o: http/handler/COverlineHandlerPrivate.cpp.o
.PHONY : http/handler/COverlineHandlerPrivate.o

# target to build an object file
http/handler/COverlineHandlerPrivate.cpp.o:
	cd /home/<USER>/EnergyStorage/AI_Box && $(MAKE) $(MAKESILENT) -f src/aibox/CMakeFiles/AI_Box.dir/build.make src/aibox/CMakeFiles/AI_Box.dir/http/handler/COverlineHandlerPrivate.cpp.o
.PHONY : http/handler/COverlineHandlerPrivate.cpp.o

http/handler/COverlineHandlerPrivate.i: http/handler/COverlineHandlerPrivate.cpp.i
.PHONY : http/handler/COverlineHandlerPrivate.i

# target to preprocess a source file
http/handler/COverlineHandlerPrivate.cpp.i:
	cd /home/<USER>/EnergyStorage/AI_Box && $(MAKE) $(MAKESILENT) -f src/aibox/CMakeFiles/AI_Box.dir/build.make src/aibox/CMakeFiles/AI_Box.dir/http/handler/COverlineHandlerPrivate.cpp.i
.PHONY : http/handler/COverlineHandlerPrivate.cpp.i

http/handler/COverlineHandlerPrivate.s: http/handler/COverlineHandlerPrivate.cpp.s
.PHONY : http/handler/COverlineHandlerPrivate.s

# target to generate assembly for a file
http/handler/COverlineHandlerPrivate.cpp.s:
	cd /home/<USER>/EnergyStorage/AI_Box && $(MAKE) $(MAKESILENT) -f src/aibox/CMakeFiles/AI_Box.dir/build.make src/aibox/CMakeFiles/AI_Box.dir/http/handler/COverlineHandlerPrivate.cpp.s
.PHONY : http/handler/COverlineHandlerPrivate.cpp.s

http/handler/CSysHandlerPrivate.o: http/handler/CSysHandlerPrivate.cpp.o
.PHONY : http/handler/CSysHandlerPrivate.o

# target to build an object file
http/handler/CSysHandlerPrivate.cpp.o:
	cd /home/<USER>/EnergyStorage/AI_Box && $(MAKE) $(MAKESILENT) -f src/aibox/CMakeFiles/AI_Box.dir/build.make src/aibox/CMakeFiles/AI_Box.dir/http/handler/CSysHandlerPrivate.cpp.o
.PHONY : http/handler/CSysHandlerPrivate.cpp.o

http/handler/CSysHandlerPrivate.i: http/handler/CSysHandlerPrivate.cpp.i
.PHONY : http/handler/CSysHandlerPrivate.i

# target to preprocess a source file
http/handler/CSysHandlerPrivate.cpp.i:
	cd /home/<USER>/EnergyStorage/AI_Box && $(MAKE) $(MAKESILENT) -f src/aibox/CMakeFiles/AI_Box.dir/build.make src/aibox/CMakeFiles/AI_Box.dir/http/handler/CSysHandlerPrivate.cpp.i
.PHONY : http/handler/CSysHandlerPrivate.cpp.i

http/handler/CSysHandlerPrivate.s: http/handler/CSysHandlerPrivate.cpp.s
.PHONY : http/handler/CSysHandlerPrivate.s

# target to generate assembly for a file
http/handler/CSysHandlerPrivate.cpp.s:
	cd /home/<USER>/EnergyStorage/AI_Box && $(MAKE) $(MAKESILENT) -f src/aibox/CMakeFiles/AI_Box.dir/build.make src/aibox/CMakeFiles/AI_Box.dir/http/handler/CSysHandlerPrivate.cpp.s
.PHONY : http/handler/CSysHandlerPrivate.cpp.s

http/handler/CTools.o: http/handler/CTools.cpp.o
.PHONY : http/handler/CTools.o

# target to build an object file
http/handler/CTools.cpp.o:
	cd /home/<USER>/EnergyStorage/AI_Box && $(MAKE) $(MAKESILENT) -f src/aibox/CMakeFiles/AI_Box.dir/build.make src/aibox/CMakeFiles/AI_Box.dir/http/handler/CTools.cpp.o
.PHONY : http/handler/CTools.cpp.o

http/handler/CTools.i: http/handler/CTools.cpp.i
.PHONY : http/handler/CTools.i

# target to preprocess a source file
http/handler/CTools.cpp.i:
	cd /home/<USER>/EnergyStorage/AI_Box && $(MAKE) $(MAKESILENT) -f src/aibox/CMakeFiles/AI_Box.dir/build.make src/aibox/CMakeFiles/AI_Box.dir/http/handler/CTools.cpp.i
.PHONY : http/handler/CTools.cpp.i

http/handler/CTools.s: http/handler/CTools.cpp.s
.PHONY : http/handler/CTools.s

# target to generate assembly for a file
http/handler/CTools.cpp.s:
	cd /home/<USER>/EnergyStorage/AI_Box && $(MAKE) $(MAKESILENT) -f src/aibox/CMakeFiles/AI_Box.dir/build.make src/aibox/CMakeFiles/AI_Box.dir/http/handler/CTools.cpp.s
.PHONY : http/handler/CTools.cpp.s

http/handler/CUserHandlerPrivate.o: http/handler/CUserHandlerPrivate.cpp.o
.PHONY : http/handler/CUserHandlerPrivate.o

# target to build an object file
http/handler/CUserHandlerPrivate.cpp.o:
	cd /home/<USER>/EnergyStorage/AI_Box && $(MAKE) $(MAKESILENT) -f src/aibox/CMakeFiles/AI_Box.dir/build.make src/aibox/CMakeFiles/AI_Box.dir/http/handler/CUserHandlerPrivate.cpp.o
.PHONY : http/handler/CUserHandlerPrivate.cpp.o

http/handler/CUserHandlerPrivate.i: http/handler/CUserHandlerPrivate.cpp.i
.PHONY : http/handler/CUserHandlerPrivate.i

# target to preprocess a source file
http/handler/CUserHandlerPrivate.cpp.i:
	cd /home/<USER>/EnergyStorage/AI_Box && $(MAKE) $(MAKESILENT) -f src/aibox/CMakeFiles/AI_Box.dir/build.make src/aibox/CMakeFiles/AI_Box.dir/http/handler/CUserHandlerPrivate.cpp.i
.PHONY : http/handler/CUserHandlerPrivate.cpp.i

http/handler/CUserHandlerPrivate.s: http/handler/CUserHandlerPrivate.cpp.s
.PHONY : http/handler/CUserHandlerPrivate.s

# target to generate assembly for a file
http/handler/CUserHandlerPrivate.cpp.s:
	cd /home/<USER>/EnergyStorage/AI_Box && $(MAKE) $(MAKESILENT) -f src/aibox/CMakeFiles/AI_Box.dir/build.make src/aibox/CMakeFiles/AI_Box.dir/http/handler/CUserHandlerPrivate.cpp.s
.PHONY : http/handler/CUserHandlerPrivate.cpp.s

main.o: main.cpp.o
.PHONY : main.o

# target to build an object file
main.cpp.o:
	cd /home/<USER>/EnergyStorage/AI_Box && $(MAKE) $(MAKESILENT) -f src/aibox/CMakeFiles/AI_Box.dir/build.make src/aibox/CMakeFiles/AI_Box.dir/main.cpp.o
.PHONY : main.cpp.o

main.i: main.cpp.i
.PHONY : main.i

# target to preprocess a source file
main.cpp.i:
	cd /home/<USER>/EnergyStorage/AI_Box && $(MAKE) $(MAKESILENT) -f src/aibox/CMakeFiles/AI_Box.dir/build.make src/aibox/CMakeFiles/AI_Box.dir/main.cpp.i
.PHONY : main.cpp.i

main.s: main.cpp.s
.PHONY : main.s

# target to generate assembly for a file
main.cpp.s:
	cd /home/<USER>/EnergyStorage/AI_Box && $(MAKE) $(MAKESILENT) -f src/aibox/CMakeFiles/AI_Box.dir/build.make src/aibox/CMakeFiles/AI_Box.dir/main.cpp.s
.PHONY : main.cpp.s

# Help Target
help:
	@echo "The following are some of the valid targets for this Makefile:"
	@echo "... all (the default if no target is provided)"
	@echo "... clean"
	@echo "... depend"
	@echo "... edit_cache"
	@echo "... install"
	@echo "... install/local"
	@echo "... install/strip"
	@echo "... list_install_components"
	@echo "... rebuild_cache"
	@echo "... valgrind_test"
	@echo "... AI_Box"
	@echo "... AIBoxListManager.o"
	@echo "... AIBoxListManager.i"
	@echo "... AIBoxListManager.s"
	@echo "... AttemperEngineSink.o"
	@echo "... AttemperEngineSink.i"
	@echo "... AttemperEngineSink.s"
	@echo "... EnergyStorageCabinManager.o"
	@echo "... EnergyStorageCabinManager.i"
	@echo "... EnergyStorageCabinManager.s"
	@echo "... RoomListManager.o"
	@echo "... RoomListManager.i"
	@echo "... RoomListManager.s"
	@echo "... ServiceUnits.o"
	@echo "... ServiceUnits.i"
	@echo "... ServiceUnits.s"
	@echo "... data/CDirManager.o"
	@echo "... data/CDirManager.i"
	@echo "... data/CDirManager.s"
	@echo "... data/CGlobal.o"
	@echo "... data/CGlobal.i"
	@echo "... data/CGlobal.s"
	@echo "... data/CServerGlobal.o"
	@echo "... data/CServerGlobal.i"
	@echo "... data/CServerGlobal.s"
	@echo "... data/CUserInfo.o"
	@echo "... data/CUserInfo.i"
	@echo "... data/CUserInfo.s"
	@echo "... http/CHandler.o"
	@echo "... http/CHandler.i"
	@echo "... http/CHandler.s"
	@echo "... http/HttpServer.o"
	@echo "... http/HttpServer.i"
	@echo "... http/HttpServer.s"
	@echo "... http/handler/CAlarmLogHandlerPrivate.o"
	@echo "... http/handler/CAlarmLogHandlerPrivate.i"
	@echo "... http/handler/CAlarmLogHandlerPrivate.s"
	@echo "... http/handler/CAlgorithmHandlerPrivate.o"
	@echo "... http/handler/CAlgorithmHandlerPrivate.i"
	@echo "... http/handler/CAlgorithmHandlerPrivate.s"
	@echo "... http/handler/CBaseBankHandlerPrivate.o"
	@echo "... http/handler/CBaseBankHandlerPrivate.i"
	@echo "... http/handler/CBaseBankHandlerPrivate.s"
	@echo "... http/handler/CDefaultHandlerPrivate.o"
	@echo "... http/handler/CDefaultHandlerPrivate.i"
	@echo "... http/handler/CDefaultHandlerPrivate.s"
	@echo "... http/handler/CFileHandlerPrivate.o"
	@echo "... http/handler/CFileHandlerPrivate.i"
	@echo "... http/handler/CFileHandlerPrivate.s"
	@echo "... http/handler/CHttpTools.o"
	@echo "... http/handler/CHttpTools.i"
	@echo "... http/handler/CHttpTools.s"
	@echo "... http/handler/CInputSourceHandlerPrivate.o"
	@echo "... http/handler/CInputSourceHandlerPrivate.i"
	@echo "... http/handler/CInputSourceHandlerPrivate.s"
	@echo "... http/handler/COverlineHandlerPrivate.o"
	@echo "... http/handler/COverlineHandlerPrivate.i"
	@echo "... http/handler/COverlineHandlerPrivate.s"
	@echo "... http/handler/CSysHandlerPrivate.o"
	@echo "... http/handler/CSysHandlerPrivate.i"
	@echo "... http/handler/CSysHandlerPrivate.s"
	@echo "... http/handler/CTools.o"
	@echo "... http/handler/CTools.i"
	@echo "... http/handler/CTools.s"
	@echo "... http/handler/CUserHandlerPrivate.o"
	@echo "... http/handler/CUserHandlerPrivate.i"
	@echo "... http/handler/CUserHandlerPrivate.s"
	@echo "... main.o"
	@echo "... main.i"
	@echo "... main.s"
.PHONY : help



#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	cd /home/<USER>/EnergyStorage/AI_Box && $(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system

