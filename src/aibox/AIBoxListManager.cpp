#include "AIBoxListManager.h"

namespace AiBox {
CAIBoxListManager::CAIBoxListManager(/* args */) {}

CAIBoxListManager::~CAIBoxListManager() {}

BoxPtr CAIBoxListManager::GetAIBoxInfo(uint32 nSocketId) {
    auto it = m_mBoxList.find(nSocketId);
    if (it != m_mBoxList.end()) {
        return it->second;
    }
    return nullptr;
}

void CAIBoxListManager::AddAIBoxInfo(BoxPtr pAIBoxInfo) {
    m_mBoxList[pAIBoxInfo->m_nSocketId] = pAIBoxInfo;
}

bool CAIBoxListManager::DelAIBoxInfo(uint32 nSocketId) {
    if (m_mBoxList.find(nSocketId) == m_mBoxList.end()) {
        return false;
    }

    m_mBoxList.erase(nSocketId);
    return true;
}

void CAIBoxListManager::FindCameraByCameraId(uint32 nCameraId, CameraDevInfoPtr &pCameraInfo) {
    for (auto &boxInfo : m_mBoxList) {
        for (auto &cameraInfo : boxInfo.second->m_vCameraList) {
            if (cameraInfo->m_nId == nCameraId) {
                pCameraInfo = cameraInfo;
                return;
            }
        }
    }
}

bool CAIBoxListManager::IsContainBox(uint32_t nBoxId) {
    for (auto &it : m_mBoxList)
    {
        if (it.second->m_nBoxId == nBoxId)
        {
            return true;
        }
    }
    return false;
}

int CAIBoxListManager::GetBoxCount()
{
    return m_mBoxList.size();
}

BoxPtr CAIBoxListManager::GetBoxByIndex(int nIndex)
{
    int count = 0;
    for (auto &it : m_mBoxList)
    {
        if (count == nIndex)
        {
            return it.second;
        }
        count++;
    }
    return nullptr;
}


    
} // namespace AiBox
