[CorrespondNet]
BindIP=127.0.0.1
Port=8610;

[LocalNet]
WinBindIP=0.0.0.0
LinuxBindIP=0.0.0.0;云服务私有IP
Port=8600;
Name=Logon
ConnectTime=5;

[Log]
LogDir=log
LogLevel=1	;关闭-0 TRACE = 1,DEBUG = 2,INFO = 3,WARN = 4,ERROR = 5,FATAL = 6,
LogFlags=3	;1-时间 2-等级 4-种类
LogType=3	;CONSOLE = 0x01,FILE = 0x02,DB = 0x04  =>  CONSOLE|FILE|DB
LogColor=14 14 14 5 8 1	; 14-白 5-紫 8-黄 1-红
LogFile=AI_Box.log|a|8|2048000

[DB]
UpdateFlags=0
Name=ai_box;
ConnectionInfo=127.0.0.1|3306|root|123456|ai_box;
AsyncThreads=1
SynchThreads=1

[HTTP]
Port=18850
PushHost=************
PushPort=18868

[DEFINE]
AlarmLogDate=2592000#报警日志30天