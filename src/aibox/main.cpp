#include "Define.h"
#include "IOContext.h"
#include "Log.h"
#include "ServiceUnits.h"
#include "http/HttpServer.h"
#include <csignal>
#include <iostream>


int main(int argc, char **argv) {
    using namespace LogComm;

    struct sigaction sa {};
    sa.sa_handler = SIG_IGN;
    sigemptyset(&sa.sa_mask);
    if (sigaction(SIGINT, &sa, nullptr) == -1) {
        perror("sigaction");
        return 1;
    }


    sLogMgr->Initialize("AI_Box.ini");

    LOG_INFO("server.logon", "Starting Logonserver");

    HttpServer httpServer;
    httpServer.startServer();
    std::shared_ptr<Net::IOContext> ioContext = std::make_shared<Net::IOContext>();

    SrvUnitsMgr->Start(ioContext.get());

    asio::io_context::work work(*ioContext);
    ioContext->run();

    return 0;
}