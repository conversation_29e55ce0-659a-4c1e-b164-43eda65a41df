﻿// #pragma once
#include <mutex>
#include <string>
// #include <QMap>
// #include <QDateTime>
// #include <QReadWriteLock>
#include <map>
#include "CAbstractServer.h"

namespace httplib {
	class Server;
}

class CUserInfo;

class CServerGlobal final : public CAbstractServer
{
public:
	CServerGlobal();
	virtual ~CServerGlobal();

public: //interface

	void createServer() override;
	httplib::Server* getServer() const override;
	void stopServer() override;
	void releaseServer() override;

	bool login(const std::string &name,
			   const std::string &pwd,
			   std::string &msg) override;

	bool modify(const std::string &username,
				const std::string &oldpwd,
				const std::string &newpwd,
				std::string &msg) override;

	bool reset_pwd(const std::string &root_pwd,
				   std::string &msg) override;

	void updateSession(const std::string &session) override;
	void clearSession() override;
	void updateSessionValid() override; //更新session有效期
	bool checkSession(const std::string &session)  override;
    const std::string getSession();
     bool sessionIsValid() ;//按时间检查session是否过期
	// QVariant getValue(const QString &key) override;
	// void setValue(const QString &key, const QVariant &value) override;

   
private:


	void readUserConfig();
	void saveUserConfig();

	std::string defaultPwd() const;

private:
	httplib::Server* m_pServer = nullptr;

	CUserInfo *m_pUser = nullptr;

	std::string      m_session;
	long        m_sessionDT;
	// std::map<std::string, QVariant> m_values;

	std::mutex   m_userInfoLock;
	std::mutex   m_sessionLock;
	std::mutex   m_sessionDTLock;
	std::mutex   m_valueLock;
};

