﻿#include "CUserInfo.h"

const static std::string NoneName("");
const static std::string AdminName(u8"管理员");
const static std::string CommonName(u8"普通用户");

const static std::string NoneId("");
const static std::string AdminId("Admin");
const static std::string CommonId("Common");

CUserInfo::CUserInfo()
{

}

CUserInfo::~CUserInfo()
{

}

bool CUserInfo::isAdmin() const
{
	return getPower() == PowerType::Admin;
}

bool CUserInfo::isCommon() const
{
	return getPower() == PowerType::Common;
}

bool CUserInfo::isValid() const
{
	if( (!getName().empty()) &&
		(!getPwd().empty()) &&
		checkValidPower(getPower()) )
		return true;

	return false;
}

void CUserInfo::reset()
{
	m_name.clear();
	m_pwd.clear();
	m_power = (int)PowerType::None;
}

void CUserInfo::clear()
{
	reset();
}

void CUserInfo::setInfo(const std::string& name, const std::string& pwd)
{
	m_name = name;
	m_pwd  = pwd;
	if(name == "admin")
		m_power = PowerType::Admin;
	else
		m_power = PowerType::Common;
}

std::string CUserInfo::getPowerId(int power)
{
	switch (power)
	{
	case Admin:
		return AdminId;
	case Common:
		return CommonId;
	default: break;
	}
	return NoneId;
}

std::string CUserInfo::getPowerName(int power)
{
	switch (power)
	{
	case Admin:
		return AdminName;
	case Common:
		return CommonName;
	default: break;
	}
	return NoneName;
}

int CUserInfo::powerId2Type(const std::string& id)
{
	if(id == AdminId) {
		return PowerType::Admin;
	}
	else if(id == CommonId) {
		return PowerType::Common;
	}
	return PowerType::None;
}

bool CUserInfo::checkValidPower(int power)
{
	if(power <= PowerType::Begin || power >= PowerType::End)
		return false;
	return true;
}
