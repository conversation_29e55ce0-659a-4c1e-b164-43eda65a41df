﻿#include <string>
// #include <QString>
// #include <QVariant>
// #include "CParam.h"

namespace httplib {
    class Server;
}

class CAbstractServer
{
public:
    CAbstractServer() = default;
    virtual ~CAbstractServer() = default;

    virtual void createServer() = 0;
    virtual httplib::Server* getServer() const = 0;
    virtual void stopServer() = 0;
    virtual void releaseServer() = 0;

    virtual bool login(const std::string &name,
                       const std::string &pwd,
                       std::string &msg) = 0;

    virtual bool modify(const std::string &username,
                        const std::string &oldpwd,
                        const std::string &newpwd,
                        std::string &msg) = 0;

    virtual bool reset_pwd(const std::string &root_pwd,
                           std::string &msg) = 0;

    //随机session
    virtual void updateSession(const std::string &session) = 0;
    virtual void clearSession() = 0;
    virtual void updateSessionValid() = 0;
    virtual bool checkSession(const std::string &session)  = 0;
    virtual bool sessionIsValid() = 0;//按时间检查session是否过期
    virtual const std::string getSession() = 0;

    // virtual void* getValue(const std::string &key) = 0;
    // virtual void setValue(const std::string &key, const void* &value) = 0;
};

