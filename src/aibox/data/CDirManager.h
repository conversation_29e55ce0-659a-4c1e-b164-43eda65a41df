﻿#ifndef _CDIRMANAGERH
#define _CDIRMANAGERH
#include <string>

class CDirManager
{
public:
    enum Type {
        BankPath,     //底库路径
        BankTmpPath,  //底库包上传临时路径
        AlgorithmPath,//算法路径
        AlgImportPath,//算法导入路径
        ConfigPath,   //配置信息
        AlarmPath,    //报警图片
        OverlinePath, //跨线计数图片
        VehiclePath,  //车辆识别路径
        OCRPath,      //光学字符识别路径
        DBPath,       //数据库路径
        UpgradePath,  //新版本路径
        TemplatePath, //模板路径
    };
    CDirManager();
    virtual ~CDirManager();

    std::string getPath(Type t) const;
    std::string getSubPath(Type t) const;

    std::string rootPath() const;

private:
    std::string _root_path;
};


#endif /* _CDIRMANAGERH */


