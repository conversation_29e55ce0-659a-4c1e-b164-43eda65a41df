﻿#ifndef CNOTIFYCHANGEDITEM_H
#define CNOTIFYCHANGEDITEM_H

// #include <QVariant>

#include "NotifyType.h"
// #include "IMessageItem.h"

class CNotifyAddSourceItem //: public IMessageItem
{
    // MEMBER(int,      SourceId)  //id
    // MEMBER(QString,  StreamUrl) //流地址
public:
    CNotifyAddSourceItem() //: IMessageItem(NotifyType::NotifyType_AddSource) 
    {
        //
    }

    virtual ~CNotifyAddSourceItem() {
        //
    }
};

class CNotifyModifySourceItem //: public IMessageItem
{
    // MEMBER(int,      SourceId)  //id
    // MEMBER(QString,  StreamUrl) //流地址
public:
    CNotifyModifySourceItem() //: IMessageItem(NotifyType::NotifyType_ModifySource) 
    {
        //
    }

    virtual ~CNotifyModifySourceItem() {
        //
    }
};

class CNotifyRemoveSourceItem //: public IMessageItem
{
    //MEMBER(int,      SourceId)  //id
public:
    CNotifyRemoveSourceItem() //: IMessageItem(NotifyType::NotifyType_RemoveSource) 
    {
        //
    }

    virtual ~CNotifyRemoveSourceItem() {
        //
    }
};


/// 算法发生变化
class CNotifyAlgorithmChangedItem// : public IMessageItem
{
public:
    CNotifyAlgorithmChangedItem() //: IMessageItem(NotifyType::NotifyType_AlgorithmChanged)
     {
        //
    }

    virtual ~CNotifyAlgorithmChangedItem() {
        //
    }
};

#endif // CNOTIFYCHANGEDITEM_H
