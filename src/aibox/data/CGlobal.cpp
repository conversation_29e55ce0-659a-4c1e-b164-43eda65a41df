﻿#include "CGlobal.h"
#include "CServerGlobal.h"
#include "CDirManager.h"
// #include "CPublisher.h"
// #include "CInsertRecord.h"
// #include <QProcess>

CGlobal::CGlobal()
{
    init();
}

CGlobal::~CGlobal()
{
    uninit();
}

CGlobal* CGlobal::getInstance()
{
    static CGlobal m;
    return &m;
}

void CGlobal::init()
{
    if(m_cache.find(Dir) == m_cache.end()) {
        std::shared_ptr<void> ptr = std::make_shared<CDirManager>();
        m_cache[Dir]=ptr;
        // m_cache.insert(std::pair(Dir, ptr));
    }

    if(m_cache.find(Server) == m_cache.end()) {
        std::shared_ptr<void> ptr = std::make_shared<CServerGlobal>();
        m_cache[Server]= ptr;
    }

    if(m_cache.find(Publisher) == m_cache.end()) {
        // std::shared_ptr<void> ptr = std::make_shared<CPublisher>();
        // m_cache.insert(Publisher, ptr);
    }

    // if(!m_cache.contains(InsertRecord)) {
    //     std::shared_ptr<void> ptr = std::make_shared<CInsertRecord>();
    //     m_cache.insert(InsertRecord, ptr);
    // }
}

void CGlobal::uninit()
{
    m_cache.clear();
}

bool CGlobal::contains(int key) const
{
    return m_cache.find(key) != m_cache.end() ;
}

void CGlobal::add(int key, VoidPtr ptr, bool replace)
{
    if(replace || !contains(key)) {
        m_cache[key] = ptr;
    }
}

VoidPtr CGlobal::get(int key) const
{
    return m_cache.find(key)->second;
}

std::string CGlobal::getHttpHost()
{
    static std::string http_host;
    // if(http_host.empty())
    // {
    //     FILE* pipe = popen("hostname -I", "r");
    //     if (!pipe) return "";
        
    //     char buffer[128];
    //     std::string result;
    //     while (fgets(buffer, sizeof(buffer), pipe) != nullptr) {
    //         result += buffer;
    //     }
    //     pclose(pipe);
        
    //     // 去除首尾空白字符
    //     result.erase(result.find_last_not_of(" \n\r\t") + 1);
        
    //     // 处理特殊字符
    //     size_t pos = result.find_first_of("/ ");
    //     if (pos != std::string::npos) {
    //         result = result.substr(0, pos);
    //     }
    
    //     http_host = result;
    // }
    http_host = "*************";
    return http_host;
}

