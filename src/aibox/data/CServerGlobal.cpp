﻿#include "CServerGlobal.h"
#include "httplib.h"

// #include <QReadLocker>
// #include <QWriteLocker>
// #include <QFile>
#include "../../db/DBExports.h"
#include "../../util/Timer.h"
#include "CUserInfo.h"
#include "http/handler/CTools.h"
// #include "CIniConf.h"
// #include "CTools.h"
#include <string>

static const std::string _name("username");
static const std::string _pwd("userpwd");
using namespace DB;

CServerGlobal::CServerGlobal() { m_pUser = new CUserInfo(); }

CServerGlobal::~CServerGlobal() { releaseServer(); }

void CServerGlobal::createServer() {
    if (m_pServer == nullptr)
        m_pServer = new httplib::Server;
}

httplib::Server *CServerGlobal::getServer() const { return m_pServer; }

void CServerGlobal::stopServer() {
    if (m_pServer && m_pServer->is_running())
        m_pServer->stop();
}

void CServerGlobal::releaseServer() {
    if (m_pServer) {
        stopServer();
        delete m_pServer;
        m_pServer = nullptr;
    }
}

bool CServerGlobal::login(const std::string &name, const std::string &pwd, std::string &msg) {
    if (name.empty()) {
        msg = std::string(u8"账户不能为空");
        return false;
    }

    if (pwd.empty()) {
        msg = std::string(u8"密码不能为空");
        return false;
    }

#if 1
    if (pwd.size() != 32) {
        msg = std::string(u8"密码长度 != 32");
        return false;
    }
#endif

    std::lock_guard<std::mutex> locker(m_userInfoLock);
    if (!m_pUser->isValid()) {
        readUserConfig();
    }

    std::cout << "login: " << m_pUser->getName() << ", " << m_pUser->getPwd() << std::endl;
    if (name == m_pUser->getName() && pwd == m_pUser->getPwd()) {
        msg = std::string(u8"登录成功");
        return true;
    }

    msg = std::string(u8"密码错误");
    return false;
}

bool CServerGlobal::modify(const std::string &username, const std::string &oldpwd, const std::string &newpwd,
                           std::string &msg) {
    if (username.empty()) {
        msg = std::string(u8"账户不能为空.");
        return false;
    }

    if (username != std::string("admin")) {
        msg = std::string(u8"账号目前只支持'admin'");
        return false;
    }

    if (oldpwd.empty()) {
        msg = std::string(u8"原始密码不能为空");
        return false;
    }

    if (oldpwd.size() != 32) {
        msg = std::string(u8"原始密码长度 != 32.");
        return false;
    }

    if (newpwd.empty()) {
        msg = std::string(u8"新密码不能为空");
        return false;
    }

    if (newpwd.size() != 32) {
        msg = std::string(u8"新密码长度  != 32.");
        return false;
    }

    std::lock_guard<std::mutex> lock(m_userInfoLock);
    // QReadLocker locker(&m_userInfoLock);
    // if(!m_pUser->isValid())
    //     readUserConfig();

    if (username == m_pUser->getName() && oldpwd == m_pUser->getPwd()) {
        msg = u8"修改成功";
        m_pUser->setInfo(m_pUser->getName(), newpwd);

        PreparedStatement *stmt = LogonDatabasePool.GetPreparedStatement(AI_BOX_MODIFY_PWD);
        stmt->SetString(0, newpwd);
        stmt->SetString(1, username);
        LogonDatabasePool.Query(stmt);
        return true;
    }

    msg = std::string(u8"原始账号密码错误.");
    return false;
}

bool CServerGlobal::reset_pwd(const std::string &root_pwd, std::string &msg) {
    // std::string roowpwd = CTools::Md5("twdz:twdz123456").toStdString();
    // if(root_pwd != roowpwd)
    // {
    //     msg = "reset failed.";
    //     return false;
    // }

    // msg = "reset success.";

    // QWriteLocker locker(&m_userInfoLock);
    // m_pUser->setInfo(m_pUser->getName(), defaultPwd().toStdString());
    // saveUserConfig();
    return true;
}

void CServerGlobal::updateSession(const std::string &session) {
    std::lock_guard<std::mutex> locker(m_sessionLock);
    m_session = session;
}

void CServerGlobal::clearSession() { updateSession(std::string()); }

void CServerGlobal::updateSessionValid() {
    std::lock_guard<std::mutex> locker(m_sessionDTLock);
    m_sessionDT = DB::getCurrentTimeSec();
}

bool CServerGlobal::sessionIsValid() {
    std::lock_guard<std::mutex> locker(m_sessionDTLock);
    int64 delta = DB::getCurrentTimeSec();
    if (delta > (m_sessionDT + 2 * 60 * 60)) // 有效时长2小时
    {
        return false;
    }
    return true;
}

void CServerGlobal::readUserConfig() {
    std::string name = "admin";
    std::string pwd = defaultPwd();

    // CIniConf conf("userinfo.ini");
    // if(conf.open()) {
    //     auto s1 = conf.get(QString::fromStdString( _name ), QString("admin")).toString();
    //     auto s2 = conf.get(QString::fromStdString( _pwd ), QString(defaultPwd())).toString();
    //     name = s1.toStdString();
    //     pwd  = s2.toStdString();
    // }

    // conf.close();

    m_pUser->setInfo(name, pwd);
}

void CServerGlobal::saveUserConfig() {
    // CIniConf conf("userinfo.ini");
    // if(conf.open()) {
    //     conf.set(QString::fromStdString( _name ), QString::fromStdString( m_pUser->getName() ));
    //     conf.set(QString::fromStdString( _pwd ), QString::fromStdString( m_pUser->getPwd() ));
    //     conf.sync();
    // }

    // conf.close();
}

std::string CServerGlobal::defaultPwd() const {
#if 1
    return CTools::Md5("twdz:123456");
#else
    return QByteArray("123456");
#endif
    return ("123456");
}

bool CServerGlobal::checkSession(const std::string &session) {
    if (session.empty()) {
        std::cout << "==>session is empty." << std::endl;
        return false;
    }
    // std::mutex mtx;
    std::lock_guard<std::mutex> locker(m_sessionLock);
    std::cout << "checkSession222222" << std::endl;
    bool b = (m_session == session);
    if (b && sessionIsValid()) {
        return true;
    }
    return false;
}

const std::string CServerGlobal::getSession() { return m_session; }

// QVariant CServerGlobal::getValue(const QString& key)
// {
//     QReadLocker locker(&m_valueLock);
//     return m_values.value(key, QVariant());
// }

// void CServerGlobal::setValue(const QString& key, const QVariant& value)
// {
//     QWriteLocker locker(&m_valueLock);
//     m_values[key] = value;
// }
