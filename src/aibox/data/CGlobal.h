﻿#ifndef _CGLOBALH
#define _CGLOBALH
#include <map>
#include <memory>

using VoidPtr = std::shared_ptr<void>;

#define XGlobal CGlobal::getInstance()


template<typename T, typename U>
std::shared_ptr<T> reinterpret_pointer_cast(const std::shared_ptr<U>& r) noexcept {
    auto p = reinterpret_cast<typename std::shared_ptr<T>::element_type*>(r.get());
    return std::shared_ptr<T>(r, p);
}

#define XGlobal_Get(CLASS, NAME, TYPE) \
    std::shared_ptr<CLASS> NAME = nullptr; \
    { \
        VoidPtr _ptr = XGlobal->get(CGlobal::TYPE); \
        NAME = reinterpret_pointer_cast<CLASS>(_ptr); \
    }


// #define XGlobal_Get(CLASS, NAME, TYPE) \
// std::shared_ptr<CLASS> NAME = nullptr; \
// { \
//     VoidPtr _ptr = XGlobal->get(CGlobal::TYPE); \
//     if (_ptr) { \
//         NAME = std::shared_ptr<CLASS>(static_cast<CLASS*>(_ptr.get())); \
//     } \
// }


class CGlobal final
{
public:
    enum Type {
        Dir = 1,
        Server,
        Publisher,
        InsertRecord,

        User = 1024,
    };

    static CGlobal *getInstance();

    bool contains(int key) const;
    void add(int key, VoidPtr ptr, bool replace = false);
    VoidPtr get(int key) const;

    static std::string getHttpHost();

public:
    CGlobal();
    ~CGlobal();

private:
    void init();
    void uninit();

private:
    std::map<int, VoidPtr> m_cache;
};

#endif /* _CGLOBALH */

// #include <QString>
// #include <QMap>

