﻿#include "CDirManager.h"
#include "http/handler/CTools.h"
CDirManager::CDirManager(){
    // const QString docPath = QStandardPaths::writableLocation(QStandardPaths::DocumentsLocation);
    // _root_path = QString("%1/%2/%3").arg(docPath)
    //         .arg(qApp->organizationName())
    //         .arg(qApp->applicationName());
    _root_path = "/aibox_img";
}

CDirManager::~CDirManager() {}

std::string CDirManager::getPath(CDirManager::Type t) const
{
    std::string path = rootPath() + "/" + getSubPath(t);
    CTools::checkPath(path, true);
    return path;
    return "";
}

std::string CDirManager::getSubPath(Type t) const
{
    std::string sub_path;
    switch (t)
    {
    case BankPath:
        sub_path = "res/bank";
        break;
    case BankTmpPath:
        sub_path = "res/bank-tmp";
        break;
    case AlgorithmPath:
        sub_path = "res/algorithm";
        break;
    case AlgImportPath:
        sub_path = "res/algorithm-import-tmp";
        break;
    case ConfigPath:
        sub_path = "config";
        break;
    case AlarmPath:
        sub_path = "ret/alarm";
        break;
    case OverlinePath:
        sub_path = "ret/overline";
        break;
    case VehiclePath:
        sub_path = "ret/Vehicle";
        break;
    case OCRPath:
        sub_path = "ret/OCR";
        break;
    case DBPath:
        sub_path = "db";
        break;
    case UpgradePath:
        sub_path = "upgrade";
        break;
    case TemplatePath:
        sub_path = "template";
        break;
    default:
        break;
    }
    return sub_path;
}

std::string CDirManager::rootPath() const
{
    return _root_path;
}
