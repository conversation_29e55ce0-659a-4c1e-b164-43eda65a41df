﻿#ifndef _CUSERINFOH
#define _CUSERINFOH

#include <string>

class CUserInfo final
{
public:
	enum PowerType {
		None = 0,
		Begin = None,

		Admin/*管理员*/,
		Common/*普通用户*/,

		End
	};
	CUserInfo();
	~CUserInfo();

	bool isAdmin() const;
	bool isCommon() const;

	//判断是否是有效的用户
	bool isValid() const;

	void reset();//重置属性 == 清空属性
	void clear();//清空属性

	void      setInfo(const std::string &name, const std::string &pwd);

	std::string   getName()      const { return m_name; }
	std::string   getPwd()       const { return m_pwd;  }
	int           getPower()     const { return m_power; }
	std::string   getPowerId()   const { return getPowerId(m_power); }
	std::string   getPowerName() const { return getPowerName(m_power); }

	//权限名称
	static std::string getPowerId(int power);
	static std::string getPowerName(int power);
	static int         powerId2Type(const std::string &id);

	//判断是否时有效的权限
	static bool    checkValidPower(int power);

protected:
	std::string    m_name;
	std::string    m_pwd;
	int            m_power = (int)PowerType::None;
};


#endif /* _CUSERINFOH */


