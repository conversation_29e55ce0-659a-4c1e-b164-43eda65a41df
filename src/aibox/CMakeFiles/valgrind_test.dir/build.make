# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.28

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Produce verbose output by default.
VERBOSE = 1

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/EnergyStorage/AI_Box

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/EnergyStorage/AI_Box

# Utility rule file for valgrind_test.

# Include any custom commands dependencies for this target.
include src/aibox/CMakeFiles/valgrind_test.dir/compiler_depend.make

# Include the progress variables for this target.
include src/aibox/CMakeFiles/valgrind_test.dir/progress.make

src/aibox/CMakeFiles/valgrind_test: src/aibox/AI_Box
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --blue --bold --progress-dir=/home/<USER>/EnergyStorage/AI_Box/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Running Valgrind memory check on AI_Box"
	cd /home/<USER>/EnergyStorage/AI_Box/bin && /usr/bin/valgrind --leak-check=full --track-origins=yes --error-exitcode=1 /home/<USER>/EnergyStorage/AI_Box/src/aibox/AI_Box

valgrind_test: src/aibox/CMakeFiles/valgrind_test
valgrind_test: src/aibox/CMakeFiles/valgrind_test.dir/build.make
.PHONY : valgrind_test

# Rule to build all files generated by this target.
src/aibox/CMakeFiles/valgrind_test.dir/build: valgrind_test
.PHONY : src/aibox/CMakeFiles/valgrind_test.dir/build

src/aibox/CMakeFiles/valgrind_test.dir/clean:
	cd /home/<USER>/EnergyStorage/AI_Box/src/aibox && $(CMAKE_COMMAND) -P CMakeFiles/valgrind_test.dir/cmake_clean.cmake
.PHONY : src/aibox/CMakeFiles/valgrind_test.dir/clean

src/aibox/CMakeFiles/valgrind_test.dir/depend:
	cd /home/<USER>/EnergyStorage/AI_Box && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /home/<USER>/EnergyStorage/AI_Box /home/<USER>/EnergyStorage/AI_Box/src/aibox /home/<USER>/EnergyStorage/AI_Box /home/<USER>/EnergyStorage/AI_Box/src/aibox /home/<USER>/EnergyStorage/AI_Box/src/aibox/CMakeFiles/valgrind_test.dir/DependInfo.cmake "--color=$(COLOR)"
.PHONY : src/aibox/CMakeFiles/valgrind_test.dir/depend

