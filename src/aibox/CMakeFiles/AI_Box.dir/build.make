# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.28

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Produce verbose output by default.
VERBOSE = 1

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/EnergyStorage/AI_Box

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/EnergyStorage/AI_Box

# Include any dependencies generated for this target.
include src/aibox/CMakeFiles/AI_Box.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include src/aibox/CMakeFiles/AI_Box.dir/compiler_depend.make

# Include the progress variables for this target.
include src/aibox/CMakeFiles/AI_Box.dir/progress.make

# Include the compile flags for this target's objects.
include src/aibox/CMakeFiles/AI_Box.dir/flags.make

src/aibox/CMakeFiles/AI_Box.dir/AIBoxListManager.cpp.o: src/aibox/CMakeFiles/AI_Box.dir/flags.make
src/aibox/CMakeFiles/AI_Box.dir/AIBoxListManager.cpp.o: src/aibox/AIBoxListManager.cpp
src/aibox/CMakeFiles/AI_Box.dir/AIBoxListManager.cpp.o: src/aibox/CMakeFiles/AI_Box.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/EnergyStorage/AI_Box/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building CXX object src/aibox/CMakeFiles/AI_Box.dir/AIBoxListManager.cpp.o"
	cd /home/<USER>/EnergyStorage/AI_Box/src/aibox && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT src/aibox/CMakeFiles/AI_Box.dir/AIBoxListManager.cpp.o -MF CMakeFiles/AI_Box.dir/AIBoxListManager.cpp.o.d -o CMakeFiles/AI_Box.dir/AIBoxListManager.cpp.o -c /home/<USER>/EnergyStorage/AI_Box/src/aibox/AIBoxListManager.cpp

src/aibox/CMakeFiles/AI_Box.dir/AIBoxListManager.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/AI_Box.dir/AIBoxListManager.cpp.i"
	cd /home/<USER>/EnergyStorage/AI_Box/src/aibox && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/EnergyStorage/AI_Box/src/aibox/AIBoxListManager.cpp > CMakeFiles/AI_Box.dir/AIBoxListManager.cpp.i

src/aibox/CMakeFiles/AI_Box.dir/AIBoxListManager.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/AI_Box.dir/AIBoxListManager.cpp.s"
	cd /home/<USER>/EnergyStorage/AI_Box/src/aibox && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/EnergyStorage/AI_Box/src/aibox/AIBoxListManager.cpp -o CMakeFiles/AI_Box.dir/AIBoxListManager.cpp.s

src/aibox/CMakeFiles/AI_Box.dir/AttemperEngineSink.cpp.o: src/aibox/CMakeFiles/AI_Box.dir/flags.make
src/aibox/CMakeFiles/AI_Box.dir/AttemperEngineSink.cpp.o: src/aibox/AttemperEngineSink.cpp
src/aibox/CMakeFiles/AI_Box.dir/AttemperEngineSink.cpp.o: src/aibox/CMakeFiles/AI_Box.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/EnergyStorage/AI_Box/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Building CXX object src/aibox/CMakeFiles/AI_Box.dir/AttemperEngineSink.cpp.o"
	cd /home/<USER>/EnergyStorage/AI_Box/src/aibox && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT src/aibox/CMakeFiles/AI_Box.dir/AttemperEngineSink.cpp.o -MF CMakeFiles/AI_Box.dir/AttemperEngineSink.cpp.o.d -o CMakeFiles/AI_Box.dir/AttemperEngineSink.cpp.o -c /home/<USER>/EnergyStorage/AI_Box/src/aibox/AttemperEngineSink.cpp

src/aibox/CMakeFiles/AI_Box.dir/AttemperEngineSink.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/AI_Box.dir/AttemperEngineSink.cpp.i"
	cd /home/<USER>/EnergyStorage/AI_Box/src/aibox && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/EnergyStorage/AI_Box/src/aibox/AttemperEngineSink.cpp > CMakeFiles/AI_Box.dir/AttemperEngineSink.cpp.i

src/aibox/CMakeFiles/AI_Box.dir/AttemperEngineSink.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/AI_Box.dir/AttemperEngineSink.cpp.s"
	cd /home/<USER>/EnergyStorage/AI_Box/src/aibox && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/EnergyStorage/AI_Box/src/aibox/AttemperEngineSink.cpp -o CMakeFiles/AI_Box.dir/AttemperEngineSink.cpp.s

src/aibox/CMakeFiles/AI_Box.dir/EnergyStorageCabinManager.cpp.o: src/aibox/CMakeFiles/AI_Box.dir/flags.make
src/aibox/CMakeFiles/AI_Box.dir/EnergyStorageCabinManager.cpp.o: src/aibox/EnergyStorageCabinManager.cpp
src/aibox/CMakeFiles/AI_Box.dir/EnergyStorageCabinManager.cpp.o: src/aibox/CMakeFiles/AI_Box.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/EnergyStorage/AI_Box/CMakeFiles --progress-num=$(CMAKE_PROGRESS_3) "Building CXX object src/aibox/CMakeFiles/AI_Box.dir/EnergyStorageCabinManager.cpp.o"
	cd /home/<USER>/EnergyStorage/AI_Box/src/aibox && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT src/aibox/CMakeFiles/AI_Box.dir/EnergyStorageCabinManager.cpp.o -MF CMakeFiles/AI_Box.dir/EnergyStorageCabinManager.cpp.o.d -o CMakeFiles/AI_Box.dir/EnergyStorageCabinManager.cpp.o -c /home/<USER>/EnergyStorage/AI_Box/src/aibox/EnergyStorageCabinManager.cpp

src/aibox/CMakeFiles/AI_Box.dir/EnergyStorageCabinManager.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/AI_Box.dir/EnergyStorageCabinManager.cpp.i"
	cd /home/<USER>/EnergyStorage/AI_Box/src/aibox && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/EnergyStorage/AI_Box/src/aibox/EnergyStorageCabinManager.cpp > CMakeFiles/AI_Box.dir/EnergyStorageCabinManager.cpp.i

src/aibox/CMakeFiles/AI_Box.dir/EnergyStorageCabinManager.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/AI_Box.dir/EnergyStorageCabinManager.cpp.s"
	cd /home/<USER>/EnergyStorage/AI_Box/src/aibox && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/EnergyStorage/AI_Box/src/aibox/EnergyStorageCabinManager.cpp -o CMakeFiles/AI_Box.dir/EnergyStorageCabinManager.cpp.s

src/aibox/CMakeFiles/AI_Box.dir/RoomListManager.cpp.o: src/aibox/CMakeFiles/AI_Box.dir/flags.make
src/aibox/CMakeFiles/AI_Box.dir/RoomListManager.cpp.o: src/aibox/RoomListManager.cpp
src/aibox/CMakeFiles/AI_Box.dir/RoomListManager.cpp.o: src/aibox/CMakeFiles/AI_Box.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/EnergyStorage/AI_Box/CMakeFiles --progress-num=$(CMAKE_PROGRESS_4) "Building CXX object src/aibox/CMakeFiles/AI_Box.dir/RoomListManager.cpp.o"
	cd /home/<USER>/EnergyStorage/AI_Box/src/aibox && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT src/aibox/CMakeFiles/AI_Box.dir/RoomListManager.cpp.o -MF CMakeFiles/AI_Box.dir/RoomListManager.cpp.o.d -o CMakeFiles/AI_Box.dir/RoomListManager.cpp.o -c /home/<USER>/EnergyStorage/AI_Box/src/aibox/RoomListManager.cpp

src/aibox/CMakeFiles/AI_Box.dir/RoomListManager.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/AI_Box.dir/RoomListManager.cpp.i"
	cd /home/<USER>/EnergyStorage/AI_Box/src/aibox && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/EnergyStorage/AI_Box/src/aibox/RoomListManager.cpp > CMakeFiles/AI_Box.dir/RoomListManager.cpp.i

src/aibox/CMakeFiles/AI_Box.dir/RoomListManager.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/AI_Box.dir/RoomListManager.cpp.s"
	cd /home/<USER>/EnergyStorage/AI_Box/src/aibox && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/EnergyStorage/AI_Box/src/aibox/RoomListManager.cpp -o CMakeFiles/AI_Box.dir/RoomListManager.cpp.s

src/aibox/CMakeFiles/AI_Box.dir/ServiceUnits.cpp.o: src/aibox/CMakeFiles/AI_Box.dir/flags.make
src/aibox/CMakeFiles/AI_Box.dir/ServiceUnits.cpp.o: src/aibox/ServiceUnits.cpp
src/aibox/CMakeFiles/AI_Box.dir/ServiceUnits.cpp.o: src/aibox/CMakeFiles/AI_Box.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/EnergyStorage/AI_Box/CMakeFiles --progress-num=$(CMAKE_PROGRESS_5) "Building CXX object src/aibox/CMakeFiles/AI_Box.dir/ServiceUnits.cpp.o"
	cd /home/<USER>/EnergyStorage/AI_Box/src/aibox && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT src/aibox/CMakeFiles/AI_Box.dir/ServiceUnits.cpp.o -MF CMakeFiles/AI_Box.dir/ServiceUnits.cpp.o.d -o CMakeFiles/AI_Box.dir/ServiceUnits.cpp.o -c /home/<USER>/EnergyStorage/AI_Box/src/aibox/ServiceUnits.cpp

src/aibox/CMakeFiles/AI_Box.dir/ServiceUnits.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/AI_Box.dir/ServiceUnits.cpp.i"
	cd /home/<USER>/EnergyStorage/AI_Box/src/aibox && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/EnergyStorage/AI_Box/src/aibox/ServiceUnits.cpp > CMakeFiles/AI_Box.dir/ServiceUnits.cpp.i

src/aibox/CMakeFiles/AI_Box.dir/ServiceUnits.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/AI_Box.dir/ServiceUnits.cpp.s"
	cd /home/<USER>/EnergyStorage/AI_Box/src/aibox && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/EnergyStorage/AI_Box/src/aibox/ServiceUnits.cpp -o CMakeFiles/AI_Box.dir/ServiceUnits.cpp.s

src/aibox/CMakeFiles/AI_Box.dir/main.cpp.o: src/aibox/CMakeFiles/AI_Box.dir/flags.make
src/aibox/CMakeFiles/AI_Box.dir/main.cpp.o: src/aibox/main.cpp
src/aibox/CMakeFiles/AI_Box.dir/main.cpp.o: src/aibox/CMakeFiles/AI_Box.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/EnergyStorage/AI_Box/CMakeFiles --progress-num=$(CMAKE_PROGRESS_6) "Building CXX object src/aibox/CMakeFiles/AI_Box.dir/main.cpp.o"
	cd /home/<USER>/EnergyStorage/AI_Box/src/aibox && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT src/aibox/CMakeFiles/AI_Box.dir/main.cpp.o -MF CMakeFiles/AI_Box.dir/main.cpp.o.d -o CMakeFiles/AI_Box.dir/main.cpp.o -c /home/<USER>/EnergyStorage/AI_Box/src/aibox/main.cpp

src/aibox/CMakeFiles/AI_Box.dir/main.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/AI_Box.dir/main.cpp.i"
	cd /home/<USER>/EnergyStorage/AI_Box/src/aibox && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/EnergyStorage/AI_Box/src/aibox/main.cpp > CMakeFiles/AI_Box.dir/main.cpp.i

src/aibox/CMakeFiles/AI_Box.dir/main.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/AI_Box.dir/main.cpp.s"
	cd /home/<USER>/EnergyStorage/AI_Box/src/aibox && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/EnergyStorage/AI_Box/src/aibox/main.cpp -o CMakeFiles/AI_Box.dir/main.cpp.s

src/aibox/CMakeFiles/AI_Box.dir/data/CDirManager.cpp.o: src/aibox/CMakeFiles/AI_Box.dir/flags.make
src/aibox/CMakeFiles/AI_Box.dir/data/CDirManager.cpp.o: src/aibox/data/CDirManager.cpp
src/aibox/CMakeFiles/AI_Box.dir/data/CDirManager.cpp.o: src/aibox/CMakeFiles/AI_Box.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/EnergyStorage/AI_Box/CMakeFiles --progress-num=$(CMAKE_PROGRESS_7) "Building CXX object src/aibox/CMakeFiles/AI_Box.dir/data/CDirManager.cpp.o"
	cd /home/<USER>/EnergyStorage/AI_Box/src/aibox && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT src/aibox/CMakeFiles/AI_Box.dir/data/CDirManager.cpp.o -MF CMakeFiles/AI_Box.dir/data/CDirManager.cpp.o.d -o CMakeFiles/AI_Box.dir/data/CDirManager.cpp.o -c /home/<USER>/EnergyStorage/AI_Box/src/aibox/data/CDirManager.cpp

src/aibox/CMakeFiles/AI_Box.dir/data/CDirManager.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/AI_Box.dir/data/CDirManager.cpp.i"
	cd /home/<USER>/EnergyStorage/AI_Box/src/aibox && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/EnergyStorage/AI_Box/src/aibox/data/CDirManager.cpp > CMakeFiles/AI_Box.dir/data/CDirManager.cpp.i

src/aibox/CMakeFiles/AI_Box.dir/data/CDirManager.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/AI_Box.dir/data/CDirManager.cpp.s"
	cd /home/<USER>/EnergyStorage/AI_Box/src/aibox && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/EnergyStorage/AI_Box/src/aibox/data/CDirManager.cpp -o CMakeFiles/AI_Box.dir/data/CDirManager.cpp.s

src/aibox/CMakeFiles/AI_Box.dir/data/CGlobal.cpp.o: src/aibox/CMakeFiles/AI_Box.dir/flags.make
src/aibox/CMakeFiles/AI_Box.dir/data/CGlobal.cpp.o: src/aibox/data/CGlobal.cpp
src/aibox/CMakeFiles/AI_Box.dir/data/CGlobal.cpp.o: src/aibox/CMakeFiles/AI_Box.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/EnergyStorage/AI_Box/CMakeFiles --progress-num=$(CMAKE_PROGRESS_8) "Building CXX object src/aibox/CMakeFiles/AI_Box.dir/data/CGlobal.cpp.o"
	cd /home/<USER>/EnergyStorage/AI_Box/src/aibox && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT src/aibox/CMakeFiles/AI_Box.dir/data/CGlobal.cpp.o -MF CMakeFiles/AI_Box.dir/data/CGlobal.cpp.o.d -o CMakeFiles/AI_Box.dir/data/CGlobal.cpp.o -c /home/<USER>/EnergyStorage/AI_Box/src/aibox/data/CGlobal.cpp

src/aibox/CMakeFiles/AI_Box.dir/data/CGlobal.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/AI_Box.dir/data/CGlobal.cpp.i"
	cd /home/<USER>/EnergyStorage/AI_Box/src/aibox && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/EnergyStorage/AI_Box/src/aibox/data/CGlobal.cpp > CMakeFiles/AI_Box.dir/data/CGlobal.cpp.i

src/aibox/CMakeFiles/AI_Box.dir/data/CGlobal.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/AI_Box.dir/data/CGlobal.cpp.s"
	cd /home/<USER>/EnergyStorage/AI_Box/src/aibox && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/EnergyStorage/AI_Box/src/aibox/data/CGlobal.cpp -o CMakeFiles/AI_Box.dir/data/CGlobal.cpp.s

src/aibox/CMakeFiles/AI_Box.dir/data/CServerGlobal.cpp.o: src/aibox/CMakeFiles/AI_Box.dir/flags.make
src/aibox/CMakeFiles/AI_Box.dir/data/CServerGlobal.cpp.o: src/aibox/data/CServerGlobal.cpp
src/aibox/CMakeFiles/AI_Box.dir/data/CServerGlobal.cpp.o: src/aibox/CMakeFiles/AI_Box.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/EnergyStorage/AI_Box/CMakeFiles --progress-num=$(CMAKE_PROGRESS_9) "Building CXX object src/aibox/CMakeFiles/AI_Box.dir/data/CServerGlobal.cpp.o"
	cd /home/<USER>/EnergyStorage/AI_Box/src/aibox && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT src/aibox/CMakeFiles/AI_Box.dir/data/CServerGlobal.cpp.o -MF CMakeFiles/AI_Box.dir/data/CServerGlobal.cpp.o.d -o CMakeFiles/AI_Box.dir/data/CServerGlobal.cpp.o -c /home/<USER>/EnergyStorage/AI_Box/src/aibox/data/CServerGlobal.cpp

src/aibox/CMakeFiles/AI_Box.dir/data/CServerGlobal.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/AI_Box.dir/data/CServerGlobal.cpp.i"
	cd /home/<USER>/EnergyStorage/AI_Box/src/aibox && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/EnergyStorage/AI_Box/src/aibox/data/CServerGlobal.cpp > CMakeFiles/AI_Box.dir/data/CServerGlobal.cpp.i

src/aibox/CMakeFiles/AI_Box.dir/data/CServerGlobal.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/AI_Box.dir/data/CServerGlobal.cpp.s"
	cd /home/<USER>/EnergyStorage/AI_Box/src/aibox && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/EnergyStorage/AI_Box/src/aibox/data/CServerGlobal.cpp -o CMakeFiles/AI_Box.dir/data/CServerGlobal.cpp.s

src/aibox/CMakeFiles/AI_Box.dir/data/CUserInfo.cpp.o: src/aibox/CMakeFiles/AI_Box.dir/flags.make
src/aibox/CMakeFiles/AI_Box.dir/data/CUserInfo.cpp.o: src/aibox/data/CUserInfo.cpp
src/aibox/CMakeFiles/AI_Box.dir/data/CUserInfo.cpp.o: src/aibox/CMakeFiles/AI_Box.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/EnergyStorage/AI_Box/CMakeFiles --progress-num=$(CMAKE_PROGRESS_10) "Building CXX object src/aibox/CMakeFiles/AI_Box.dir/data/CUserInfo.cpp.o"
	cd /home/<USER>/EnergyStorage/AI_Box/src/aibox && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT src/aibox/CMakeFiles/AI_Box.dir/data/CUserInfo.cpp.o -MF CMakeFiles/AI_Box.dir/data/CUserInfo.cpp.o.d -o CMakeFiles/AI_Box.dir/data/CUserInfo.cpp.o -c /home/<USER>/EnergyStorage/AI_Box/src/aibox/data/CUserInfo.cpp

src/aibox/CMakeFiles/AI_Box.dir/data/CUserInfo.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/AI_Box.dir/data/CUserInfo.cpp.i"
	cd /home/<USER>/EnergyStorage/AI_Box/src/aibox && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/EnergyStorage/AI_Box/src/aibox/data/CUserInfo.cpp > CMakeFiles/AI_Box.dir/data/CUserInfo.cpp.i

src/aibox/CMakeFiles/AI_Box.dir/data/CUserInfo.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/AI_Box.dir/data/CUserInfo.cpp.s"
	cd /home/<USER>/EnergyStorage/AI_Box/src/aibox && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/EnergyStorage/AI_Box/src/aibox/data/CUserInfo.cpp -o CMakeFiles/AI_Box.dir/data/CUserInfo.cpp.s

src/aibox/CMakeFiles/AI_Box.dir/http/CHandler.cpp.o: src/aibox/CMakeFiles/AI_Box.dir/flags.make
src/aibox/CMakeFiles/AI_Box.dir/http/CHandler.cpp.o: src/aibox/http/CHandler.cpp
src/aibox/CMakeFiles/AI_Box.dir/http/CHandler.cpp.o: src/aibox/CMakeFiles/AI_Box.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/EnergyStorage/AI_Box/CMakeFiles --progress-num=$(CMAKE_PROGRESS_11) "Building CXX object src/aibox/CMakeFiles/AI_Box.dir/http/CHandler.cpp.o"
	cd /home/<USER>/EnergyStorage/AI_Box/src/aibox && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT src/aibox/CMakeFiles/AI_Box.dir/http/CHandler.cpp.o -MF CMakeFiles/AI_Box.dir/http/CHandler.cpp.o.d -o CMakeFiles/AI_Box.dir/http/CHandler.cpp.o -c /home/<USER>/EnergyStorage/AI_Box/src/aibox/http/CHandler.cpp

src/aibox/CMakeFiles/AI_Box.dir/http/CHandler.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/AI_Box.dir/http/CHandler.cpp.i"
	cd /home/<USER>/EnergyStorage/AI_Box/src/aibox && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/EnergyStorage/AI_Box/src/aibox/http/CHandler.cpp > CMakeFiles/AI_Box.dir/http/CHandler.cpp.i

src/aibox/CMakeFiles/AI_Box.dir/http/CHandler.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/AI_Box.dir/http/CHandler.cpp.s"
	cd /home/<USER>/EnergyStorage/AI_Box/src/aibox && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/EnergyStorage/AI_Box/src/aibox/http/CHandler.cpp -o CMakeFiles/AI_Box.dir/http/CHandler.cpp.s

src/aibox/CMakeFiles/AI_Box.dir/http/HttpServer.cpp.o: src/aibox/CMakeFiles/AI_Box.dir/flags.make
src/aibox/CMakeFiles/AI_Box.dir/http/HttpServer.cpp.o: src/aibox/http/HttpServer.cpp
src/aibox/CMakeFiles/AI_Box.dir/http/HttpServer.cpp.o: src/aibox/CMakeFiles/AI_Box.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/EnergyStorage/AI_Box/CMakeFiles --progress-num=$(CMAKE_PROGRESS_12) "Building CXX object src/aibox/CMakeFiles/AI_Box.dir/http/HttpServer.cpp.o"
	cd /home/<USER>/EnergyStorage/AI_Box/src/aibox && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT src/aibox/CMakeFiles/AI_Box.dir/http/HttpServer.cpp.o -MF CMakeFiles/AI_Box.dir/http/HttpServer.cpp.o.d -o CMakeFiles/AI_Box.dir/http/HttpServer.cpp.o -c /home/<USER>/EnergyStorage/AI_Box/src/aibox/http/HttpServer.cpp

src/aibox/CMakeFiles/AI_Box.dir/http/HttpServer.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/AI_Box.dir/http/HttpServer.cpp.i"
	cd /home/<USER>/EnergyStorage/AI_Box/src/aibox && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/EnergyStorage/AI_Box/src/aibox/http/HttpServer.cpp > CMakeFiles/AI_Box.dir/http/HttpServer.cpp.i

src/aibox/CMakeFiles/AI_Box.dir/http/HttpServer.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/AI_Box.dir/http/HttpServer.cpp.s"
	cd /home/<USER>/EnergyStorage/AI_Box/src/aibox && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/EnergyStorage/AI_Box/src/aibox/http/HttpServer.cpp -o CMakeFiles/AI_Box.dir/http/HttpServer.cpp.s

src/aibox/CMakeFiles/AI_Box.dir/http/handler/CAlarmLogHandlerPrivate.cpp.o: src/aibox/CMakeFiles/AI_Box.dir/flags.make
src/aibox/CMakeFiles/AI_Box.dir/http/handler/CAlarmLogHandlerPrivate.cpp.o: src/aibox/http/handler/CAlarmLogHandlerPrivate.cpp
src/aibox/CMakeFiles/AI_Box.dir/http/handler/CAlarmLogHandlerPrivate.cpp.o: src/aibox/CMakeFiles/AI_Box.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/EnergyStorage/AI_Box/CMakeFiles --progress-num=$(CMAKE_PROGRESS_13) "Building CXX object src/aibox/CMakeFiles/AI_Box.dir/http/handler/CAlarmLogHandlerPrivate.cpp.o"
	cd /home/<USER>/EnergyStorage/AI_Box/src/aibox && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT src/aibox/CMakeFiles/AI_Box.dir/http/handler/CAlarmLogHandlerPrivate.cpp.o -MF CMakeFiles/AI_Box.dir/http/handler/CAlarmLogHandlerPrivate.cpp.o.d -o CMakeFiles/AI_Box.dir/http/handler/CAlarmLogHandlerPrivate.cpp.o -c /home/<USER>/EnergyStorage/AI_Box/src/aibox/http/handler/CAlarmLogHandlerPrivate.cpp

src/aibox/CMakeFiles/AI_Box.dir/http/handler/CAlarmLogHandlerPrivate.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/AI_Box.dir/http/handler/CAlarmLogHandlerPrivate.cpp.i"
	cd /home/<USER>/EnergyStorage/AI_Box/src/aibox && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/EnergyStorage/AI_Box/src/aibox/http/handler/CAlarmLogHandlerPrivate.cpp > CMakeFiles/AI_Box.dir/http/handler/CAlarmLogHandlerPrivate.cpp.i

src/aibox/CMakeFiles/AI_Box.dir/http/handler/CAlarmLogHandlerPrivate.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/AI_Box.dir/http/handler/CAlarmLogHandlerPrivate.cpp.s"
	cd /home/<USER>/EnergyStorage/AI_Box/src/aibox && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/EnergyStorage/AI_Box/src/aibox/http/handler/CAlarmLogHandlerPrivate.cpp -o CMakeFiles/AI_Box.dir/http/handler/CAlarmLogHandlerPrivate.cpp.s

src/aibox/CMakeFiles/AI_Box.dir/http/handler/CAlgorithmHandlerPrivate.cpp.o: src/aibox/CMakeFiles/AI_Box.dir/flags.make
src/aibox/CMakeFiles/AI_Box.dir/http/handler/CAlgorithmHandlerPrivate.cpp.o: src/aibox/http/handler/CAlgorithmHandlerPrivate.cpp
src/aibox/CMakeFiles/AI_Box.dir/http/handler/CAlgorithmHandlerPrivate.cpp.o: src/aibox/CMakeFiles/AI_Box.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/EnergyStorage/AI_Box/CMakeFiles --progress-num=$(CMAKE_PROGRESS_14) "Building CXX object src/aibox/CMakeFiles/AI_Box.dir/http/handler/CAlgorithmHandlerPrivate.cpp.o"
	cd /home/<USER>/EnergyStorage/AI_Box/src/aibox && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT src/aibox/CMakeFiles/AI_Box.dir/http/handler/CAlgorithmHandlerPrivate.cpp.o -MF CMakeFiles/AI_Box.dir/http/handler/CAlgorithmHandlerPrivate.cpp.o.d -o CMakeFiles/AI_Box.dir/http/handler/CAlgorithmHandlerPrivate.cpp.o -c /home/<USER>/EnergyStorage/AI_Box/src/aibox/http/handler/CAlgorithmHandlerPrivate.cpp

src/aibox/CMakeFiles/AI_Box.dir/http/handler/CAlgorithmHandlerPrivate.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/AI_Box.dir/http/handler/CAlgorithmHandlerPrivate.cpp.i"
	cd /home/<USER>/EnergyStorage/AI_Box/src/aibox && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/EnergyStorage/AI_Box/src/aibox/http/handler/CAlgorithmHandlerPrivate.cpp > CMakeFiles/AI_Box.dir/http/handler/CAlgorithmHandlerPrivate.cpp.i

src/aibox/CMakeFiles/AI_Box.dir/http/handler/CAlgorithmHandlerPrivate.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/AI_Box.dir/http/handler/CAlgorithmHandlerPrivate.cpp.s"
	cd /home/<USER>/EnergyStorage/AI_Box/src/aibox && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/EnergyStorage/AI_Box/src/aibox/http/handler/CAlgorithmHandlerPrivate.cpp -o CMakeFiles/AI_Box.dir/http/handler/CAlgorithmHandlerPrivate.cpp.s

src/aibox/CMakeFiles/AI_Box.dir/http/handler/CBaseBankHandlerPrivate.cpp.o: src/aibox/CMakeFiles/AI_Box.dir/flags.make
src/aibox/CMakeFiles/AI_Box.dir/http/handler/CBaseBankHandlerPrivate.cpp.o: src/aibox/http/handler/CBaseBankHandlerPrivate.cpp
src/aibox/CMakeFiles/AI_Box.dir/http/handler/CBaseBankHandlerPrivate.cpp.o: src/aibox/CMakeFiles/AI_Box.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/EnergyStorage/AI_Box/CMakeFiles --progress-num=$(CMAKE_PROGRESS_15) "Building CXX object src/aibox/CMakeFiles/AI_Box.dir/http/handler/CBaseBankHandlerPrivate.cpp.o"
	cd /home/<USER>/EnergyStorage/AI_Box/src/aibox && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT src/aibox/CMakeFiles/AI_Box.dir/http/handler/CBaseBankHandlerPrivate.cpp.o -MF CMakeFiles/AI_Box.dir/http/handler/CBaseBankHandlerPrivate.cpp.o.d -o CMakeFiles/AI_Box.dir/http/handler/CBaseBankHandlerPrivate.cpp.o -c /home/<USER>/EnergyStorage/AI_Box/src/aibox/http/handler/CBaseBankHandlerPrivate.cpp

src/aibox/CMakeFiles/AI_Box.dir/http/handler/CBaseBankHandlerPrivate.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/AI_Box.dir/http/handler/CBaseBankHandlerPrivate.cpp.i"
	cd /home/<USER>/EnergyStorage/AI_Box/src/aibox && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/EnergyStorage/AI_Box/src/aibox/http/handler/CBaseBankHandlerPrivate.cpp > CMakeFiles/AI_Box.dir/http/handler/CBaseBankHandlerPrivate.cpp.i

src/aibox/CMakeFiles/AI_Box.dir/http/handler/CBaseBankHandlerPrivate.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/AI_Box.dir/http/handler/CBaseBankHandlerPrivate.cpp.s"
	cd /home/<USER>/EnergyStorage/AI_Box/src/aibox && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/EnergyStorage/AI_Box/src/aibox/http/handler/CBaseBankHandlerPrivate.cpp -o CMakeFiles/AI_Box.dir/http/handler/CBaseBankHandlerPrivate.cpp.s

src/aibox/CMakeFiles/AI_Box.dir/http/handler/CDefaultHandlerPrivate.cpp.o: src/aibox/CMakeFiles/AI_Box.dir/flags.make
src/aibox/CMakeFiles/AI_Box.dir/http/handler/CDefaultHandlerPrivate.cpp.o: src/aibox/http/handler/CDefaultHandlerPrivate.cpp
src/aibox/CMakeFiles/AI_Box.dir/http/handler/CDefaultHandlerPrivate.cpp.o: src/aibox/CMakeFiles/AI_Box.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/EnergyStorage/AI_Box/CMakeFiles --progress-num=$(CMAKE_PROGRESS_16) "Building CXX object src/aibox/CMakeFiles/AI_Box.dir/http/handler/CDefaultHandlerPrivate.cpp.o"
	cd /home/<USER>/EnergyStorage/AI_Box/src/aibox && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT src/aibox/CMakeFiles/AI_Box.dir/http/handler/CDefaultHandlerPrivate.cpp.o -MF CMakeFiles/AI_Box.dir/http/handler/CDefaultHandlerPrivate.cpp.o.d -o CMakeFiles/AI_Box.dir/http/handler/CDefaultHandlerPrivate.cpp.o -c /home/<USER>/EnergyStorage/AI_Box/src/aibox/http/handler/CDefaultHandlerPrivate.cpp

src/aibox/CMakeFiles/AI_Box.dir/http/handler/CDefaultHandlerPrivate.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/AI_Box.dir/http/handler/CDefaultHandlerPrivate.cpp.i"
	cd /home/<USER>/EnergyStorage/AI_Box/src/aibox && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/EnergyStorage/AI_Box/src/aibox/http/handler/CDefaultHandlerPrivate.cpp > CMakeFiles/AI_Box.dir/http/handler/CDefaultHandlerPrivate.cpp.i

src/aibox/CMakeFiles/AI_Box.dir/http/handler/CDefaultHandlerPrivate.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/AI_Box.dir/http/handler/CDefaultHandlerPrivate.cpp.s"
	cd /home/<USER>/EnergyStorage/AI_Box/src/aibox && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/EnergyStorage/AI_Box/src/aibox/http/handler/CDefaultHandlerPrivate.cpp -o CMakeFiles/AI_Box.dir/http/handler/CDefaultHandlerPrivate.cpp.s

src/aibox/CMakeFiles/AI_Box.dir/http/handler/CFileHandlerPrivate.cpp.o: src/aibox/CMakeFiles/AI_Box.dir/flags.make
src/aibox/CMakeFiles/AI_Box.dir/http/handler/CFileHandlerPrivate.cpp.o: src/aibox/http/handler/CFileHandlerPrivate.cpp
src/aibox/CMakeFiles/AI_Box.dir/http/handler/CFileHandlerPrivate.cpp.o: src/aibox/CMakeFiles/AI_Box.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/EnergyStorage/AI_Box/CMakeFiles --progress-num=$(CMAKE_PROGRESS_17) "Building CXX object src/aibox/CMakeFiles/AI_Box.dir/http/handler/CFileHandlerPrivate.cpp.o"
	cd /home/<USER>/EnergyStorage/AI_Box/src/aibox && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT src/aibox/CMakeFiles/AI_Box.dir/http/handler/CFileHandlerPrivate.cpp.o -MF CMakeFiles/AI_Box.dir/http/handler/CFileHandlerPrivate.cpp.o.d -o CMakeFiles/AI_Box.dir/http/handler/CFileHandlerPrivate.cpp.o -c /home/<USER>/EnergyStorage/AI_Box/src/aibox/http/handler/CFileHandlerPrivate.cpp

src/aibox/CMakeFiles/AI_Box.dir/http/handler/CFileHandlerPrivate.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/AI_Box.dir/http/handler/CFileHandlerPrivate.cpp.i"
	cd /home/<USER>/EnergyStorage/AI_Box/src/aibox && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/EnergyStorage/AI_Box/src/aibox/http/handler/CFileHandlerPrivate.cpp > CMakeFiles/AI_Box.dir/http/handler/CFileHandlerPrivate.cpp.i

src/aibox/CMakeFiles/AI_Box.dir/http/handler/CFileHandlerPrivate.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/AI_Box.dir/http/handler/CFileHandlerPrivate.cpp.s"
	cd /home/<USER>/EnergyStorage/AI_Box/src/aibox && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/EnergyStorage/AI_Box/src/aibox/http/handler/CFileHandlerPrivate.cpp -o CMakeFiles/AI_Box.dir/http/handler/CFileHandlerPrivate.cpp.s

src/aibox/CMakeFiles/AI_Box.dir/http/handler/CHttpTools.cpp.o: src/aibox/CMakeFiles/AI_Box.dir/flags.make
src/aibox/CMakeFiles/AI_Box.dir/http/handler/CHttpTools.cpp.o: src/aibox/http/handler/CHttpTools.cpp
src/aibox/CMakeFiles/AI_Box.dir/http/handler/CHttpTools.cpp.o: src/aibox/CMakeFiles/AI_Box.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/EnergyStorage/AI_Box/CMakeFiles --progress-num=$(CMAKE_PROGRESS_18) "Building CXX object src/aibox/CMakeFiles/AI_Box.dir/http/handler/CHttpTools.cpp.o"
	cd /home/<USER>/EnergyStorage/AI_Box/src/aibox && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT src/aibox/CMakeFiles/AI_Box.dir/http/handler/CHttpTools.cpp.o -MF CMakeFiles/AI_Box.dir/http/handler/CHttpTools.cpp.o.d -o CMakeFiles/AI_Box.dir/http/handler/CHttpTools.cpp.o -c /home/<USER>/EnergyStorage/AI_Box/src/aibox/http/handler/CHttpTools.cpp

src/aibox/CMakeFiles/AI_Box.dir/http/handler/CHttpTools.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/AI_Box.dir/http/handler/CHttpTools.cpp.i"
	cd /home/<USER>/EnergyStorage/AI_Box/src/aibox && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/EnergyStorage/AI_Box/src/aibox/http/handler/CHttpTools.cpp > CMakeFiles/AI_Box.dir/http/handler/CHttpTools.cpp.i

src/aibox/CMakeFiles/AI_Box.dir/http/handler/CHttpTools.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/AI_Box.dir/http/handler/CHttpTools.cpp.s"
	cd /home/<USER>/EnergyStorage/AI_Box/src/aibox && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/EnergyStorage/AI_Box/src/aibox/http/handler/CHttpTools.cpp -o CMakeFiles/AI_Box.dir/http/handler/CHttpTools.cpp.s

src/aibox/CMakeFiles/AI_Box.dir/http/handler/CInputSourceHandlerPrivate.cpp.o: src/aibox/CMakeFiles/AI_Box.dir/flags.make
src/aibox/CMakeFiles/AI_Box.dir/http/handler/CInputSourceHandlerPrivate.cpp.o: src/aibox/http/handler/CInputSourceHandlerPrivate.cpp
src/aibox/CMakeFiles/AI_Box.dir/http/handler/CInputSourceHandlerPrivate.cpp.o: src/aibox/CMakeFiles/AI_Box.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/EnergyStorage/AI_Box/CMakeFiles --progress-num=$(CMAKE_PROGRESS_19) "Building CXX object src/aibox/CMakeFiles/AI_Box.dir/http/handler/CInputSourceHandlerPrivate.cpp.o"
	cd /home/<USER>/EnergyStorage/AI_Box/src/aibox && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT src/aibox/CMakeFiles/AI_Box.dir/http/handler/CInputSourceHandlerPrivate.cpp.o -MF CMakeFiles/AI_Box.dir/http/handler/CInputSourceHandlerPrivate.cpp.o.d -o CMakeFiles/AI_Box.dir/http/handler/CInputSourceHandlerPrivate.cpp.o -c /home/<USER>/EnergyStorage/AI_Box/src/aibox/http/handler/CInputSourceHandlerPrivate.cpp

src/aibox/CMakeFiles/AI_Box.dir/http/handler/CInputSourceHandlerPrivate.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/AI_Box.dir/http/handler/CInputSourceHandlerPrivate.cpp.i"
	cd /home/<USER>/EnergyStorage/AI_Box/src/aibox && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/EnergyStorage/AI_Box/src/aibox/http/handler/CInputSourceHandlerPrivate.cpp > CMakeFiles/AI_Box.dir/http/handler/CInputSourceHandlerPrivate.cpp.i

src/aibox/CMakeFiles/AI_Box.dir/http/handler/CInputSourceHandlerPrivate.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/AI_Box.dir/http/handler/CInputSourceHandlerPrivate.cpp.s"
	cd /home/<USER>/EnergyStorage/AI_Box/src/aibox && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/EnergyStorage/AI_Box/src/aibox/http/handler/CInputSourceHandlerPrivate.cpp -o CMakeFiles/AI_Box.dir/http/handler/CInputSourceHandlerPrivate.cpp.s

src/aibox/CMakeFiles/AI_Box.dir/http/handler/COverlineHandlerPrivate.cpp.o: src/aibox/CMakeFiles/AI_Box.dir/flags.make
src/aibox/CMakeFiles/AI_Box.dir/http/handler/COverlineHandlerPrivate.cpp.o: src/aibox/http/handler/COverlineHandlerPrivate.cpp
src/aibox/CMakeFiles/AI_Box.dir/http/handler/COverlineHandlerPrivate.cpp.o: src/aibox/CMakeFiles/AI_Box.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/EnergyStorage/AI_Box/CMakeFiles --progress-num=$(CMAKE_PROGRESS_20) "Building CXX object src/aibox/CMakeFiles/AI_Box.dir/http/handler/COverlineHandlerPrivate.cpp.o"
	cd /home/<USER>/EnergyStorage/AI_Box/src/aibox && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT src/aibox/CMakeFiles/AI_Box.dir/http/handler/COverlineHandlerPrivate.cpp.o -MF CMakeFiles/AI_Box.dir/http/handler/COverlineHandlerPrivate.cpp.o.d -o CMakeFiles/AI_Box.dir/http/handler/COverlineHandlerPrivate.cpp.o -c /home/<USER>/EnergyStorage/AI_Box/src/aibox/http/handler/COverlineHandlerPrivate.cpp

src/aibox/CMakeFiles/AI_Box.dir/http/handler/COverlineHandlerPrivate.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/AI_Box.dir/http/handler/COverlineHandlerPrivate.cpp.i"
	cd /home/<USER>/EnergyStorage/AI_Box/src/aibox && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/EnergyStorage/AI_Box/src/aibox/http/handler/COverlineHandlerPrivate.cpp > CMakeFiles/AI_Box.dir/http/handler/COverlineHandlerPrivate.cpp.i

src/aibox/CMakeFiles/AI_Box.dir/http/handler/COverlineHandlerPrivate.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/AI_Box.dir/http/handler/COverlineHandlerPrivate.cpp.s"
	cd /home/<USER>/EnergyStorage/AI_Box/src/aibox && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/EnergyStorage/AI_Box/src/aibox/http/handler/COverlineHandlerPrivate.cpp -o CMakeFiles/AI_Box.dir/http/handler/COverlineHandlerPrivate.cpp.s

src/aibox/CMakeFiles/AI_Box.dir/http/handler/CSysHandlerPrivate.cpp.o: src/aibox/CMakeFiles/AI_Box.dir/flags.make
src/aibox/CMakeFiles/AI_Box.dir/http/handler/CSysHandlerPrivate.cpp.o: src/aibox/http/handler/CSysHandlerPrivate.cpp
src/aibox/CMakeFiles/AI_Box.dir/http/handler/CSysHandlerPrivate.cpp.o: src/aibox/CMakeFiles/AI_Box.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/EnergyStorage/AI_Box/CMakeFiles --progress-num=$(CMAKE_PROGRESS_21) "Building CXX object src/aibox/CMakeFiles/AI_Box.dir/http/handler/CSysHandlerPrivate.cpp.o"
	cd /home/<USER>/EnergyStorage/AI_Box/src/aibox && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT src/aibox/CMakeFiles/AI_Box.dir/http/handler/CSysHandlerPrivate.cpp.o -MF CMakeFiles/AI_Box.dir/http/handler/CSysHandlerPrivate.cpp.o.d -o CMakeFiles/AI_Box.dir/http/handler/CSysHandlerPrivate.cpp.o -c /home/<USER>/EnergyStorage/AI_Box/src/aibox/http/handler/CSysHandlerPrivate.cpp

src/aibox/CMakeFiles/AI_Box.dir/http/handler/CSysHandlerPrivate.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/AI_Box.dir/http/handler/CSysHandlerPrivate.cpp.i"
	cd /home/<USER>/EnergyStorage/AI_Box/src/aibox && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/EnergyStorage/AI_Box/src/aibox/http/handler/CSysHandlerPrivate.cpp > CMakeFiles/AI_Box.dir/http/handler/CSysHandlerPrivate.cpp.i

src/aibox/CMakeFiles/AI_Box.dir/http/handler/CSysHandlerPrivate.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/AI_Box.dir/http/handler/CSysHandlerPrivate.cpp.s"
	cd /home/<USER>/EnergyStorage/AI_Box/src/aibox && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/EnergyStorage/AI_Box/src/aibox/http/handler/CSysHandlerPrivate.cpp -o CMakeFiles/AI_Box.dir/http/handler/CSysHandlerPrivate.cpp.s

src/aibox/CMakeFiles/AI_Box.dir/http/handler/CTools.cpp.o: src/aibox/CMakeFiles/AI_Box.dir/flags.make
src/aibox/CMakeFiles/AI_Box.dir/http/handler/CTools.cpp.o: src/aibox/http/handler/CTools.cpp
src/aibox/CMakeFiles/AI_Box.dir/http/handler/CTools.cpp.o: src/aibox/CMakeFiles/AI_Box.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/EnergyStorage/AI_Box/CMakeFiles --progress-num=$(CMAKE_PROGRESS_22) "Building CXX object src/aibox/CMakeFiles/AI_Box.dir/http/handler/CTools.cpp.o"
	cd /home/<USER>/EnergyStorage/AI_Box/src/aibox && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT src/aibox/CMakeFiles/AI_Box.dir/http/handler/CTools.cpp.o -MF CMakeFiles/AI_Box.dir/http/handler/CTools.cpp.o.d -o CMakeFiles/AI_Box.dir/http/handler/CTools.cpp.o -c /home/<USER>/EnergyStorage/AI_Box/src/aibox/http/handler/CTools.cpp

src/aibox/CMakeFiles/AI_Box.dir/http/handler/CTools.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/AI_Box.dir/http/handler/CTools.cpp.i"
	cd /home/<USER>/EnergyStorage/AI_Box/src/aibox && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/EnergyStorage/AI_Box/src/aibox/http/handler/CTools.cpp > CMakeFiles/AI_Box.dir/http/handler/CTools.cpp.i

src/aibox/CMakeFiles/AI_Box.dir/http/handler/CTools.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/AI_Box.dir/http/handler/CTools.cpp.s"
	cd /home/<USER>/EnergyStorage/AI_Box/src/aibox && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/EnergyStorage/AI_Box/src/aibox/http/handler/CTools.cpp -o CMakeFiles/AI_Box.dir/http/handler/CTools.cpp.s

src/aibox/CMakeFiles/AI_Box.dir/http/handler/CUserHandlerPrivate.cpp.o: src/aibox/CMakeFiles/AI_Box.dir/flags.make
src/aibox/CMakeFiles/AI_Box.dir/http/handler/CUserHandlerPrivate.cpp.o: src/aibox/http/handler/CUserHandlerPrivate.cpp
src/aibox/CMakeFiles/AI_Box.dir/http/handler/CUserHandlerPrivate.cpp.o: src/aibox/CMakeFiles/AI_Box.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/EnergyStorage/AI_Box/CMakeFiles --progress-num=$(CMAKE_PROGRESS_23) "Building CXX object src/aibox/CMakeFiles/AI_Box.dir/http/handler/CUserHandlerPrivate.cpp.o"
	cd /home/<USER>/EnergyStorage/AI_Box/src/aibox && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT src/aibox/CMakeFiles/AI_Box.dir/http/handler/CUserHandlerPrivate.cpp.o -MF CMakeFiles/AI_Box.dir/http/handler/CUserHandlerPrivate.cpp.o.d -o CMakeFiles/AI_Box.dir/http/handler/CUserHandlerPrivate.cpp.o -c /home/<USER>/EnergyStorage/AI_Box/src/aibox/http/handler/CUserHandlerPrivate.cpp

src/aibox/CMakeFiles/AI_Box.dir/http/handler/CUserHandlerPrivate.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/AI_Box.dir/http/handler/CUserHandlerPrivate.cpp.i"
	cd /home/<USER>/EnergyStorage/AI_Box/src/aibox && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/EnergyStorage/AI_Box/src/aibox/http/handler/CUserHandlerPrivate.cpp > CMakeFiles/AI_Box.dir/http/handler/CUserHandlerPrivate.cpp.i

src/aibox/CMakeFiles/AI_Box.dir/http/handler/CUserHandlerPrivate.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/AI_Box.dir/http/handler/CUserHandlerPrivate.cpp.s"
	cd /home/<USER>/EnergyStorage/AI_Box/src/aibox && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/EnergyStorage/AI_Box/src/aibox/http/handler/CUserHandlerPrivate.cpp -o CMakeFiles/AI_Box.dir/http/handler/CUserHandlerPrivate.cpp.s

# Object files for target AI_Box
AI_Box_OBJECTS = \
"CMakeFiles/AI_Box.dir/AIBoxListManager.cpp.o" \
"CMakeFiles/AI_Box.dir/AttemperEngineSink.cpp.o" \
"CMakeFiles/AI_Box.dir/EnergyStorageCabinManager.cpp.o" \
"CMakeFiles/AI_Box.dir/RoomListManager.cpp.o" \
"CMakeFiles/AI_Box.dir/ServiceUnits.cpp.o" \
"CMakeFiles/AI_Box.dir/main.cpp.o" \
"CMakeFiles/AI_Box.dir/data/CDirManager.cpp.o" \
"CMakeFiles/AI_Box.dir/data/CGlobal.cpp.o" \
"CMakeFiles/AI_Box.dir/data/CServerGlobal.cpp.o" \
"CMakeFiles/AI_Box.dir/data/CUserInfo.cpp.o" \
"CMakeFiles/AI_Box.dir/http/CHandler.cpp.o" \
"CMakeFiles/AI_Box.dir/http/HttpServer.cpp.o" \
"CMakeFiles/AI_Box.dir/http/handler/CAlarmLogHandlerPrivate.cpp.o" \
"CMakeFiles/AI_Box.dir/http/handler/CAlgorithmHandlerPrivate.cpp.o" \
"CMakeFiles/AI_Box.dir/http/handler/CBaseBankHandlerPrivate.cpp.o" \
"CMakeFiles/AI_Box.dir/http/handler/CDefaultHandlerPrivate.cpp.o" \
"CMakeFiles/AI_Box.dir/http/handler/CFileHandlerPrivate.cpp.o" \
"CMakeFiles/AI_Box.dir/http/handler/CHttpTools.cpp.o" \
"CMakeFiles/AI_Box.dir/http/handler/CInputSourceHandlerPrivate.cpp.o" \
"CMakeFiles/AI_Box.dir/http/handler/COverlineHandlerPrivate.cpp.o" \
"CMakeFiles/AI_Box.dir/http/handler/CSysHandlerPrivate.cpp.o" \
"CMakeFiles/AI_Box.dir/http/handler/CTools.cpp.o" \
"CMakeFiles/AI_Box.dir/http/handler/CUserHandlerPrivate.cpp.o"

# External object files for target AI_Box
AI_Box_EXTERNAL_OBJECTS =

src/aibox/AI_Box: src/aibox/CMakeFiles/AI_Box.dir/AIBoxListManager.cpp.o
src/aibox/AI_Box: src/aibox/CMakeFiles/AI_Box.dir/AttemperEngineSink.cpp.o
src/aibox/AI_Box: src/aibox/CMakeFiles/AI_Box.dir/EnergyStorageCabinManager.cpp.o
src/aibox/AI_Box: src/aibox/CMakeFiles/AI_Box.dir/RoomListManager.cpp.o
src/aibox/AI_Box: src/aibox/CMakeFiles/AI_Box.dir/ServiceUnits.cpp.o
src/aibox/AI_Box: src/aibox/CMakeFiles/AI_Box.dir/main.cpp.o
src/aibox/AI_Box: src/aibox/CMakeFiles/AI_Box.dir/data/CDirManager.cpp.o
src/aibox/AI_Box: src/aibox/CMakeFiles/AI_Box.dir/data/CGlobal.cpp.o
src/aibox/AI_Box: src/aibox/CMakeFiles/AI_Box.dir/data/CServerGlobal.cpp.o
src/aibox/AI_Box: src/aibox/CMakeFiles/AI_Box.dir/data/CUserInfo.cpp.o
src/aibox/AI_Box: src/aibox/CMakeFiles/AI_Box.dir/http/CHandler.cpp.o
src/aibox/AI_Box: src/aibox/CMakeFiles/AI_Box.dir/http/HttpServer.cpp.o
src/aibox/AI_Box: src/aibox/CMakeFiles/AI_Box.dir/http/handler/CAlarmLogHandlerPrivate.cpp.o
src/aibox/AI_Box: src/aibox/CMakeFiles/AI_Box.dir/http/handler/CAlgorithmHandlerPrivate.cpp.o
src/aibox/AI_Box: src/aibox/CMakeFiles/AI_Box.dir/http/handler/CBaseBankHandlerPrivate.cpp.o
src/aibox/AI_Box: src/aibox/CMakeFiles/AI_Box.dir/http/handler/CDefaultHandlerPrivate.cpp.o
src/aibox/AI_Box: src/aibox/CMakeFiles/AI_Box.dir/http/handler/CFileHandlerPrivate.cpp.o
src/aibox/AI_Box: src/aibox/CMakeFiles/AI_Box.dir/http/handler/CHttpTools.cpp.o
src/aibox/AI_Box: src/aibox/CMakeFiles/AI_Box.dir/http/handler/CInputSourceHandlerPrivate.cpp.o
src/aibox/AI_Box: src/aibox/CMakeFiles/AI_Box.dir/http/handler/COverlineHandlerPrivate.cpp.o
src/aibox/AI_Box: src/aibox/CMakeFiles/AI_Box.dir/http/handler/CSysHandlerPrivate.cpp.o
src/aibox/AI_Box: src/aibox/CMakeFiles/AI_Box.dir/http/handler/CTools.cpp.o
src/aibox/AI_Box: src/aibox/CMakeFiles/AI_Box.dir/http/handler/CUserHandlerPrivate.cpp.o
src/aibox/AI_Box: src/aibox/CMakeFiles/AI_Box.dir/build.make
src/aibox/AI_Box: apilib/db/DataBase.so
src/aibox/AI_Box: apilib/net/Net.so
src/aibox/AI_Box: /usr/bin/libjsoncpp.a
src/aibox/AI_Box: /usr/local/lib/libboost_filesystem.so.1.88.0
src/aibox/AI_Box: /usr/lib/x86_64-linux-gnu/libmysqlclient.so
src/aibox/AI_Box: apilib/log/Log.so
src/aibox/AI_Box: apilib/util/Util.so
src/aibox/AI_Box: apilib/dep/fmt/fmt.a
src/aibox/AI_Box: /usr/lib/x86_64-linux-gnu/libssl.so
src/aibox/AI_Box: /usr/lib/x86_64-linux-gnu/libcrypto.so
src/aibox/AI_Box: /usr/local/lib/libboost_atomic.so.1.88.0
src/aibox/AI_Box: /usr/local/lib/libboost_system.so.1.88.0
src/aibox/AI_Box: src/aibox/CMakeFiles/AI_Box.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --bold --progress-dir=/home/<USER>/EnergyStorage/AI_Box/CMakeFiles --progress-num=$(CMAKE_PROGRESS_24) "Linking CXX executable AI_Box"
	cd /home/<USER>/EnergyStorage/AI_Box/src/aibox && $(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/AI_Box.dir/link.txt --verbose=$(VERBOSE)
	cd /home/<USER>/EnergyStorage/AI_Box/src/aibox && /usr/bin/cmake -E copy /home/<USER>/EnergyStorage/AI_Box/src/aibox/AI_Box /home/<USER>/EnergyStorage/AI_Box/bin/
	cd /home/<USER>/EnergyStorage/AI_Box/src/aibox && /usr/bin/cmake -E copy /home/<USER>/EnergyStorage/AI_Box/src/aibox/AI_Box.ini /home/<USER>/EnergyStorage/AI_Box/bin/

# Rule to build all files generated by this target.
src/aibox/CMakeFiles/AI_Box.dir/build: src/aibox/AI_Box
.PHONY : src/aibox/CMakeFiles/AI_Box.dir/build

src/aibox/CMakeFiles/AI_Box.dir/clean:
	cd /home/<USER>/EnergyStorage/AI_Box/src/aibox && $(CMAKE_COMMAND) -P CMakeFiles/AI_Box.dir/cmake_clean.cmake
.PHONY : src/aibox/CMakeFiles/AI_Box.dir/clean

src/aibox/CMakeFiles/AI_Box.dir/depend:
	cd /home/<USER>/EnergyStorage/AI_Box && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /home/<USER>/EnergyStorage/AI_Box /home/<USER>/EnergyStorage/AI_Box/src/aibox /home/<USER>/EnergyStorage/AI_Box /home/<USER>/EnergyStorage/AI_Box/src/aibox /home/<USER>/EnergyStorage/AI_Box/src/aibox/CMakeFiles/AI_Box.dir/DependInfo.cmake "--color=$(COLOR)"
.PHONY : src/aibox/CMakeFiles/AI_Box.dir/depend

