#ifndef _CMD_AIBOXH
#define _CMD_AIBOXH

#include "Define.h"

#define CABIN_TOTAL_CNT 45
#define CABIN_NAME_LEN 32
#define YITIJI_CNT_IN_CABIN 4
#define CABIN_NUMBER_LEN 32

#define MDM_CS_EDGEBOX 10     // BOX相关
#define SUB_CS_EDGEBOX_INFO 1 // BOX信息

#define MDM_CS_TEMP 11
#define SUB_CS_TEMP_WARN 1
#define SUB_CS_TEMP_INFO 2

#define MDM_CS_CLIENT 12                 // 客户端相关
#define SUB_CS_CLIENT_REGISTER 101       // 注册
#define SUB_SC_CLIENT_REGISTER_RESQ 102  // 注册应答
#define SUB_SC_CLIENT_WARN 103           // 温度报警
#define SUB_SC_CLIENT_TEMP 104           // 更新温度
#define SUB_CS_CLIENT_QUERY_MSG 105      // 查询信息
#define SUB_SC_CLIENT_DEV_STA 106        // 设备状态
#define SUB_SC_CLIENT_CABIN_STA 107      // 储能舱状态
#define SUB_SC_CLIENT_CABIN_STA_RESQ 108 // 储能舱状态返回
#define SUB_CS_CLIENT_DEV_OPEN 109       // 起瓶
#define SUB_CS_CLIENT_DEV_OPEN_RESQ 110  // 起瓶返回

#define MDM_BS_MAINBOARD 13                // 主板相关
#define SUB_BS_MAINBOARD_REGISTER 101      // 注册
#define SUB_SB_MAINBOARD_REGISTER_RESQ 102 // 注册应答
#define SUB_SB_MAINBOARD_DEV_STATUS 103    // 状态
#define SUB_SB_MAINBOARD_DEV_OPT 104       // 操作
#define SUB_SB_MAINBOARD_DEV_OPT_RESQ 105  // 操作应答
#define SUB_SB_MAINBOARD_DEV_ONLINE 106    // 上线
#define SUB_SB_MAINBOARD_DEV_ONLINE_RESQ 107 // 上线返回

enum ALARM_TYPE
{
    None = 0,
    ALARM_TYPE_PERSONNEL = 1,
    ALARM_TYPE_TEMP = 2,
    ALARM_TYPE_FIRE = 3,
};

enum YTJ_TYPE
{
    YI_TI_JI = 1,
    MEI_HUO_DAN = 2,
};

struct sTemp
{
    int cameraid;
    float temp;
    XPACK(O(cameraid, temp));
};

struct sTempwarn
{
    int cameraid;
    float hightemp;
    std::string imgpath;
    long long m_nTime;
    XPACK(O(cameraid, hightemp, imgpath, m_nTime));
};

struct CMD_CS_BoxRegister
{
    uint16 uBoxId; // 盒子ID
    XPACK(O(uBoxId));
};

#pragma pack(1)

struct CMD_CS_ClientRegister
{
    char cName[32];
    char cPWD[32];
    CMD_CS_ClientRegister()
    {
        memset(cName, 0, sizeof(cName));
        memset(cName, 0, sizeof(cName));
    }
};

struct CMD_SC_ClientRegisterResq
{
    uint32_t dwUserRight;
    CMD_SC_ClientRegisterResq()
    {

        dwUserRight = 0; // 0识别，1成功
    }
};

// 报警
struct CMD_SC_Client_Warn
{
    int nCameraid;      // 摄像头id
    float fHightemp;    // 高温
    long long llTime;   // 报警时间
    uint8_t uWarnType;  // 报警类型 1人员 2温度 3火焰
    char sImgpath[128]; // 图片路径
    CMD_SC_Client_Warn()
    {

        nCameraid = 0;
        fHightemp = 0.0;
        memset(sImgpath, 0, sizeof(sImgpath));
        llTime = 0;
        uWarnType = 0;
    }
};

// 温度
struct CMD_SC_Client_Temp
{
    int nCameraid;
    float fTemp;
    CMD_SC_Client_Temp()
    {

        nCameraid = 0;
        fTemp = 0.0;
    }
};

// 监控报警统计
struct CMD_SC_Alarm_Statistics
{
    uint16_t u16PersonCnt; // 人员检测
    uint16_t u16TemperCnt; // 温度检测
    uint16_t u16FireCnt;   // 火焰检测
};
struct CMD_SC_Dev_Sta
{
    uint16_t u16NormalCnt; // 正常
    uint16_t u16AlarmCnt;  // 报警
    uint16_t u16ErrlCnt;   // 故障
};
// 报警统计(C2S)上行数据
struct CMD_CS_Down_Dev_Sta // SUB_CS_CLIENT_QUERY_MSG  105
{
    int m_nCode;
};

struct CMD_SC_Alarm_Level
{
    uint16_t u16Level1Cnt; // 一级报警数量
    uint16_t u16Level2Cnt; // 二级报警数量
    uint16_t u16Level3Cnt; // 三级报警数量
};

// 报警统计(S2C)上行数据

struct CMD_SC_Dev_Statistics // SUB_SC_CLIENT_DEV_STA   106
{
    CMD_SC_Alarm_Statistics stWarnCnt;  // 监控报警统计
    CMD_SC_Dev_Sta stAiDev;             // 智能摄像头状态
    CMD_SC_Dev_Sta stBoxDev;            // 边缘计算盒状态
    CMD_SC_Dev_Sta stHuiYanDev;         // 慧眼状态
    CMD_SC_Alarm_Level stAlarmLevenCnt; // 报警等级数量
};

// 起瓶返回，上线
struct CMD_CS_Dev_Open
{
    uint8_t uCabinId; // 仓储ID
    uint8_t uTpye;    // 1一体机 2灭火弹
    uint8_t uDevId;   // 设备ID
    uint8_t uOptType; // 1开 2上线
};

//起瓶返回
struct CMD_CS_Dev_Open_Resq
{
    uint8_t uCabinId; // 仓储ID
    uint8_t uTpye;    // 1一体机 2灭火弹
    uint8_t uDevId;   // 设备ID
    uint8_t uOptType; // 1开 2上线
    uint8_t uSta;     // 0成功 1失败
};

enum CAMERA_STATUS
{
    CAMERA_STATUS_NORMAL = 0, // 正常
    CAMERA_STATUS_ALARM,      // 报警
    CAMERA_STATUS_ERROR,      // 故障
};

struct st_Cabin_Sta
{
    char m_8CabinName[CABIN_NAME_LEN];          // 机舱名   en_Cabin_Name_Len: 32
    char m_nCabinNum[CABIN_NUMBER_LEN];         // 机舱编号
    uint8_t m_u8CabinSta;                       // 机舱状态（0:在线，1：离线）
    uint8_t m_u8AlarmLevel;                     // 报警等级（1：一级报警，2：二级报警，3：三级报警）
    uint16_t m_u16MaxTemper;                    // 最高温度
    uint8_t m_u8YiTiJiSta[YITIJI_CNT_IN_CABIN]; // 0：在线，1：离线，2：空瓶，3：满瓶,4:故障   en_YiTiJi_Cnt_In_Cabin:  4
    uint8_t m_u8OutFireBomb;                    // 灭火弹状态 0：在线，1：离线，2：空瓶，3：满瓶,4:故障

    st_Cabin_Sta()
    {
        memset(this, 0, sizeof(st_Cabin_Sta));
    }
};

// 储能舱状态查询(S2C)上行行数据
struct CMD_SC_Energ_Cabin_Sta
{
    st_Cabin_Sta m_data[CABIN_TOTAL_CNT]; // en_Cabin_Total_Cnt :45
};
/*********************************主板协议************************************/

// 设备状态
struct Dev_Status
{
    uint8_t type; // 1一体机 2灭火弹
    uint8_t id;   // id
    uint8_t sta;  // 状态0正常，1故障，2 空
};

struct CMD_BS_Mainboard_Dev_Status
{
    uint8_t dev_cnt = 0;
    Dev_Status status[30]; // 状态
};

struct CMD_BS_Mainboard_Dev_Opt
{
    uint8_t type; // 1一体机 2灭火弹
    uint8_t id;   //
};

struct CMD_BS_Mainboard_Dev_Opt_Resq
{

    uint8_t type; // 1一体机 2灭火弹
    uint8_t id;   //
    uint8_t sta;  // 0成功 1失败
};

/*
struct Msg{
TCP_Head head; //head
CMD_BS_Mainboard_Register body; //data
TCP_CRC crc;    //crc校验
};
*/

// 上线
struct CMD_BS_Mainboard_Dev_Online
{
    uint8_t type; // 1一体机 2灭火弹
    uint8_t id;   //
};

// 上线返回
struct CMD_BS_Mainboard_Dev_Online_Resq
{
    uint8_t type; // 1一体机 2灭火弹
    uint8_t id;   //
    uint8_t sta;  // 0成功 1失败
};

#pragma pack()

#endif /* _CMD_AIBOXH */