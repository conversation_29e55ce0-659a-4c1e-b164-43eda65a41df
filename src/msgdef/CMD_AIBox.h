#ifndef _CMD_AIBOXH
#define _CMD_AIBOXH





#include "Define.h"

#define MDM_CS_EDGEBOX  			10									//BOX相关
#define SUB_CS_EDGEBOX_INFO		    1									//BOX信息


#define MDM_CS_TEMP                11  
#define SUB_CS_TEMP_WARN           1  
#define SUB_CS_TEMP_INFO           2

#define MDM_CS_CLIENT 12      // 客户端相关
#define SUB_CS_CLIENT_REGISTER 101 // 注册
#define SUB_SC_CLIENT_REGISTER_RESQ 102 //注册应答
#define SUB_SC_CLIENT_WARN 103 // 温度报警
#define SUB_SC_CLIENT_TEMP 104 // 更新温度

enum ALARM_TYPE {
    None = 0,
    ALARM_TYPE_PERSONNEL = 1,
    ALARM_TYPE_TEMP = 2,
    ALARM_TYPE_FIRE = 3,
};

struct sTemp{
    int cameraid;
    float temp;
    XPACK(O(cameraid,temp));
};



struct sTempwarn{
    int cameraid;
    float hightemp;
    std::string imgpath;
    long long  m_nTime;
    XPACK(O(cameraid,hightemp,imgpath,m_nTime));


};

struct CMD_CS_BoxRegister {
    uint16 uBoxId;//盒子ID
    XPACK(O(uBoxId));
};




#pragma pack(1)





struct CMD_CS_ClientRegister {
    char cName[32];
    char cPWD[32];
    CMD_CS_ClientRegister(){
        memset(cName, 0, sizeof(cName));
        memset(cName, 0, sizeof(cName));
    }
};

struct CMD_SC_ClientRegisterResq {
    uint32_t dwUserRight;
    CMD_SC_ClientRegisterResq() {
        
        dwUserRight = 0;//0识别，1成功 
    }
};



//报警
struct CMD_SC_Client_Warn {
    int nCameraid;        // 摄像头id
    float fHightemp;      // 高温
    long long llTime;   // 报警时间
    uint8_t uWarnType;  //报警类型 1人员 2温度 3火焰
    char sImgpath[128]; // 图片路径
    CMD_SC_Client_Warn() {

        nCameraid = 0;
        fHightemp = 0.0;
        memset(sImgpath, 0, sizeof(sImgpath));
        llTime = 0;
        uWarnType = 0;
    }
};

//温度
struct CMD_SC_Client_Temp {
    int nCameraid;
    float fTemp;
    CMD_SC_Client_Temp() {

        nCameraid = 0;
        fTemp = 0.0;
    }
};

#pragma pack()



#endif /* _CMD_AIBOXH */